<?php
/**
 * 火鸟门户系统采集插件独立定时任务脚本
 * 用于宝塔面板计划任务或独立cron调用
 * 
 * @version 1.0
 * <AUTHOR> Assistant
 * @date 2025-06-30
 * @usage php collection_cron_runner.php [node_id]
 */

// 设置字符集和错误报告
header('Content-Type: text/html; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置执行时间限制
set_time_limit(300); // 5分钟
ignore_user_abort(true);

// 引入系统核心文件
define('HUONIAOINC', dirname(__FILE__) . '/include');
require_once(HUONIAOINC . '/common.inc.php');

// 初始化数据库连接
$dsql = new dsql($dbo);

echo "🔥 火鸟门户系统采集插件定时任务执行器\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n";
echo "==========================================\n\n";

// 动态检测数据库表前缀
$table_prefix = '';
if (defined('DB_PREFIX')) {
    $table_prefix = DB_PREFIX;
} elseif (isset($GLOBALS['DB_PREFIX'])) {
    $table_prefix = $GLOBALS['DB_PREFIX'];
} else {
    // 尝试检测表前缀
    $test_tables = ['hn_site_plugins', 'huoniao_site_plugins'];
    foreach ($test_tables as $test_table) {
        $test_sql = $dsql->SetQuery("SHOW TABLES LIKE '$test_table'");
        $test_result = $dsql->dsqlOper($test_sql, "results");
        if ($test_result && count($test_result) > 0) {
            $table_prefix = str_replace('site_plugins', '', $test_table);
            break;
        }
    }
}

if (empty($table_prefix)) {
    die("❌ 错误: 无法检测数据库表前缀！\n");
}

echo "📋 检测到表前缀: {$table_prefix}\n";

// 动态查找采集插件ID
$plugin_sql = $dsql->SetQuery("SELECT * FROM `{$table_prefix}site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%' ORDER BY `pid` ASC");
$plugin_result = $dsql->dsqlOper($plugin_sql, "results");

if (!$plugin_result || count($plugin_result) == 0) {
    die("❌ 错误: 未找到采集插件！\n");
}

$collection_plugin = $plugin_result[0];
$plugin_id = $collection_plugin['pid'];
$plugin_title = $collection_plugin['title'];

echo "✅ 找到采集插件: {$plugin_title} (ID: {$plugin_id})\n";

// 检查插件目录是否存在
$plugin_dir = HUONIAOINC . "/plugins/{$plugin_id}/";
if (!is_dir($plugin_dir)) {
    die("❌ 错误: 插件目录不存在: {$plugin_dir}\n");
}

echo "📁 插件目录: {$plugin_dir}\n\n";

// 获取命令行参数 (指定节点ID)
$target_node_id = 0;
if (isset($argv[1]) && is_numeric($argv[1])) {
    $target_node_id = intval($argv[1]);
    echo "🎯 指定采集节点ID: {$target_node_id}\n";
}

// 查询需要执行的采集节点
$where_condition = "`status` = 1";
if ($target_node_id > 0) {
    $where_condition .= " AND `id` = {$target_node_id}";
} else {
    // 查询到期需要执行的节点
    $current_time = time();
    $where_condition .= " AND (`last_run_time` IS NULL OR `last_run_time` + `interval_time` <= {$current_time})";
}

$nodes_sql = $dsql->SetQuery("SELECT * FROM `{$table_prefix}collection_nodes` WHERE {$where_condition} ORDER BY `last_run_time` ASC LIMIT 5");
$nodes_result = $dsql->dsqlOper($nodes_sql, "results");

if (!$nodes_result || count($nodes_result) == 0) {
    echo "ℹ️  没有需要执行的采集节点\n";
    exit(0);
}

echo "📝 找到 " . count($nodes_result) . " 个需要执行的采集节点\n\n";

// 执行采集任务
$success_count = 0;
$error_count = 0;

foreach ($nodes_result as $node) {
    $node_id = $node['id'];
    $node_name = $node['name'];
    $target_type = $node['target_type'];
    
    echo "🚀 开始执行采集: {$node_name} (ID: {$node_id}, 类型: {$target_type})\n";
    
    try {
        // 更新最后执行时间
        $update_sql = "UPDATE `{$table_prefix}collection_nodes` SET `last_run_time` = " . time() . " WHERE `id` = {$node_id}";
        $dsql->dsqlOper($dsql->SetQuery($update_sql), "update");
        
        // 检查插件的cron.php文件是否存在
        $cron_file = $plugin_dir . "cron.php";
        if (file_exists($cron_file)) {
            // 设置当前采集节点ID (供cron.php使用)
            $_GET['node_id'] = $node_id;
            $_REQUEST['node_id'] = $node_id;
            
            // 包含并执行插件的cron脚本
            ob_start();
            include $cron_file;
            $output = ob_get_clean();
            
            echo "✅ 采集完成: {$node_name}\n";
            if (!empty($output)) {
                echo "📄 输出: " . trim($output) . "\n";
            }
            $success_count++;
            
        } else {
            // 如果没有cron.php，尝试调用其他采集文件
            $alt_files = ['getNews.php', 'crawler.php', 'collect.php'];
            $executed = false;
            
            foreach ($alt_files as $alt_file) {
                $alt_path = $plugin_dir . $alt_file;
                if (file_exists($alt_path)) {
                    $_GET['node_id'] = $node_id;
                    $_REQUEST['node_id'] = $node_id;
                    
                    ob_start();
                    include $alt_path;
                    $output = ob_get_clean();
                    
                    echo "✅ 采集完成: {$node_name} (使用 {$alt_file})\n";
                    if (!empty($output)) {
                        echo "📄 输出: " . trim($output) . "\n";
                    }
                    $executed = true;
                    $success_count++;
                    break;
                }
            }
            
            if (!$executed) {
                throw new Exception("未找到可执行的采集脚本");
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 采集失败: {$node_name} - {$e->getMessage()}\n";
        $error_count++;
    }
    
    echo "\n";
    
    // 避免过于频繁的请求
    sleep(2);
}

echo "==========================================\n";
echo "📊 定时任务执行完成统计:\n";
echo "✅ 成功执行: {$success_count} 个采集节点\n";
echo "❌ 执行失败: {$error_count} 个采集节点\n";
echo "🎯 总计处理: " . ($success_count + $error_count) . " 个采集节点\n";
echo "⏰ 完成时间: " . date('Y-m-d H:i:s') . "\n\n";

if ($success_count > 0) {
    echo "🎉 定时任务执行成功！\n";
    echo "📋 下次执行建议:\n";
    echo "- 全部节点: php " . __FILE__ . "\n";
    echo "- 指定节点: php " . __FILE__ . " [节点ID]\n";
    echo "- 宝塔计划任务: */30 * * * * cd " . dirname(__FILE__) . " && php " . __FILE__ . "\n";
}

echo "\n🔄 定时任务脚本执行完成！\n";
?>
