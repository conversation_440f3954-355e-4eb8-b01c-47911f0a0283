<?php
/**
 * 火鸟门户系统采集插件批量导入脚本
 * 基于官方文档和实际测试设计
 * 
 * @version 1.0
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */

// 引入系统核心文件
define('HUONIAOINC', dirname(__FILE__) . '/include');
require_once(HUONIAOINC . '/common.inc.php');

// 初始化数据库连接
$dsql = new dsql($dbo);

echo "🔥 火鸟门户系统采集插件批量导入工具\n";
echo "=====================================\n\n";

// 检查插件是否安装
$plugin_check = $dsql->SetQuery("SELECT * FROM `#@__site_plugins` WHERE `pid` = 4");
$plugin_result = $dsql->dsqlOper($plugin_check, "results");

if (!$plugin_result) {
    die("❌ 错误: 信息资讯采集插件(ID:4)未安装，请先安装插件！\n");
}

if ($plugin_result[0]['state'] != 1) {
    die("❌ 错误: 信息资讯采集插件未启用，请先启用插件！\n");
}

echo "✅ 插件检查通过: {$plugin_result[0]['title']}\n\n";

// 批量导入配置数据 (基于官方文档格式)
$collection_configs = [
    [
        'name' => '新浪新闻科技',
        'target_type' => 'html',
        'list_url' => 'https://tech.sina.com.cn/',
        'url_pattern' => 'https://tech.sina.com.cn/(*)',
        'url_include' => 'tech.sina.com.cn',
        'url_exclude' => '.jpg,.png,.gif',
        'start_mark' => '<div class="feed-card-item">',
        'end_mark' => '<div class="pagination">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h1>',
                'end_mark' => '</h1>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="article">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'author',
                'start_mark' => '<span class="author">',
                'end_mark' => '</span>',
                'required' => false
            ],
            [
                'field_type' => 'source',
                'start_mark' => '来源：',
                'end_mark' => '&nbsp;',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<span class="time">',
                'end_mark' => '</span>',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '网易新闻科技',
        'target_type' => 'html',
        'list_url' => 'https://tech.163.com/',
        'url_pattern' => 'https://tech.163.com/(*)',
        'url_include' => 'tech.163.com',
        'url_exclude' => '',
        'start_mark' => '<div class="n-list">',
        'end_mark' => '<div class="pagination">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h1 class="post_title">',
                'end_mark' => '</h1>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="post_body">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'author',
                'start_mark' => '<div class="ep-editor">',
                'end_mark' => '</div>',
                'required' => false
            ],
            [
                'field_type' => 'source',
                'start_mark' => '<div class="ep-source">',
                'end_mark' => '</div>',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<div class="post_time_source">',
                'end_mark' => '</div>',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '腾讯新闻科技',
        'target_type' => 'interface',
        'list_url' => 'https://news.qq.com/api/tech.js',
        'url_pattern' => 'https://news.qq.com/api/tech_(*).js',
        'url_include' => 'news.qq.com',
        'url_exclude' => '',
        'start_mark' => 'datacallback(',
        'end_mark' => ');',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '"title":"',
                'end_mark' => '",',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '"digest":"',
                'end_mark' => '",',
                'required' => true
            ],
            [
                'field_type' => 'source',
                'start_mark' => '"source":"',
                'end_mark' => '",',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '"time":"',
                'end_mark' => '",',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '搜狐新闻科技',
        'target_type' => 'multiple',
        'list_url' => 'https://it.sohu.com/',
        'url_pattern' => 'https://it.sohu.com/page_(*).html',
        'url_include' => 'it.sohu.com',
        'url_exclude' => '',
        'start_mark' => '<div class="news-item">',
        'end_mark' => '<div class="page-box">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h4><a',
                'end_mark' => '</a></h4>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="content">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'source',
                'start_mark' => '<span class="source">',
                'end_mark' => '</span>',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<span class="time">',
                'end_mark' => '</span>',
                'required' => false
            ]
        ]
    ]
];

// 执行批量导入
$success_count = 0;
$error_count = 0;

echo "🚀 开始批量导入采集配置...\n\n";

foreach ($collection_configs as $index => $config) {
    echo "📝 正在处理: {$config['name']} (" . ($index + 1) . "/" . count($collection_configs) . ")\n";
    
    try {
        // 开始事务
        $dsql->ExecuteNoneQuery("START TRANSACTION");
        
        // 检查是否已存在同名节点
        $check_sql = "SELECT id FROM `#@__collection_nodes` WHERE `name` = '{$config['name']}'";
        $check_result = $dsql->dsqlOper($dsql->SetQuery($check_sql), "results");
        
        if ($check_result) {
            echo "⚠️  节点已存在，跳过: {$config['name']}\n";
            $dsql->ExecuteNoneQuery("ROLLBACK");
            continue;
        }
        
        // 插入采集节点
        $node_sql = "INSERT INTO `#@__collection_nodes` 
                     (`name`, `target_type`, `list_url`, `url_pattern`, `url_include`, `url_exclude`, 
                      `start_mark`, `end_mark`, `encoding`, `status`, `created_time`) 
                     VALUES ('" . addslashes($config['name']) . "', '{$config['target_type']}', 
                            '" . addslashes($config['list_url']) . "', '" . addslashes($config['url_pattern']) . "', 
                            '" . addslashes($config['url_include']) . "', '" . addslashes($config['url_exclude']) . "',
                            '" . addslashes($config['start_mark']) . "', '" . addslashes($config['end_mark']) . "', 
                            '{$config['encoding']}', 1, " . time() . ")";
        
        $dsql->ExecuteNoneQuery($node_sql);
        $node_id = $dsql->GetLastID();
        
        if (!$node_id) {
            throw new Exception("插入采集节点失败");
        }
        
        // 插入采集规则
        $sort_order = 1;
        foreach ($config['rules'] as $rule) {
            $is_required = $rule['required'] ? 1 : 0;
            
            $rule_sql = "INSERT INTO `#@__collection_rules` 
                         (`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`) 
                         VALUES ($node_id, '{$rule['field_type']}', '" . addslashes($rule['start_mark']) . "', 
                                '" . addslashes($rule['end_mark']) . "', $is_required, $sort_order)";
            
            $dsql->ExecuteNoneQuery($rule_sql);
            $sort_order++;
        }
        
        // 提交事务
        $dsql->ExecuteNoneQuery("COMMIT");
        
        echo "✅ 成功导入: {$config['name']} (节点ID: $node_id, 规则数: " . count($config['rules']) . ")\n";
        $success_count++;
        
    } catch (Exception $e) {
        // 回滚事务
        $dsql->ExecuteNoneQuery("ROLLBACK");
        echo "❌ 导入失败: {$config['name']} - {$e->getMessage()}\n";
        $error_count++;
    }
    
    echo "\n";
}

echo "=====================================\n";
echo "📊 批量导入完成统计:\n";
echo "✅ 成功导入: $success_count 个采集节点\n";
echo "❌ 导入失败: $error_count 个采集节点\n";
echo "🎯 总计处理: " . ($success_count + $error_count) . " 个采集节点\n\n";

if ($success_count > 0) {
    echo "🎉 导入成功！您现在可以:\n";
    echo "1. 登录后台管理界面\n";
    echo "2. 进入 插件管理 → 火鸟采集\n";
    echo "3. 查看新增的采集节点\n";
    echo "4. 执行测试采集\n\n";
    
    echo "📋 验证SQL:\n";
    echo "SELECT * FROM `#@__collection_nodes` ORDER BY `id` DESC LIMIT 10;\n";
    echo "SELECT * FROM `#@__collection_rules` ORDER BY `id` DESC LIMIT 20;\n";
}

echo "\n🔄 脚本执行完成！\n";
?>
