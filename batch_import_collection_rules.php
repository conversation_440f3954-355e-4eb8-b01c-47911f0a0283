<?php
/**
 * 火鸟门户系统采集插件批量导入脚本
 * 基于官方文档和实际测试设计
 *
 * @version 1.1
 * <AUTHOR> Assistant
 * @date 2025-06-30
 * @update 根据用户建议优化：表前缀、插件ID、字符集等
 */

// 设置字符集和错误报告
header('Content-Type: text/html; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入系统核心文件
define('HUONIAOINC', dirname(__FILE__) . '/include');
require_once(HUONIAOINC . '/common.inc.php');

// 初始化数据库连接
$dsql = new dsql($dbo);

echo "🔥 火鸟门户系统采集插件批量导入工具\n";
echo "=====================================\n\n";

// 动态检测数据库表前缀
$table_prefix = '';
if (defined('DB_PREFIX')) {
    $table_prefix = DB_PREFIX;
} elseif (isset($GLOBALS['DB_PREFIX'])) {
    $table_prefix = $GLOBALS['DB_PREFIX'];
} else {
    // 尝试检测表前缀
    $test_tables = ['hn_site_plugins', 'huoniao_site_plugins'];
    foreach ($test_tables as $test_table) {
        $test_sql = $dsql->SetQuery("SHOW TABLES LIKE '$test_table'");
        $test_result = $dsql->dsqlOper($test_sql, "results");
        if ($test_result && count($test_result) > 0) {
            $table_prefix = str_replace('site_plugins', '', $test_table);
            break;
        }
    }
}

if (empty($table_prefix)) {
    die("❌ 错误: 无法检测数据库表前缀，请检查数据库配置！\n");
}

echo "📋 检测到表前缀: {$table_prefix}\n";

// 动态查找采集插件ID (不固定为4)
$plugin_sql = $dsql->SetQuery("SELECT * FROM `{$table_prefix}site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%' ORDER BY `pid` ASC");
$plugin_result = $dsql->dsqlOper($plugin_sql, "results");

if (!$plugin_result || count($plugin_result) == 0) {
    die("❌ 错误: 未找到采集插件，请先安装采集插件！\n");
}

// 选择第一个采集插件
$collection_plugin = $plugin_result[0];
$plugin_id = $collection_plugin['pid'];
$plugin_title = $collection_plugin['title'];
$plugin_state = $collection_plugin['state'];

if ($plugin_state != 1) {
    die("❌ 错误: 采集插件未启用，请先启用插件！插件: {$plugin_title} (ID: {$plugin_id})\n");
}

echo "✅ 插件检查通过: {$plugin_title} (ID: {$plugin_id})\n";
echo "📁 插件路径: /include/plugins/{$plugin_id}/\n\n";

// 批量导入配置数据 (基于官方文档格式)
$collection_configs = [
    [
        'name' => '新浪新闻科技',
        'target_type' => 'html',
        'list_url' => 'https://tech.sina.com.cn/',
        'url_pattern' => 'https://tech.sina.com.cn/(*)',
        'url_include' => 'tech.sina.com.cn',
        'url_exclude' => '.jpg,.png,.gif',
        'start_mark' => '<div class="feed-card-item">',
        'end_mark' => '<div class="pagination">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h1>',
                'end_mark' => '</h1>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="article">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'author',
                'start_mark' => '<span class="author">',
                'end_mark' => '</span>',
                'required' => false
            ],
            [
                'field_type' => 'source',
                'start_mark' => '来源：',
                'end_mark' => '&nbsp;',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<span class="time">',
                'end_mark' => '</span>',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '网易新闻科技',
        'target_type' => 'html',
        'list_url' => 'https://tech.163.com/',
        'url_pattern' => 'https://tech.163.com/(*)',
        'url_include' => 'tech.163.com',
        'url_exclude' => '',
        'start_mark' => '<div class="n-list">',
        'end_mark' => '<div class="pagination">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h1 class="post_title">',
                'end_mark' => '</h1>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="post_body">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'author',
                'start_mark' => '<div class="ep-editor">',
                'end_mark' => '</div>',
                'required' => false
            ],
            [
                'field_type' => 'source',
                'start_mark' => '<div class="ep-source">',
                'end_mark' => '</div>',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<div class="post_time_source">',
                'end_mark' => '</div>',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '腾讯新闻科技',
        'target_type' => 'interface',
        'list_url' => 'https://news.qq.com/api/tech.js',
        'url_pattern' => 'https://news.qq.com/api/tech_(*).js',
        'url_include' => 'news.qq.com',
        'url_exclude' => '',
        'start_mark' => 'datacallback(',
        'end_mark' => ');',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '"title":"',
                'end_mark' => '",',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '"digest":"',
                'end_mark' => '",',
                'required' => true
            ],
            [
                'field_type' => 'source',
                'start_mark' => '"source":"',
                'end_mark' => '",',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '"time":"',
                'end_mark' => '",',
                'required' => false
            ]
        ]
    ],
    [
        'name' => '搜狐新闻科技',
        'target_type' => 'multiple',
        'list_url' => 'https://it.sohu.com/',
        'url_pattern' => 'https://it.sohu.com/page_(*).html',
        'url_include' => 'it.sohu.com',
        'url_exclude' => '',
        'start_mark' => '<div class="news-item">',
        'end_mark' => '<div class="page-box">',
        'encoding' => 'utf-8',
        'rules' => [
            [
                'field_type' => 'title',
                'start_mark' => '<h4><a',
                'end_mark' => '</a></h4>',
                'required' => true
            ],
            [
                'field_type' => 'content',
                'start_mark' => '<div class="content">',
                'end_mark' => '</div>',
                'required' => true
            ],
            [
                'field_type' => 'source',
                'start_mark' => '<span class="source">',
                'end_mark' => '</span>',
                'required' => false
            ],
            [
                'field_type' => 'time',
                'start_mark' => '<span class="time">',
                'end_mark' => '</span>',
                'required' => false
            ]
        ]
    ]
];

// 执行批量导入
$success_count = 0;
$error_count = 0;

echo "🚀 开始批量导入采集配置...\n\n";

foreach ($collection_configs as $index => $config) {
    echo "📝 正在处理: {$config['name']} (" . ($index + 1) . "/" . count($collection_configs) . ")\n";
    
    try {
        // 开始事务
        $dsql->dsqlOper($dsql->SetQuery("START TRANSACTION"), "update");

        // 检查是否已存在同名节点
        $check_sql = "SELECT id FROM `{$table_prefix}collection_nodes` WHERE `name` = '" . addslashes($config['name']) . "'";
        $check_result = $dsql->dsqlOper($dsql->SetQuery($check_sql), "results");

        if ($check_result && count($check_result) > 0) {
            echo "⚠️  节点已存在，跳过: {$config['name']}\n";
            $dsql->dsqlOper($dsql->SetQuery("ROLLBACK"), "update");
            continue;
        }

        // 插入采集节点
        $node_sql = "INSERT INTO `{$table_prefix}collection_nodes`
                     (`name`, `target_type`, `list_url`, `url_pattern`, `url_include`, `url_exclude`,
                      `start_mark`, `end_mark`, `encoding`, `status`, `created_time`)
                     VALUES ('" . addslashes($config['name']) . "', '{$config['target_type']}',
                            '" . addslashes($config['list_url']) . "', '" . addslashes($config['url_pattern']) . "',
                            '" . addslashes($config['url_include']) . "', '" . addslashes($config['url_exclude']) . "',
                            '" . addslashes($config['start_mark']) . "', '" . addslashes($config['end_mark']) . "',
                            '{$config['encoding']}', 1, " . time() . ")";

        $node_result = $dsql->dsqlOper($dsql->SetQuery($node_sql), "update");

        if ($node_result != "ok") {
            throw new Exception("插入采集节点失败: " . $node_result);
        }

        // 获取插入的节点ID (使用PDO方式)
        global $dbo;
        $node_id = $dbo->lastInsertId();

        if (!$node_id) {
            throw new Exception("获取节点ID失败");
        }

        // 插入采集规则
        $sort_order = 1;
        foreach ($config['rules'] as $rule) {
            $is_required = $rule['required'] ? 1 : 0;

            $rule_sql = "INSERT INTO `{$table_prefix}collection_rules`
                         (`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`)
                         VALUES ($node_id, '{$rule['field_type']}', '" . addslashes($rule['start_mark']) . "',
                                '" . addslashes($rule['end_mark']) . "', $is_required, $sort_order)";

            $rule_result = $dsql->dsqlOper($dsql->SetQuery($rule_sql), "update");
            if ($rule_result != "ok") {
                throw new Exception("插入采集规则失败: " . $rule_result);
            }
            $sort_order++;
        }

        // 提交事务
        $dsql->dsqlOper($dsql->SetQuery("COMMIT"), "update");

        echo "✅ 成功导入: {$config['name']} (节点ID: $node_id, 规则数: " . count($config['rules']) . ")\n";
        $success_count++;

    } catch (Exception $e) {
        // 回滚事务
        $dsql->dsqlOper($dsql->SetQuery("ROLLBACK"), "update");
        echo "❌ 导入失败: {$config['name']} - {$e->getMessage()}\n";
        $error_count++;
    }
    
    echo "\n";
}

echo "=====================================\n";
echo "📊 批量导入完成统计:\n";
echo "✅ 成功导入: $success_count 个采集节点\n";
echo "❌ 导入失败: $error_count 个采集节点\n";
echo "🎯 总计处理: " . ($success_count + $error_count) . " 个采集节点\n\n";

if ($success_count > 0) {
    echo "🎉 导入成功！您现在可以:\n";
    echo "1. 登录后台管理界面\n";
    echo "2. 进入 插件管理 → {$plugin_title}\n";
    echo "3. 查看新增的采集节点\n";
    echo "4. 执行测试采集\n\n";

    echo "📋 验证SQL:\n";
    echo "SELECT * FROM `{$table_prefix}collection_nodes` ORDER BY `id` DESC LIMIT 10;\n";
    echo "SELECT * FROM `{$table_prefix}collection_rules` ORDER BY `id` DESC LIMIT 20;\n\n";

    echo "🔧 定时任务设置 (宝塔面板):\n";
    echo "任务类型: Shell脚本\n";
    echo "执行周期: */30 * * * * (每30分钟)\n";
    echo "脚本内容: cd " . dirname(__FILE__) . " && php /include/plugins/{$plugin_id}/cron.php\n\n";

    echo "📁 插件文件路径:\n";
    echo "- 插件目录: /include/plugins/{$plugin_id}/\n";
    echo "- 定时脚本: /include/plugins/{$plugin_id}/cron.php\n";
    echo "- 管理界面: /include/plugins/{$plugin_id}/index.php\n";
}

echo "\n🔄 脚本执行完成！\n";
?>
