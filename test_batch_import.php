<?php
/**
 * 火鸟门户系统采集插件批量导入脚本 - 测试版本
 * 独立运行，不依赖火鸟门户系统核心文件
 */

// 设置字符集和错误报告
header('Content-Type: text/html; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔥 火鸟门户系统采集插件批量导入工具 (测试版)\n";
echo "=====================================\n\n";

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'hawaiihub_test',
    'charset' => 'utf8mb4'
];

try {
    // 创建数据库连接
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
        ]
    );
    
    echo "✅ 数据库连接成功\n";
    
    // 动态检测表前缀
    $table_prefix = '';
    $test_tables = ['hn_site_plugins', 'huoniao_site_plugins'];
    
    foreach ($test_tables as $test_table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$test_table'");
        if ($stmt->rowCount() > 0) {
            $table_prefix = str_replace('site_plugins', '', $test_table);
            break;
        }
    }
    
    if (empty($table_prefix)) {
        throw new Exception("无法检测数据库表前缀");
    }
    
    echo "📋 检测到表前缀: {$table_prefix}\n";
    
    // 动态获取插件ID
    $plugin_sql = "SELECT pid FROM `{$table_prefix}site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%' LIMIT 1";
    $stmt = $pdo->query($plugin_sql);
    $plugin_data = $stmt->fetch();
    
    if (!$plugin_data) {
        throw new Exception("未找到采集插件，请先安装采集插件");
    }
    
    $plugin_id = $plugin_data['pid'];
    echo "🔌 检测到插件ID: {$plugin_id}\n";
    
    // 准备批量导入的采集节点配置
    $collection_configs = [
        [
            'name' => '新浪新闻测试',
            'target_type' => 'html',
            'list_url' => 'https://news.sina.com.cn/',
            'url_pattern' => 'https://news.sina.com.cn/(*)',
            'start_mark' => '<div class="news-list">',
            'end_mark' => '</div>',
            'rules' => [
                ['field_type' => 'title', 'start_mark' => '<h1>', 'end_mark' => '</h1>', 'is_required' => 1],
                ['field_type' => 'content', 'start_mark' => '<div class="article">', 'end_mark' => '</div>', 'is_required' => 1],
                ['field_type' => 'author', 'start_mark' => '<span class="author">', 'end_mark' => '</span>', 'is_required' => 0]
            ]
        ],
        [
            'name' => '网易科技测试',
            'target_type' => 'html',
            'list_url' => 'https://tech.163.com/',
            'url_pattern' => 'https://tech.163.com/(*)',
            'start_mark' => '<div class="tech-list">',
            'end_mark' => '</div>',
            'rules' => [
                ['field_type' => 'title', 'start_mark' => '<h2>', 'end_mark' => '</h2>', 'is_required' => 1],
                ['field_type' => 'content', 'start_mark' => '<div class="content">', 'end_mark' => '</div>', 'is_required' => 1]
            ]
        ]
    ];
    
    echo "\n🚀 开始批量导入采集节点...\n";
    echo "=====================================\n";
    
    $pdo->beginTransaction();
    $imported_count = 0;
    $skipped_count = 0;
    
    foreach ($collection_configs as $config) {
        echo "\n📝 处理节点: {$config['name']}\n";
        
        // 检查节点是否已存在
        $check_sql = "SELECT id FROM `{$table_prefix}collection_nodes` WHERE `name` = :name";
        $stmt = $pdo->prepare($check_sql);
        $stmt->execute(['name' => $config['name']]);
        
        if ($stmt->fetch()) {
            echo "   ⚠️  节点已存在，跳过\n";
            $skipped_count++;
            continue;
        }
        
        // 插入采集节点
        $node_sql = "INSERT INTO `{$table_prefix}collection_nodes` 
                     (`name`, `target_type`, `list_url`, `url_pattern`, `start_mark`, `end_mark`, `status`, `created_time`) 
                     VALUES (:name, :target_type, :list_url, :url_pattern, :start_mark, :end_mark, 1, :created_time)";
        
        $stmt = $pdo->prepare($node_sql);
        $stmt->execute([
            'name' => $config['name'],
            'target_type' => $config['target_type'],
            'list_url' => $config['list_url'],
            'url_pattern' => $config['url_pattern'],
            'start_mark' => $config['start_mark'],
            'end_mark' => $config['end_mark'],
            'created_time' => time()
        ]);
        
        $node_id = $pdo->lastInsertId();
        echo "   ✅ 节点创建成功 (ID: {$node_id})\n";
        
        // 插入采集规则
        if (isset($config['rules']) && is_array($config['rules'])) {
            foreach ($config['rules'] as $index => $rule) {
                $rule_sql = "INSERT INTO `{$table_prefix}collection_rules` 
                             (`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`, `created_time`) 
                             VALUES (:node_id, :field_type, :start_mark, :end_mark, :is_required, :sort_order, :created_time)";
                
                $stmt = $pdo->prepare($rule_sql);
                $stmt->execute([
                    'node_id' => $node_id,
                    'field_type' => $rule['field_type'],
                    'start_mark' => $rule['start_mark'],
                    'end_mark' => $rule['end_mark'],
                    'is_required' => $rule['is_required'],
                    'sort_order' => $index + 1,
                    'created_time' => time()
                ]);
                
                echo "   📋 规则创建: {$rule['field_type']}\n";
            }
        }
        
        $imported_count++;
    }
    
    $pdo->commit();
    
    echo "\n🎉 批量导入完成！\n";
    echo "=====================================\n";
    echo "✅ 成功导入: {$imported_count} 个节点\n";
    echo "⚠️  跳过重复: {$skipped_count} 个节点\n";
    
    // 验证导入结果
    echo "\n📊 验证导入结果:\n";
    $stmt = $pdo->query("SELECT id, name, target_type, status FROM `{$table_prefix}collection_nodes` ORDER BY id DESC LIMIT 3");
    $nodes = $stmt->fetchAll();
    
    foreach ($nodes as $node) {
        echo "   ID: {$node['id']} | 名称: {$node['name']} | 类型: {$node['target_type']} | 状态: {$node['status']}\n";
    }
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ 测试完成！\n";
?>
