<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>商家相册</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", modelType = 'business';
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="add" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>所属商家：</label></dt>
    <dd style="padding-left: 140px; overflow: visible;">
      <select class="chosen-select" name="uid" id="uid">
        <option value="0">请选择</option>
        {#foreach from=$businessList item=business#}
        <option value="{#$business.id#}"{#if $uid == $business.id#} selected{#/if#}>{#$business.title#}</option>
        {#/foreach#}
      </select>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>所属相册：</label></dt>
    <dd>
      <select name="typeid" id="typeid">
        <option value="0">请选择</option>
      </select>
      <button type="button" class="btn" id="typeBtn">相册管理</button>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>上传照片：</dt>
		<dd class="listImgBox hide">
			<div class="list-holder">
				<ul id="listSection4" class="clearfix listSection piece"></ul>
				<input type="hidden" name="pics" value='{#$pics#}' class="imglist-hidden">
			</div>
			<div class="btn-section clearfix">
				<div class="uploadinp filePicker" id="filePicker4" data-type="album" data-count="999" data-size="{#$atlasSize#}" data-imglist=""><div id="flasHolder"></div><span>添加图片</span></div>
				<div class="upload-tip">
					<p><a href="javascript:;" class="hide deleteAllAtlas">删除所有</a>&nbsp;&nbsp;{#$atlasType|replace:"*.":""|replace:";":"、"#}&nbsp;&nbsp;单张最大{#$atlasSize/1024#}M<span class="fileerror"></span></p>
				</div>
			</div>
		</dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
