<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
  <title>打印机管理</title>
  {#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 100px 10px 10px;"><button type="button" class="close" data-dismiss="alert">×</button>打印机配置教程：<a href="https://help.kumanyun.com/help-212-760.html" target="_blank">https://help.kumanyun.com/help-212-760.html</a></div>


<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
  {#if !$type && $userType != 3#}<a href="printerType.php" class="btn btn-info ml30" id="typeManage">打印机平台管理</a>{#/if#}
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <a href="businessPrinterAdd.php?action={#$action#}&type={#$type#}" class="btn btn-primary" id="addNew">新增店铺打印机</a>
    {#if $userType != 3#}
    &nbsp;&nbsp;&nbsp;<a href="javascript:;" data-url="./business/import_print.php" class="btn btn-link" id="importPrint">导入历史打印机</a>
    {#/if#}
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row25 left">标题</li>
  <li class="row25 left">店铺</li>
  <li class="row10 left">平台</li>
  <li class="row12 left">打印机终端号</li>
  <li class="row13 left">添加时间</li>
  <li class="row12">操作</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="sType"></span>
</div>

<script>
  var typeListArr = '{#$typeListArr#}', action = "{#$action#}", adminPath = "{#$adminPath#}", atype = '{#$type#}', userType = '{#$userType#}';
</script>
{#$jsFile#}
</body>
</html>
