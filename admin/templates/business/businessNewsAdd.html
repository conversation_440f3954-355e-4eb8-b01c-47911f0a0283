<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>商家动态</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", modelType = 'business', typeid = {#$typeid#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>所属商家：</label></dt>
    <dd style="padding-left: 140px; overflow: visible;">
      <select class="chosen-select" name="uid" id="uid">
        <option value="0">请选择</option>
        {#foreach from=$businessList item=business#}
        <option value="{#$business.id#}"{#if $uid == $business.id#} selected{#/if#}>{#$business.title#}</option>
        {#/foreach#}
      </select>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="title">标题：</label></dt>
    <dd>
      <input class="input-xxlarge" type="text" name="title" id="title" data-regex=".{2,50}" maxlength="50" value="{#$title#}" />
      <span class="input-tips"><s></s>请输入标题，2-50个字</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>动态分类：</label></dt>
    <dd>
      <select name="typeid" id="typeid">
        <option value="0">请选择</option>
      </select>
      <button type="button" class="btn" id="typeBtn">分类管理</button>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>内容：</dt>
    <dd>
      <script id="body" name="body" type="text/plain" style="width:85%;height:500px">{#$body#}</script>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$editorFile#}
{#$jsFile#}
</body>
</html>
