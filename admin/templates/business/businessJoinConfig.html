<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>商家入驻设置</title>
{#$cssFile#}
<style media="screen">
  .editform dt {width: 180px;}
  .domain-rules {margin: 0 50px;}
  .domain-rules th {font-size: 14px; line-height: 3em; border-bottom: 1px solid #ededed; padding: 0 5px; text-align: left;}
  .domain-rules td {font-size: 14px; line-height: 3.5em; border-bottom: 1px solid #ededed; padding: 0 5px;}
  .domain-rules .input-append, .domain-rules .input-prepend {margin: 15px 0 0;}
  .domain-rules input {font-size: 16px;}
  .editform dt label.sl {margin-top: -10px;}
  .editform dt small {display: block; margin: -8px 12px 0 0;}
  .editform dt small i {font-style: normal;}

  .priceWrap .table {width: auto;}
  .priceWrap .table th {min-width: 150px; height: 30px; text-align: center; line-height: 30px;}
  .priceWrap .table th:last-child {min-width: 50px;}
  .priceWrap .table td {text-align: center; height: 34px; line-height: 31px;}
  .priceWrap .level {font-size: 18px;}
  .priceWrap .input-append, .input-prepend {margin-bottom: 0;}
  .priceWrap .del {display: inline-block; vertical-align: middle;}
  .priceWrap .input-append select {margin: -5px -6px 0 -6px; border-radius: 0;}
</style>
<script>
var adminPath = "{#$adminPath#}", cfg_pointName = '{#$cfg_pointName#}';
</script>
<style>body {height: auto;} #cost .input-prepend {margin-right: 10px;}</style>
</head>

<body>

<div class="btn-group config-nav" data-toggle="buttons-radio">
  <button type="button" class="btn active" data-type="config">基本设置</button>
  <!-- <button type="button" class="btn" data-type="package">套餐管理</button> -->
  <button type="button" class="btn" data-type="activity">时长设置</button>
</div>

<div class="info-tips hide" id="infoTip"></div>

<form action="businessJoinConfig.php" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="configType" value="config" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <div class="item" id="config">

    
    <dl class="clearfix">
        <dt><label>商家入驻功能：</label></dt>
        <dd class="radio">
          {#html_radios name="joinState" values=$joinState checked=$joinStateChecked output=$joinStateNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>商家新入驻：</label></dt>
        <dd class="radio">
          {#html_radios name="joinCheck" values=$joinCheck checked=$joinCheckChecked output=$joinCheckNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix hide">
        <dt><label>入驻验证手机：</label></dt>
        <dd class="radio">
            {#html_radios name="joinCheckPhone" values=$joinCheckPhone checked=$joinCheckPhoneChecked output=$joinCheckPhoneNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>修改商家入驻信息：</label></dt>
        <dd class="radio">
            {#html_radios name="editJoinCheck" values=$editJoinCheck checked=$editJoinCheckChecked output=$editJoinCheckNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>模块店铺入驻：</label></dt>
        <dd class="radio">
            {#html_radios name="moduleJoinCheck" values=$moduleJoinCheck checked=$moduleJoinCheckChecked output=$moduleJoinCheckNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>修改模块店铺信息：</label></dt>
        <dd class="radio">
            {#html_radios name="editModuleJoinCheck" values=$editModuleJoinCheck checked=$editModuleJoinCheckChecked output=$editModuleJoinCheckNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>入驻时长单位：</label></dt>
        <dd class="radio">
            {#html_radios name="joinTimesUnit" values=$joinTimesUnit checked=$joinTimesUnitChecked output=$joinTimesUnitNames separator="&nbsp;&nbsp;"#}
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>企业重复入驻：</label></dt>
        <dd class="radio">
            {#html_radios name="joinRepeat" values=$joinRepeat checked=$joinRepeatChecked output=$joinRepeatNames separator="&nbsp;&nbsp;"#}
            <span class="input-tips" style="display:inline-block;"><s></s>选择【不限制】表示一个营业执照可以入驻多个店铺，选择【限制】表示一个营业执照只能入驻一个店铺。<br />该功能只对前台商家自助入驻时生效，后台管理员手动添加商家不受此影响。</span>
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label>入驻认证材料：</label></dt>
        <dd class="radio">
            <label><input type="checkbox" name="joinCheckMaterial[]" value="business" {#if in_array('business',$joinCheckMaterialArr)#}checked="checked"{#/if#}>营业执照</label>
            <label><input type="checkbox" name="joinCheckMaterial[]" value="id" {#if in_array('id',$joinCheckMaterialArr)#}checked="checked"{#/if#}>身份证</label>
            <span class="input-tips" style="display:inline-block;"><s></s>如果两个都选中，商家可以根据自己的情况选择其中一种提交。<br />建议开启系统基本参数中聚合数据接口的企业工商数据和身份证识别功能，将有效提升审核准确度！</span>
        </dd>
    </dl>


    <dl class="clearfix" style="margin-top: 30px;">
      <dt style="width: 140px;"><strong style="font-size: 16px;">商家特权：</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt style="width: 50px;"></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>功能</th>
                <th>自定义显示名称</th>
                <th class="hide">标签</th>
                <th>描述</th>
                <th style="width: 150px;">价格</th>
                <th style="width: 150px;">原价</th>
                <th style="width: 100px;">状态</th>
              </tr>
            </thead>
            <tbody>
              {#foreach from=$cfg_businessPrivilegeArr item='module' key='name'#}
              <tr>
                <td>{#$module.title#}</td>
                <td><input class="input-small" type="text" name="business[{#$module.name#}][title]" value="{#if $businessPrivilege[$module.name]['title']#}{#$businessPrivilege[$module.name]['title']#}{#else#}{#$module.title#}{#/if#}"></td>
                <td class="hide"><input class="input-small" type="text" name="business[{#$module.name#}][label]" value="{#$businessPrivilege[$module.name]['label']#}"></td>
                <td><textarea  name="business[{#$module.name#}][note]">{#$businessPrivilege[$module.name]['note']#}</textarea></td>
                <td><div class="input-append"><input class="input-mini" type="text" name="business[{#$module.name#}][price]" value="{#$businessPrivilege[$module.name]['price']#}"><span class="add-on">{#echoCurrency type='short'#}/<span class="joinTimesUnit">月</span></span></div></td>
                <td><div class="input-append"><input class="input-mini" type="text" name="business[{#$module.name#}][mprice]" value="{#$businessPrivilege[$module.name]['mprice']#}"><span class="add-on">{#echoCurrency type='short'#}/<span class="joinTimesUnit">月</span></span></div></td>
                <td><a style="font-size: 14px;{#if $businessPrivilege[$module.name]['state']#} color: #ff0000;{#/if#}" href="javascript:;" class="state" data-state="{#$businessPrivilege[$module.name]['state']|default:0#}">{#if $businessPrivilege[$module.name]['state']#}点击启用{#else#}点击停用{#/if#}</a><input type="hidden" name="business[{#$module.name#}][state]" value="{#$businessPrivilege[$module.name]['state']|default:0#}" /></td>
              </tr>
              {#/foreach#}
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt style="width: 140px;"><strong style="font-size: 16px;">行业特权：</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt style="width: 50px;"></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>模块</th>
                <th>自定义显示名称</th>
                <th class="hide">标签</th>
                <th>描述</th>
                <th style="width: 150px;">价格</th>
                <th style="width: 150px;">原价</th>
              </tr>
            </thead>
            <tbody>
              {#foreach from=$cfg_businessStoreModuleArr item='module' key='name'#}
              {#if $module.name != 'job'#}
              <tr>
                <td>{#$module.title#}</td>
                <td><input class="input-small" type="text" name="store[{#$module.name#}][title]" value="{#if $businessStore[$module.name]['title']#}{#$businessStore[$module.name]['title']#}{#else#}{#$module.title#}{#/if#}"></td>
                <td class="hide"><input class="input-small" type="text" name="store[{#$module.name#}][label]" value="{#$businessStore[$module.name]['label']#}"></td>
                <td><textarea  name="store[{#$module.name#}][note]">{#$businessStore[$module.name]['note']#}</textarea></td>
                <td><div class="input-append"><input class="input-mini" type="text" name="store[{#$module.name#}][price]" value="{#$businessStore[$module.name]['price']#}"><span class="add-on">{#echoCurrency type='short'#}/<span class="joinTimesUnit">月</span></span></div></td>
                <td><div class="input-append"><input class="input-mini" type="text" name="store[{#$module.name#}][mprice]" value="{#$businessStore[$module.name]['mprice']#}"><span class="add-on">{#echoCurrency type='short'#}/<span class="joinTimesUnit">月</span></span></div></td>
              </tr>
              {#/if#}
              {#/foreach#}
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

  </div>

  <!--
  <div class="item hide" id="package">

      <dl class="clearfix">
        <dt><strong style="font-size: 16px;">入驻套餐：</strong></dt>
        <dd>&nbsp;</dd>
      </dl>
      <dl class="clearfix">
        <dt style="width: 50px;"></dt>
        <dd>
          <div class="priceWrap">
            <table class="table table-hover table-bordered table-striped">
              <thead>
                <tr>
                  <th>套餐名</th>
                  <th>图标</th>
                  <th>标签</th>
                  <th class="price">价格</th>
                  <th class="price">原价</th>
                  <th>套餐内容</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="packageBody">

                {#if $businessPackage#}
                {#foreach from=$businessPackage key='k' item='package'#}
                <tr>
                  <td><input class="input-small" type="text" name="package[title][]" value="{#$package['title']#}"></td>
                  <td>
                      {#if $package['icon']#}
                      <img src="/include/attachment.php?f={#$package['icon']#}" class="img" alt="" style="height:40px;">
                      {#/if#}
                      <a href="javascript:;" class="upfile" title="上传图标">上传图标</a>
                      <input type="file" name="Filedata" class="imglist-hidden Filedata" style="display: none;" id="Filedata_{#$k#}">
                      <input type="hidden" name="package[icon][]" class="icon" value="{#$package['icon']#}">
                  </td>
                  <td><input class="input-small" type="text" name="package[label][]" value="{#$package['label']#}"></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[price][]" value="{#$package['price']#}"><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[mprice][]" value="{#$package['mprice']#}"><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><a href="javascript:;" class="manage" title="管理套餐内容">管理套餐内容</a><input type="hidden" name="package[list][]" value="{#$package['list']#}" /></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/foreach#}
                {#else#}
                <tr>
                  <td><input class="input-small" type="text" name="package[title][]" value=""></td>
                  <td>
                      <a href="javascript:;" class="upfile" title="上传图标">上传图标</a>
                      <input type="file" name="Filedata" class="imglist-hidden Filedata" style="display: none;" id="Filedata_0">
                      <input type="hidden" name="package[icon][]" class="icon" value="">
                  </td>
                  <td><input class="input-small" type="text" name="package[label][]" value="推荐"></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[price][]" value=""><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[mprice][]" value=""><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><a href="javascript:;" class="manage" title="管理套餐内容">管理套餐内容</a><input type="hidden" name="package[list][]" value="" /></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                <tr>
                  <td><input class="input-small" type="text" name="package[title][]" value=""></td>
                  <td>
                      <a href="javascript:;" class="upfile" title="上传图标">上传图标</a>
                      <input type="file" name="Filedata" class="imglist-hidden Filedata" style="display: none;" id="Filedata_1">
                      <input type="hidden" name="package[icon][]" class="icon" value="">
                  </td>
                  <td><input class="input-small" type="text" name="package[label][]" value="超值"></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[price][]" value=""><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><div class="input-append"><input class="input-small price" step="0.01" type="number" name="package[mprice][]" value=""><span class="add-on">{#echoCurrency type='short'#}/月</span></div></td>
                  <td><a href="javascript:;" class="manage" title="管理套餐内容">管理套餐内容</a><input type="hidden" name="package[list][]" value="" /></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/if#}

              </tbody>
              <tbody>
                <tr>
                  <td colspan="7">
                    <button type="button" class="btn btn-small addPackage">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </dd>
      </dl>
  </div>
  -->

  <div class="item hide" id="activity">

    <!--
      <dl class="clearfix">
        <dt><strong style="font-size: 16px;">开通时长：</strong></dt>
        <dd>&nbsp;</dd>
      </dl>
      <dl class="clearfix">
        <dt style="width: 140px;"></dt>
        <dd>
          <div class="priceWrap">
            <table class="table table-hover table-bordered table-striped">
              <tbody id="businessTimes">
                {#if $businessJoinTimes#}
                {#foreach from=$businessJoinTimes item='times'#}
                <tr>
                  <td><div class="input-append"><input class="input-small" step="1" type="number" name="times[]" value="{#$times#}"><span class="add-on">个月</span></div></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/foreach#}
                {#else#}
                <tr>
                  <td><div class="input-append"><input class="input-small" step="1" type="number" name="times[]" value=""><span class="add-on">个月</span></div></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/if#}
              </tbody>
              <tbody>
                <tr>
                  <td colspan="7">
                    <button type="button" class="btn btn-small addTimes">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </dd>
      </dl>

      <dl class="clearfix">
        <dt><strong style="font-size: 16px;">满减：</strong></dt>
        <dd>&nbsp;</dd>
      </dl>
      <dl class="clearfix">
        <dt style="width: 140px;"></dt>
        <dd>
          <div class="priceWrap">
            <table class="table table-hover table-bordered table-striped">
              <tbody id="businessSale">
                {#if $businessJoinSale#}
                {#foreach from=$businessJoinSale item='sale'#}
                <tr>
                  <td><div class="input-prepend input-append"><span class="add-on">满</span><input class="input-small" step="1" type="number" name="price[]" value="{#$sale.price#}"><span class="add-on">减</span><input class="input-small" step="1" type="number" name="amount[]" value="{#$sale.amount#}"><span class="add-on">{#echoCurrency type='short'#}</span></div></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/foreach#}
                {#else#}
                <tr>
                  <td><div class="input-prepend input-append"><span class="add-on">满</span><input class="input-small" step="1" type="number" name="price[]" value=""><span class="add-on">减</span><input class="input-small" step="1" type="number" name="amount[]" value=""><span class="add-on">{#echoCurrency type='short'#}</span></div></td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/if#}
              </tbody>
              <tbody>
                <tr>
                  <td colspan="7">
                    <button type="button" class="btn btn-small addSale">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </dd>
      </dl>
      -->

      <!-- <dl class="clearfix">
        <dt><strong style="font-size: 16px;">送积分：</strong></dt>
        <dd>&nbsp;</dd>
      </dl> -->
      <dl class="clearfix">
        <dt style="width: 50px;"></dt>
        <dd>
          <div class="priceWrap">
            <table class="table table-hover table-bordered table-striped">
              <tbody id="businessRule">
                {#foreach from=$businessJoinRule item='rule'#}
                <tr>
                  <td>
                    <div class="input-prepend input-append">
                        <span class="add-on">开通</span><input class="input-mini" step="1" type="text" name="times[]" value="{#$rule.times#}"><span class="add-on"><span class="joinTimesUnit1">个月</span></span>
                        <span class="add-on" style="margin-left: 10px;">打</span><input class="input-mini" step="1" type="text" name="discount[]" value="{#$rule.discount#}"><span class="add-on">折</span>
                        <span class="add-on" style="margin-left: 10px;">送</span><input class="input-mini" step="1" type="text" name="point[]" value="{#$rule.point#}"><span class="add-on">{#$cfg_pointName#}</span>
                    </div>
                  </td>
                  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
                </tr>
                {#/foreach#}
              </tbody>
              <tbody>
                <tr>
                  <td colspan="7">
                    <button type="button" class="btn btn-small addTimes">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </dd>
      </dl>

  </div>

  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

<script type="text/javascript">
    var businessPrivilege = {#$businessPrivilegeJson#}, businessStore = {#$businessStoreJson#};
</script>
{#$jsFile#}
</body>
</html>
