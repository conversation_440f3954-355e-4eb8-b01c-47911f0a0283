<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var atlasSize = {#$atlasSize#}, atlasType = "{#$atlasType#}", atlasMax = 0;  //图集配置
var mapCity = "{#$cfg_mapCity#}",
	typeid = {#$typeid#}, typeListArr = {#$typeListArr#}, action = '{#$action#}', modelType = '{#$action#}', adminPath = "{#$adminPath#}";
var service = 'business';
var cityid = {#$cityid#}, cityList = {#$cityList#};
</script>
<style>
	#videoPreview video {width:200px;}
	#bannerBox .list-holder li {width: 115px !important;height: 86px;}
	#bannerBox .list-holder li .li-thumb {margin: -5px 0 0 0 !important;}
	#bannerBox .list-holder li a.li-rm {margin: -17px -14px 0 0 !important;}
	</style>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="module">城市：</label></dt>
    <dd style="overflow: visible; padding-left: 140px;">
      <select class="chosen-select" id="cityid" name="cityid" style="width: auto; min-width: 150px;"></select>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="title">信息标题：</label></dt>
    <dd>
      <input class="input-xxlarge" type="text" name="title" id="title" data-regex=".{1,60}" maxlength="60" value="{#$title#}" />
      <span class="input-tips"><s></s>请输入信息标题，1-60个汉字</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>信息分类：</dt>
    <dd style="overflow:visible;">
      <div class="btn-group" id="typeBtn" style="margin-left:10px;">
        <button class="btn dropdown-toggle" type="button" data-toggle="dropdown">{#$typename#}<span class="caret"></span></button>
      </div>
      <input type="hidden" name="typeid" id="typeid" value="{#$typeid#}" />
      <span class="input-tips"><s></s>请选择信息分类</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>缩略图：</dt>
    <dd class="thumb clearfix listImgBox">
      <div class="uploadinp filePicker thumbtn{#if $litpic != ""#} hide{#/if#}" id="filePicker1" data-type="thumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
      {#if $litpic != ""#}
      <ul id="listSection1" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_0_1"><a href='{#$cfg_attachment#}{#$litpic|escape:"url"#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$litpic|escape:"url"#}" data-val="{#$litpic#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
      {#else#}
      <ul id="listSection1" class="listSection thumblist clearfix"></ul>
      {#/if#}
      <input type="hidden" name="litpic" value="{#$litpic#}" class="imglist-hidden" id="litpic">
    </dd>
  </dl>
	<dl class="clearfix">
    <dt><label for="body">信息详情：</label></dt>
    <dd>
      <script id="body" name="body" type="text/plain" style="width:85%;height:500px">{#$body#}</script>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="sid">插入商家：</label></dt>
    <dd style="position:static;">
      <input class="input-large" type="text" id="storename" value="{#$storename#}" autocomplete="off" />
      <input type="hidden" name="sid" id="sid" value="{#$sid#}" />
      <span class="input-tips" style="display:inline-block;"><s></s>输入商家名称</span>
      <div id="companyList" class="popup_key"></div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="click">浏览次数：</label></dt>
    <dd>
      <span><input class="input-mini" type="number" name="click" min="0" id="click" value="{#$click#}" /></span>
      <label class="ml30" for="weight">排序：</label><input class="input-mini" type="number" name="weight" id="weight" min="1" data-regex="[1-9]\d*" value="{#$weight#}" />
      <span class="input-tips"><s></s>必填，排序越大，越排在前面</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="writer">作者：</label></dt>
    <dd>
      <input class="input-medium" type="text" name="writer" id="writer" placeholder="信息作者" value="{#$writer#}" /><button type="button" class="btn chooseData" data-type="writer">选择</button>
      <span class="ml30" style="font-size: 14px;">
        发布时间：<div class="input-append form_datetime" style="margin: 0;">
          <input class="input-medium" type="text" name="pubdate" id="pubdate" date-language="ch" value="{#$pubdate#}" />
          <span class="add-on"><i class="icon-time"></i></span>
        </div>
      </span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="state">信息状态：</label></dt>
    <dd class="radio">
      <select name="state" id="state" class="input-medium">
        {#html_options options=$stateList selected=$state#}
      </select>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>
{#$editorFile#}
{#$jsFile#}
</body>
</html>
