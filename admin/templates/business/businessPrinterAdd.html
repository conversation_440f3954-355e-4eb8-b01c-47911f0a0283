<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
  <title>{#$pagetitle#}</title>
  {#$cssFile#}
  <script>
    var atlasSize = {#$atlasSize#}, atlasType = "{#$atlasType#}", atlasMax = 0;  //图集配置
    var atype = '{#$type#}', classid = '{#$classid#}', typeid = '{#$typeid#}', action = modelType = '{#$action#}',
            adminPath = "{#$adminPath#}";
  </script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="typeid">打印机平台：</label></dt>
    <dd>
      <select name="typeid" id="typeid">
        <option value="">请选择</option>
        {#foreach from=$print item=l#}
        <option value="{#$l.print_code#}" {#if $type == $l.print_code#} selected{#/if#}>{#$l.print_name#}</option>
        {#/foreach#}
      </select>
      <span class="input-tips"><s></s>请选择打印机平台</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="shopid">所属店铺：</label></dt>
    <dd style="overflow: inherit; padding-left: 140px;">
        <select class="chosen-select" name="shopid" id="shopid">
        <option value="">全部店铺</option>
        {#foreach from=$shop item=l#}
        <option value="{#$l.id#}" {#if $sid == $l.id#} selected{#/if#}>{#$l.shopname#}</option>
        {#/foreach#}
      </select>
      <span class="input-tips"><s></s>请选择所属店铺</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="title">打印机名称：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="title" id="title" data-regex=".{1,60}" maxlength="60" value="{#$title#}" />
      <span class="input-tips"><s></s>打印机名称</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="mcode">打印机终端号：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="mcode" id="mcode" data-regex=".{1,60}" maxlength="60" value="{#$mcode#}" />
      <span class="input-tips"><s></s>请输入打印机终端号</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="msign">打印机秘钥：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="msign" id="msign" data-regex=".{1,60}" maxlength="60" value="{#$msign#}" />
      <span class="input-tips"><s></s>请输入秘钥</span>
    </dd>
  </dl>

  <dl class="clearfix">
    <dt><label for="printMode">打印方式：</label></dt>
    <dd>
      <select name="printMode" id="printMode">
          {#html_options options=$printModuleList selected=$printModule#}
      </select>
      <span class="input-tips" style="display: inline-block;"><s></s>图片打印只支持易联云K6打印机！</span>
    </dd>
  </dl>
  
  <dl class="clearfix hide">
    <dt><label for="clientId">应用ID：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="clientId" id="clientId" data-regex=".{1,60}" maxlength="60" value="{#$clientId#}" />
    </dd>
  </dl>

  <dl class="clearfix hide">
    <dt><label for="client_secret">应用秘钥：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="client_secret" id="client_secret" data-regex=".{1,60}" maxlength="60" value="{#$client_secret#}" />
    </dd>
  </dl>

  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>


{#$editorFile#}
{#$jsFile#}
</body>
</html>
