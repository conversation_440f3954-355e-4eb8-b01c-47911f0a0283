<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>管理商家入驻申请</title>
{#$cssFile#}
</head>

<body>
<div class="search">
  <label>搜索：
  <select class="chosen-select" id="cityid" style="width: auto;">
    <option value="">选择分站城市</option>
    {#foreach from=$cityArr item=city#}
    <option value="{#$city.id#}">{#$city.name#}</option>
    {#/foreach#}
  </select>&nbsp;&nbsp;
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <div class="btn-group" id="typeBtn" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部分类<span class="caret"></span></button>
  </div>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" id="delBtn">删除</button>
    <button class="btn btn-success hide" id="batchAudit">批量审核</button>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row15 left">店铺名称</li>
  <li class="row25 left">所属会员</li>
  <li class="row10 left">经营品类</li>
  <li class="row10 left">所在区域</li>
  <li class="row12 left">手机/邮箱</li>
  <li class="row15 left">申请时间</li>
  <li class="row10">&nbsp;&nbsp;操 作</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="sType"></span>
</div>

<script>
  var typeListArr = {#$typeListArr#}, adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
