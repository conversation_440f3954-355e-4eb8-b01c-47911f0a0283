<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
  <title>管理打印机</title>
  {#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 100px 10px 10px;"><button type="button" class="close" data-dismiss="alert">×</button>打印机配置教程：<a href="https://help.kumanyun.com/help-212-760.html" target="_blank">https://help.kumanyun.com/help-212-760.html</a></div>

{#if $installArr|@count neq 0#}
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px;">
  <li class="row80 left">&nbsp;&nbsp;已安装</li>
  <li class="row20 left">操作</li>
</ul>
<div class="list mb50" id="list" style="margin-top:-20px;">
  <ul class="root">
    {#foreach from=$installArr item=install#}
    <li class="li0">
      <div class="tr clearfix" data-id="{#$install.print_id#}">
        <div class="row80 left">&nbsp;&nbsp;&nbsp;<strong>{#$install.print_name#}</strong><sup>{#$install.version#}</sup>&nbsp;&nbsp;<a href="javascript:;" class="explain">说明</a></div>
        <div class="row20 left"><a href="printerType.php?action=edit&id={#$install.print_id#}" class="modify" data-title="{#$install.print_name#}" data-id="{#$install.print_id#}">配置</a>&nbsp;|&nbsp;<a href="printerType.php?action=uninstall&id={#$install.print_id#}" class="uninstall">卸载</a>{#if $install.state == 2#}&nbsp;&nbsp;&nbsp;&nbsp;<font color="#f00">未启用</font>{#/if#}</div>
        <div class="hide">{#$install.print_desc#}</div>
      </div>
    </li>
    {#/foreach#}
  </ul>
</div>
{#/if#}
{#if $uninstallArr|@count neq 0#}
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px;">
  <li class="row80 left">&nbsp;&nbsp;未安装</li>
  <li class="row20 left">操作</li>
</ul>
<div class="list mb50" style="margin-top:-20px;">
  <ul>
    {#foreach from=$uninstallArr item=install#}
    <li>
      <div class="tr clearfix">
        <div class="row80 left">&nbsp;&nbsp;&nbsp;<strong>{#$install.print_name#}</strong><sup>{#$install.version#}</sup>&nbsp;&nbsp;<a href="javascript:;" class="explain">说明</a></div>
        <div class="row20 left"><a href="printerType.php?action=install&code={#$install.print_code#}" class="modify" data-title="{#$install.print_name#}" data-id="{#$install.print_code#}">安装</a></div>
        <div class="hide">{#$install.print_desc#}</div>
      </div>
    </li>
    {#/foreach#}
  </ul>
</div>
{#/if#}
<script>var adminPath = "{#$adminPath#}";</script>
{#$jsFile#}
</body>
</html>
