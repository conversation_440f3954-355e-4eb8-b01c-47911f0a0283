<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>推文助手</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", cfg_basehost = '{#$cfg_basehost#}', infoTypeListArr = {#$infoTypeListArr#},shopTypeListArr = {#$shopTypeListArr#},jobTypeListArr={#$jobTypeListArr#}, cityList = {#$cityList#};
</script>
<style>
    #editform {min-height: 800px;}
    .wechat_demo {position: fixed; top: 25px; left: 50%; margin-left: 230px; width: 375px; height: 790px; background: url('/static/images/admin/wechat_mockup.jpg?v=2') no-repeat top center; background-size: 375px; text-align: center; overflow: hidden;}
    .wechat_main1 {width: 355px; height: 576px; margin: 83px auto 0; overflow: hidden; border-radius: 0 0 50px 40px;}
    .wechat_main {width: 350px; height: 571px; padding: 0 0 0 5px; margin: 0 auto; border-radius: 0 0 40px 40px; overflow-x: hidden; overflow-y: auto; text-align: left;}
    #copyWechat {margin: 25px auto 0;}
    .wechat_empty {line-height: 400px; text-align: center; color: #999;}
    .pagination {display: none!important;}
    .pagination_show {display: block!important;}
    .rich_media_title {font-size: 18px; line-height: 1.4; margin: 10px 0 5px 0; padding: 0 5px; font-weight: 600;}
    .rich_media_meta_list {padding: 0 5px;}
    .rich_media_meta {display: inline-block; vertical-align: middle; margin: 0 10px 5px 0; font-size: 12px; -webkit-tap-highlight-color: rgba(0,0,0,0);}
    .rich_media_meta a {color: #576b95;}
    .rich_media_meta_text {color: rgba(0,0,0,0.3); font-style: normal;}
    #wechatPreview {padding: 0 5px;}
    .item.fn-hide{display: none;}

    /* 分类信息模板一 */
    /* .infoTemplate_1 .previewBox{min-height: 100vh ;} */
    .infoTemplate_1 .previewBox1{background: #FFAC1E;  padding-top: 20px; box-sizing: border-box; padding-bottom: 1px;}
    .previewBox1:nth-child(1){padding-top: 58px;}
    .infoTemplate_1 .bg_white{margin-left:10px; margin-right: 20px; background: #F2F2F2; border-radius: 10px; border: solid 1px #f2f2f2; box-sizing: border-box; margin-bottom: 40px;}
    .infoTemplate_1 .bg_white .previewCon{background:#fff; margin-left: 10px; margin-right: -10px;  border-radius: 10px; margin-top: -15px; margin-bottom: 10px; box-sizing: border-box; padding:  15px 10px 10px;}
    .infoTemplate_1 .previewCon .titleBox{font-size: 15px; color: #fff; text-align: center; padding:0 25px; border-radius: 3px; background: linear-gradient(90deg, #FFAC1E 0%, #FFA415 100%); display: inline-block; line-height: 24px; height: 24px; }
    .infoTemplate_1 .previewCon .titleBox s{display: block; width: 0; height: 0; border: solid 4px rgba(0,0,0,0); border-top: solid 4px #FFA415; margin-left: -10px;}
    .infoTemplate_1 .previewCon .textCon{font-size: 15px; color: #333; line-height: 28px; font-weight: 400; margin-right: 10px; padding-top: 10px;}
    .infoTemplate_1 .previewCon .textCon p {text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden;}
    .infoTemplate_1 .previewCon .fbInfo{height: 45px; background: #F5F5F5; border-radius: 3px; display: flex; align-items: center; justify-content: space-between; padding: 0 10px; box-sizing: border-box; margin-bottom: 5px; white-space: nowrap;}
    .infoTemplate_1 .previewCon .fbInfo .fb_left{display: flex; align-items: center;}
    .infoTemplate_1 .previewCon .fbInfo .fb_left .headIcon{width: 20px; height: 20px; box-sizing: border-box; padding: 2px; border-radius: 50%}
    .infoTemplate_1 .previewCon .fbInfo .fb_left .headIcon img{display: block; width: 100%; height: 100%; object-fit: cover; border-radius: 50%;}
    .infoTemplate_1 .previewCon .fbInfo .fb_left span{font-size: 13px; color: #333; margin-left: 4px;}
    .infoTemplate_1 .previewCon .fbInfo .fb_right{text-align: right; font-size: 12px; color: #808080; }
    .infoTemplate_1 .previewCon .house-images {margin: 0 auto 20px; height: 133px; display: flex; align-items: center;}
    .infoTemplate_1 .previewCon .house-images img {border-radius: 4px;}
    .infoTemplate_1 .previewCon .house-images .img1 {flex: 1 1 170px; width: 170px; height: 100%!important; min-height: 133px!important; max-height: 133px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_1 .previewCon .house-images .right-img {margin-left: 10px; flex: 0 1 85px; height: 134px!important;}
    .infoTemplate_1 .previewCon .house-images .right-img .img-item {display: block; width: 85px!important; height: 64px!important; min-height: 62px!important; max-height: 62px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_1 .previewCon .house-images .right-img .img-item:first-of-type {margin-bottom: 10px;}
    .infoTemplate_1 .previewCon .qrBox{display: flex; justify-content: space-between; margin-top: 14px}
    .infoTemplate_1 .previewCon .qrBox .left_text{min-width: 155px; border-radius: 8px; font-size: 13px; background: linear-gradient(90deg, #FFAC1E 0%, #FFA415 100%); color: #000; box-sizing: border-box; padding: 10px; flex: 1 1 155px; line-height: 1.9em;}
    .infoTemplate_1 .previewCon .qrBox .left_text p{margin: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
    .infoTemplate_1 .previewCon .qrBox .left_text p:nth-child(1){margin-bottom: 0}
    .infoTemplate_1 .previewCon .qrBox .arr {display: inline-block; width: 12px; height: 20px; background: url('{#$cfg_basehost#}/static/images/admin/arr_gzh_1.png') no-repeat center; background-size: cover; margin: 36px 10px 0 0;}
    .infoTemplate_1 .previewCon .qrBox .right_qr{width: 90px !important;  flex-shrink: 0; height: 90px !important;}
    .infoTemplate_1 .previewCon .qrBox .right_qr img{display: block; width: 90px; height: 90px; object-fit: cover;}

    /* 分类信息模板二 */
    /* .infoTemplate_2 {min-height: 100vh;} */
    .infoTemplate_2 .previewBox2{background: #DCF2EB; padding-bottom: 24px;}
    .infoTemplate_2 .previewBox2:first-child{padding-top: 60px;}
    .infoTemplate_2 .bg_wrap{background: linear-gradient(79deg, #A6EDDA, #D9EEFF, #77D1BA); box-shadow: 0px 2px 8px 0px rgba(0, 109, 243, 0.06); border-radius: 13px; margin: 0 13px; box-sizing: border-box; padding: 3px; position: relative; margin-bottom: 10px; }
    .infoTemplate_2 .bg_wrap .previewCon{background: #f0f6fe; border-radius: 10px; padding-bottom:14px;}
    .infoTemplate_2 .previewCon .titleBox{display: flex;  flex-direction: column; height: 35px; overflow: hidden;}
    .infoTemplate_2 .previewCon .titleBox span,.infoTemplate_2 .title_text{padding: 0 10px; font-size: 30px; color: #273564; font-weight: bold; display: block; transform: translateY(-22px); padding: 0; }
    .infoTemplate_2 .previewCon .titleBox span{margin-left: -5px; color: #18A867;}
    .infoTemplate_2 .title_text{padding: 0px 25px; transform: none; margin-bottom: -19px; line-height: 38px;}
    .infoTemplate_2 .previewCon .titleBox .title_bg{height: 35px; background: linear-gradient(0deg, #6BE3B9 0%, #4BE3A9 76%, #58EAB1 100%); border-radius: 10px 10px 0 0; opacity: .75; box-sizing: border-box; padding: 0 14px; color:#fff; opacity:.9; text-align:right; line-height:35px;  }
    .infoTemplate_2 .previewCon .titleBox .title_bg .flexWrap{display:flex; justify-content: space-between; min-width: 100%;  text-align: left;}
    .infoTemplate_2 .previewCon .white_con{ margin: 0 15px; padding-top:20px; }
    .infoTemplate_2 .previewCon .fbBox{margin-bottom: 10px; }
    .infoTemplate_2 .previewCon .fbBox s.line,.infoTemplate_2 .previewCon .qrBox .text_gzh .line{display: inline-block; width: 8px; height: 25px; background: linear-gradient(0deg, #6BE3B9 0%, #4BE3A9 76%, #58EAB1 100%); border-radius: 4px; vertical-align: middle; margin-right: 8px;}
    .infoTemplate_2 .previewCon .fbBox span{font-size: 19px; color: #273564; font-weight: bold; vertical-align: middle;}
    .infoTemplate_2 .previewCon .fbBox em{font-style: normal !important;  vertical-align: -webkit-baseline-middle; font-size: 12px;}
    .infoTemplate_2 .previewCon .textCon{ color: #273564; line-height: 26px; font-size: 15px; margin-bottom: 15px;}
    .infoTemplate_2 .previewCon .textCon p {text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden;}
    .infoTemplate_2 .previewCon .house-images {margin: 0 auto 10px; height: 134px; display: flex; align-items: center;}
    .infoTemplate_2 .previewCon .house-images .img1 {flex: 1 1 190px; width: 190px; height: 100%!important; min-height: 134px!important; max-height: 134px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_2 .previewCon .house-images .right-img {margin-left: 10px; flex: 0 1 90px; height: 134px!important;}
    .infoTemplate_2 .previewCon .house-images .right-img .img-item {display: block; width: 90px!important; height: 62px!important; min-height: 62px!important; max-height: 62px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_2 .previewCon .house-images .right-img .img-item:first-of-type {margin-bottom: 10px;}
    .infoTemplate_2 .previewCon .qrBox{display: flex; align-items: center; justify-content: space-between; margin-top: 15px;}
    .infoTemplate_2 .previewCon .qrBox .left_text{display: flex; flex-direction: column;}
    .infoTemplate_2 .previewCon .qrBox .right_qr{width: 96px !important; flex-shrink: 0;}
    .infoTemplate_2 .previewCon .qrBox .right_qr img{display: block; object-fit: cover}
    .infoTemplate_2 .previewCon .qrBox .text_gzh {display: flex; align-items: center;}
    .infoTemplate_2 .previewCon .qrBox .text_gzh .line{height: 35px;}
    .infoTemplate_2 .previewCon .qrBox .text_gzh .gzh_info{display: flex; flex-direction: column; justify-content: center; }
    .infoTemplate_2 .previewCon .qrBox .text_gzh p{font-size: 10px; color: #3D6799; opacity: .8; margin: 0}
    .infoTemplate_2 .previewCon .qrBox .text_gzh .h2{font-size: 18px; color: #273564; font-weight: 700;}
    .infoTemplate_2 .previewCon .qrBox .tips{color: #273564; font-size: 15px; line-height: 26px; font-weight: 500; font-weight: bold; display: flex; align-items: flex-start; margin-top: 6px; white-space: nowrap;}
    .infoTemplate_2 .previewCon .qrBox .tips .arr{display: block; width: 13px; height: 13px; margin: 6px 0 0 5px; background: url('{#$cfg_basehost#}/static/images/admin/arr_gzh.png') no-repeat center; background-size: cover;}
    .infoTemplate_2 .previewCon .qrBox .tips .arr img,.infoTemplate_2 .previewConBox .line2  img{display: block; width: 100%; height: 100%; object-fit: cover;}

    /* 分类信息模板三 */
    .infoTemplate_3 .previewBox3 {background-color: #6291FD;}
    .infoTemplate_3 .previewBox3:nth-child(1){padding-bottom: 0; margin-bottom: -53px; height: 285px;}
    .infoTemplate_3 .previewBox3 .bgBox,.infoTemplate_3 .previewBox3 .titleBox{height: 285px; }
    .infoTemplate_3 .previewBox3 .bgBox {height: 285px; background: url('{#$cfg_basehost#}/static/images/admin/bg_gzh.png') no-repeat bottom center; background-size: cover;}
    .infoTemplate_3 .previewBox3 .titleBox{padding-top: 50px; padding-left: 32px; margin-top: -285px; box-sizing: border-box;}
    .infoTemplate_3 .previewBox3 .titleBox .h1{font-size: 36px; color: #fff; font-weight: bold; transform: skewX(-10deg); line-height: 42px; white-space: nowrap;}
    .infoTemplate_3 .previewBox3 .titleBox .h1.marginLeft{margin-left: 85px;}
    .infoTemplate_3 .previewBox3 .bg_wrap{background: linear-gradient(180deg, rgb(185 208 255 / 47%), #D9EEFF, #D9EEFF, #D9EEFF, #D9EEFF, #D9EEFF, #D9EEFF, #D9EEFF); box-shadow: 0px 1px 8px 0px rgba(255, 151, 0, 0.06); border-radius: 13px; padding: 3px; margin: 0 12px;}
    .infoTemplate_3 .previewBox3 .previewCon{background: rgba(255, 255, 255, 0.97); border-radius: 10px;  padding-bottom: 20px; }
    .infoTemplate_3 .previewBox3:nth-child(2) .previewCon {background: linear-gradient(180deg, rgb(255 255 255 / 47%), #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff);}
    .infoTemplate_3 .previewBox3 .previewCon .fbBox{ padding: 25px 15px 0;}
    .infoTemplate_3 .previewBox3 .previewCon .fbBox .titleText{font-size: 19px; color: #273564; font-weight: bold; margin-right: 6px}
    .infoTemplate_3 .previewBox3 .previewCon .fbBox .typename{font-size: 18px; color: #457DFD; font-weight: bold;}
    .infoTemplate_3 .previewBox3 .previewCon .textCon{color: #273564; line-height: 26px; font-size: 15px; margin: 10px 15px 0;}
    .infoTemplate_3 .previewBox3 .previewCon .textCon p{margin-bottom: 10px; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden;}
    .infoTemplate_3 .previewBox3 .previewCon .textCon p:last-child {font-size: 12px;;}
    .infoTemplate_3 .previewBox3 {padding-bottom: 20px;}
    .infoTemplate_3 .previewCon .house-images {margin: 0 15px 20px; display: flex; align-items: center;}
    .infoTemplate_3 .previewCon .house-images img {border-radius: 4px;}
    .infoTemplate_3 .previewCon .house-images .img1 {flex: 1 1 170px; width: 170px; height: 100%!important; min-height: 133px!important; max-height: 133px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_3 .previewCon .house-images .right-img {margin-left: 10px; flex: 0 1 85px; height: 134px!important;}
    .infoTemplate_3 .previewCon .house-images .right-img .img-item {display: block; width: 85px!important; height: 64px!important; min-height: 62px!important; max-height: 62px!important; object-fit: cover; object-position: center center;}
    .infoTemplate_3 .previewCon .house-images .right-img .img-item:first-of-type {margin-bottom: 10px;}
    .infoTemplate_3 .previewCon .qrBox{display: flex; align-items: center; justify-content: space-between; margin: 15px 15px 0;}
    .infoTemplate_3 .previewCon .qrBox .left_text{display: flex; flex-direction: column;}
    .infoTemplate_3 .previewCon .qrBox .right_qr{width: 96px !important; flex-shrink: 0;}
    .infoTemplate_3 .previewCon .qrBox .right_qr img{display: block; object-fit: cover}
    .infoTemplate_3 .previewCon .qrBox .text_gzh {display: flex; align-items: center;}
    .infoTemplate_3 .previewCon .qrBox .text_gzh .line{height: 35px;}
    .infoTemplate_3 .previewCon .qrBox .text_gzh .gzh_info{display: flex; flex-direction: column; justify-content: center; }
    .infoTemplate_3 .previewCon .qrBox .text_gzh p{font-size: 10px; color: #3D6799; opacity: .8; margin: 0}
    .infoTemplate_3 .previewCon .qrBox .text_gzh .h2{font-size: 18px; color: #273564; font-weight: 500;}
    .infoTemplate_3 .previewCon .qrBox .tips{color: #273564; font-size: 15px; line-height: 26px; font-weight: 500; font-weight: bold; display: flex; align-items: flex-start; margin-top: 6px; white-space: nowrap;}
    .infoTemplate_3 .previewCon .qrBox .tips .arr{display: block; width: 13px; height: 13px; margin: 6px 0 0 5px; background: url('{#$cfg_basehost#}/static/images/admin/arr_gzh.png') no-repeat center; background-size: cover;}
    .infoTemplate_3 .previewCon .qrBox .tips .arr img,.infoTemplate_3 .previewConBox .line2  img{display: block; width: 100%; height: 100%; object-fit: cover;}


    /* 商城 */
    /* .shopTemplate_1 .previewBox1{display: none;} */
    .shopTemplate_1 .previewBox1:first-of-type{padding-top: 22px;}
    .shopTemplate_1 .previewBox1:last-of-type{padding-bottom: 32px;}
    .shopTemplate_1 .previewBox1{background: linear-gradient(130deg, #FD5E02 0%, #FD5E02 100%); min-height: 100px; padding-top:10px ;}
    .shopTemplate_1 .previewBox1 .previewConBox{margin: 0 15px; padding-bottom: 1px;}
    .shopTemplate_1 .previewBox1 .previewConBox .number{font-size: 14px; color: #FFE3BC; padding-left: 10px; opacity: .3; line-height: 1.1em;}
    .shopTemplate_1 .previewBox1 .proInfo{background: linear-gradient(180deg, rgb(255 255 255 / 47%), #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff, #ffffff); border-radius: 13px; box-shadow: 2px 4px 8px 2px rgba(191, 45, 0, 0.2); overflow: hidden;}
    .shopTemplate_1 .previewBox1 .proImg{ height: 335px;  border-radius: 13px; position: relative; overflow: hidden;}
    .shopTemplate_1 .previewBox1 .proImg .imgbox{ display: flex; align-items: center; height: 335px; }
    .shopTemplate_1 .previewBox1 .proImg .bg_front{height: 80px; /*position: absolute;*/ left: 0; right: 0; bottom: 0 ; background: linear-gradient(0deg, #FFFFFF 0%, rgba(253, 94, 2, 0) 100%); transform: translateY(-80px);}
    .shopTemplate_1 .previewBox1 .proImg .imgbox>img{ background-color: #fff; display: flex; height: 335px !important; object-fit: cover; width: 345px; flex: 1 1 345px; object-position: center;}
    .shopTemplate_1 .previewBox1 .recStarbox{ font-size: 11px; color: #fff;/* position: absolute;*/ left: 0; right: 0; bottom: 10px; padding: 0 18px; transform: translateY(-110px); height: 20px}
    .shopTemplate_1 .previewBox1 .recStarbox>section{float: left; margin-right: 4px; position: relative; border-radius: 10px; height: 20px; line-height: 20px;  overflow: hidden;  padding: 0 8px; }
    .shopTemplate_1 .previewBox1 .recStarbox .rec{font-size: 12px; width: 106px; height: 21px; line-height: 20px;  background:url('{#$cfg_basehost#}/static/images/admin/wechat/bg_star.png') no-repeat center/100% 100%; box-sizing: border-box;}
    .shopTemplate_1 .previewBox1 .recStarbox .rec em{font-style: normal;}
    .shopTemplate_1 .previewBox1 .recStarbox .click{font-size: 12px; line-height: 20px; background:url('{#$cfg_basehost#}/static/images/admin/wechat/bg_click.png') no-repeat center/100% 100%; }
    /*.shopTemplate_1 .previewBox1 .recStarbox>section span{ line-height: 20px;}
    .shopTemplate_1 .previewBox1 .recStarbox>section span,.shopTemplate_1 .previewBox1 .recStarbox>section em{position: relative; z-index: 1; font-style: normal; }
    .shopTemplate_1 .previewBox1 .recStarbox .stars{display: inline-block; width: 60px; height: 12px; background: url('{#$cfg_basehost#}/static/images/admin/wechat/star_gzh.png') no-repeat center/cover; vertical-align: middle; margin-left: 10px; margin-bottom: 2px;}
    .shopTemplate_1 .previewBox1 .recStarbox .bg_y{display: block; width: 60%; height: 20px; background: linear-gradient(90deg, #F2BB89 0%, #31231D 100%); border-radius:0 10px 10px 0; position: absolute; 
    right: 0; top: 0;}
    .shopTemplate_1 .previewBox1 .recStarbox .bg_b{display: block; width: 100px; height: 100px; background: linear-gradient(90deg, #272420 0%, #31231D 100%); position: absolute; left: -62px; transform: rotate(-60deg); top: -50px;}
    .shopTemplate_1 .previewBox1 .recStarbox .rec .bg_b{left: -76px;}
    .shopTemplate_1 .previewBox1 .recStarbox .rec .bg_y{width: 80%;}*/
    .shopTemplate_1 .previewBox1 .prodetail{margin: 0 18px;}
    .shopTemplate_1 .previewBox1 .prodetail .h2{font-size: 16px; color: #333; font-weight: bold; line-height: 20px; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
    .shopTemplate_1 .previewBox1 .prodetail .pro{overflow: hidden; display: flex; justify-content: space-between; align-items: flex-start; margin-top: 8px; padding-bottom: 10px;}
    .shopTemplate_1 .previewBox1 .prodetail .pro .proPrice{float: left; color: #FD5F02; font-size: 18px; font-weight: bold;}
    .shopTemplate_1 .previewBox1 .prodetail .pro .proPrice b{font-size: 28px; font-family: DMMIT;}
    .shopTemplate_1 .previewBox1 .prodetail .pro .shangquan{float: right; font-size: 12px; color: #808080; margin-top: 10px;}
    .shopTemplate_1 .previewBox1  .line{display: block; height: 3px; background: #fff url('{#$cfg_basehost#}/static/images/admin/wechat/dashed.png') no-repeat center/cover; margin: 0 13px; margin-top: -2px; position: relative; z-index: 1}
   
    .shopTemplate_1 .previewBox1 .recInfo{height: 105px; background: #FFF5F0; box-shadow: 0px 5px 8px 2px rgba(191, 45, 0, 0.2); border-radius: 13px; box-sizing: border-box; padding: 10px; display: flex; align-items: center; justify-content: space-between;}
    .shopTemplate_1 .previewBox1 .recInfo .recText{float: left;padding: 14px 0; padding-left: 10px; }
    .shopTemplate_1 .previewBox1 .recInfo .recText .tip{font-size: 13px; color: #000;}
    .shopTemplate_1 .previewBox1 .recInfo .recText .tip span{display: inline-block; color: #fff; font-size: 13px; font-weight: bold; padding: 0 7px; line-height: 24px; border-radius: 5px; background-color: #FD5E02; margin-left: 2px;}
    .shopTemplate_1 .previewBox1 .recInfo .recText .yhTip{font-size: 18px; font-weight: bold; color: #000; font-weight: bold; margin-top: 6px;}
    .shopTemplate_1 .previewBox1 .recInfo .recText .yhTip span{color: #FD5F02;}
    .shopTemplate_1 .previewBox1 .recInfo .qrCode{width: 84px; height: 84px; float: right; padding: 4px; box-sizing: border-box; background: linear-gradient(180deg, #fff 0%, #ffffff 100%); flex-shrink: 0;}
    .shopTemplate_1 .previewBox1 .recInfo .qrCode img{display: block; width: 100%; height: 100%; object-fit: cover;}
    @font-face {
        font-family: 'DMMIT';
        src: url('{#$cfg_basehost#}/static/fonts/DINMittelschriftStd.eot'),
        url('{#$cfg_basehost#}/static/fonts/DINMittelschriftStd.otf'),
        url('{#$cfg_basehost#}/static/fonts/DINMittelschriftStd.ttf');
    }


    /* 模板二 */
    .shopTemplate_2 .previewBox2 { background: linear-gradient(130deg, rgba(245, 43, 43, .1) 0%, rgba(239, 20, 20, .1) 100%); min-height: 100px; padding-bottom: 20px;}
    .shopTemplate_2 .previewBox2:first-of-type{padding-top: 28px;}
    .shopTemplate_2 .previewBox2:last-of-type{padding-bottom: 42px;}
    .shopTemplate_2 .previewBox2 .previewConBox{margin: 0 18px;}
    .shopTemplate_2 .previewBox2 .proInfo{ border-radius: 13px; box-shadow: 0px 0px 15px 0px rgba(227, 143, 0, 0.1); overflow: hidden; border: solid 3px #F83C2E ; box-sizing: border-box; padding-bottom: 10px; background: linear-gradient(180deg, #fff 0%, #ffffff 100%);position: relative;}
    .shopTemplate_2 .previewBox2 .proImg{height: 330px;  border-radius: 13px 13px 0 0; position: relative; overflow: hidden; z-index: 1 }
    .shopTemplate_2 .previewBox2 .proImg .bg_front{height: 70px; transform: translateY(-70px); left: 0; right: 0; bottom: 0 ; background: linear-gradient(0deg, #FFFFFF 0%, rgba(253, 94, 2, 0) 100%); }
    .shopTemplate_2 .previewBox2 .proImg .imgbox{ overflow: hidden;  display: flex; align-items: center;}
    .shopTemplate_2 .previewBox2 .proImg .imgbox>img{ display: flex; height: 335px !important; object-fit: cover; width: 345px; flex: 1 1 345px; object-position: center;}
    .shopTemplate_2 .previewBox2 .proImg .picList{overflow: hidden; /*position: absolute;*/ left: 0; right: 0; bottom: 10px; padding: 0 12px; transform: translateY(-140px);}
    .shopTemplate_2 .previewBox2 .proImg .picList .pic{width: 60px; height: 60px; border-radius: 5px; border: solid 3px #fff; box-sizing: border-box; float: left; overflow: hidden; background-color: #fff; margin-right: 5px;}
    .shopTemplate_2 .previewBox2 .proImg .picList .pic>img{display: block; height: 100%; width: 100%; object-fit: cover;}
    .shopTemplate_2 .previewBox2 .prodetail{margin: 0 18px; position: relative; z-index: 1}
    .shopTemplate_2 .previewBox2 .prodetail .h2{font-size: 16px; color: #333; font-weight: bold; line-height: 20px; display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
    .shopTemplate_2 .previewBox2 .prodetail .pro{margin-top: 8px;}
    .shopTemplate_2 .previewBox2 .prodetail .pro span{display: inline-block; padding: 0 8px; background-color: rgba(255, 93, 66, .1); border-radius: 5px; line-height: 24px; font-size: 14px; font-weight: bold;}
    .shopTemplate_2 .previewBox2 .prodetail .pro span.quan{color: #F01919;}
    .shopTemplate_2 .previewBox2 .prodetail .pro span.hot{font-size: 14px; font-family: 'SourceHanSansCN'; font-style: italic;}
    .shopTemplate_2 .previewBox2 .prodetail .pro span.hot b{color: #F01919; }
    .shopTemplate_2 .previewBox2  .line{display: block; height: 3px; background: url('{#$cfg_basehost#}/static/images/admin/wechat/dashed1.png') no-repeat center/cover; margin: 0 13px; position: relative; transform: translateY(-3px); z-index: 1}
    .shopTemplate_2 .previewBox2 .recInfo{height: 115px; background-color: #FF5D42; border-radius: 13px; padding: 16px; box-sizing: border-box; margin-top: -3px; display: flex; align-items: center; justify-content: space-between;}
    .shopTemplate_2 .previewBox2 .recInfo .qrCode{width: 84px; height: 84px; float: right; border-radius: 4px; padding: 4px; box-sizing: border-box; background: linear-gradient(180deg, #fff 0%, #ffffff 100%); flex-shrink: 0;}
    .shopTemplate_2 .previewBox2 .recInfo .qrCode img{display: block; width: 100%; height: 100%; object-fit: cover;}
    .shopTemplate_2 .previewBox2 .recInfo .price{font-size: 20px; color: #fff; font-weight: bold;}
    .shopTemplate_2 .previewBox2 .recInfo .price b{font-family: DMMIT; font-size: 32px; font-weight: normal;}
    .shopTemplate_2 .previewBox2 .recInfo .yhTip s{font-size: 13px; color: rgba(255,255,255,.5); margin-left: 6px;}
    .shopTemplate_2 .previewBox2 .recInfo .recText{padding-left: 6px;}
    .shopTemplate_2 .previewBox2 .recInfo .recText .tip{font-size: 14px; color: #fff; font-weight: bold; margin-top: 4px;}
    .shopTemplate_2 .previewBox2 .recInfo .recText .tip span{display: inline-block; line-height: 24px; color: #F01919; font-size: 14px; background-color: #fff; border-radius: 5px; margin-right: 4px; padding: 0 10px;}
    .shopTemplate_2 .previewBox2 .recInfo .recText .tip s{display: inline-block; width: 36px; height: 18px; background: url('{#$cfg_basehost#}/static/images/admin/wechat/arr_more.png') no-repeat center/cover; margin-left: 5px; vertical-align: middle; margin-bottom: 2px;}
    @font-face {
        font-family: 'SourceHanSansCN';
        src: url('{#$cfg_basehost#}/static/fonts/SourceHanSansCN-Heavy.eot'),
        url('{#$cfg_basehost#}/static/fonts/SourceHanSansCN-Heavy.woff'),
        url('{#$cfg_basehost#}/static/fonts/SourceHanSansCN-Heavy.woff2'),
        url('{#$cfg_basehost#}/static/fonts/SourceHanSansCN-Heavy.ttf');
    }


        /* 模板3 */
        .shopTemplate_3 .previewBox3:first-of-type{padding-top: 28px;}
    .shopTemplate_3 .previewBox3:last-of-type{padding-bottom: 32px;}
    .shopTemplate_3 .previewBox3{ background: linear-gradient(130deg, #F52B2B 0%, #F52B2B 100%); padding-bottom: 10px;}
    .shopTemplate_3 .previewBox3 .previewConBox{margin: 0 15px; max-height: 470px; overflow: hidden; }
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg{height: 430px; position: relative; overflow: hidden; min-width: 100%}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .imgbox{height: 430px; min-width: 100%}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .imgbox>img{ background-color: #fff; display: flex; height: 430px !important; object-fit: cover; width: 345px; flex: 1 1 100%; object-position: center; border-radius: 13px 13px 0 0; min-height: 430px; max-height: 430px}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .imgbox{ overflow: hidden; display: flex; align-items: center;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .bg_front{ height: 70px; /*position: absolute;*/ left: 0; right: 0; background: linear-gradient(0deg,  #787287 0%,rgba(120,114,135,0) 100%) ; bottom: 20px; transform: translateY(-90px);}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .prodetail{background: linear-gradient(90deg, #FF5D42 0%,rgba(255, 93, 66, 0) 100%) ; box-shadow: 0px 0px 15px 0px rgba(227, 143, 0, 0.1); opacity: 0.9; border-radius: 13px 0px 0px 13px; height: 70px; left: 10px; right: 10px;  bottom: 54px; box-sizing: border-box;padding:10px 18px ; margin:0 10px;  transform: translateY(-214px); }
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .prodetail .h2{font-size: 16px; color: #fff; font-weight: bold;  white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 100%;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .pro{margin-top: 4px; height: 32px; overflow: hidden;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .hot{display: inline-block;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .hot .shop_logo{display: inline-block; width: 18px; height: 18px; padding: 1px; background-color: #fff; border-radius: 50%; vertical-align: middle;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .hot .shop_logo img{display: block; width: 100%; height: 100%; object-fit: cover; border-radius: 50%;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .hot .shop_name{font-size: 12px; color: #fff; font-weight: bold; margin-left: 5px; vertical-align: middle; white-space: nowrap; display: inline-block; overflow: hidden; text-overflow: ellipsis;  margin-bottom: 2px ;white-space: nowrap;}
    .shopTemplate_3 .previewBox3 .previewConBox .proInfo .proImg .quan{display: inline-block; vertical-align: middle; padding: 0 8px; font-size: 14px; color: #FFE3CD; line-height: 24px; background-color: #EC2D1F; border-radius: 4px; margin-left: 6px;}
    .shopTemplate_3 .previewBox3 .previewConBox .recInfo{background: linear-gradient(180deg, #fff 0%, #ffffff 100%); border-radius: 28px 0px 13px 13px; height: 104px; transform: translateY(-66px);}
    .shopTemplate_3 .previewBox3 .recInfo .price{font-size: 20px; color: #F01919; font-weight: bold;}
    .shopTemplate_3 .previewBox3 .recInfo .price b{font-family: DMMIT; font-size: 32px; font-weight: normal;}
    .shopTemplate_3 .previewBox3 .recInfo{padding-left: 20px; padding-right: 16px;}
    .shopTemplate_3 .previewBox3 .recInfo .yhTip s{font-size: 13px; color: rgba(0,0,0,.5); margin-left: 6px;}
    .shopTemplate_3 .previewBox3 .recInfo .tip{display: inline-block; padding: 0 14px; line-height: 26px; border-radius: 13px; background-color: #2C2C3A; font-size: 14px; font-weight: bold; color: #fff; margin-top: 4px;}
    .shopTemplate_3 .previewBox3 .recInfo .recText{float: left; padding-top: 10px;}
    .shopTemplate_3 .previewBox3 .recInfo .qrCodeBox{float: right;}
    .shopTemplate_3 .previewBox3 .recInfo .qrCodeBox .qrCode{width: 84px; height: 84px; padding: 4px; box-sizing: border-box; border-radius: 5px; background: linear-gradient(180deg, #fff 0%, #ffffff 100%); margin-top: -24px; }
    .shopTemplate_3 .previewBox3 .recInfo .qrCodeBox .qrCode img{display: block; width: 100%; height: 100%; object-fit: cover;}
    .shopTemplate_3 .previewBox3 .recInfo .qrCodeBox .qrTip{color: #9F9F9F; font-size: 12px; text-align: center; position: absolute; margin-top: 12px;}
    .shopTemplate_3 .previewBox3 .recInfo .qrCodeBox .qrTip s{display: block; width: 10px; height: 10px; background:url({#$cfg_basehost#}/static/images/admin/wechat/arr_up_gzh.png) no-repeat center/cover ; position: absolute; left: 0; right: 0; top: -8px; margin: auto;}
    .shopTemplate_3 .previewBox3>.num{color: rgba(255, 227, 188, .3); font-size: 14px; font-weight: bold; padding-left: 24px; line-height: 1.1em; display: flex; min-width: 100%}
    /* 招聘模板 */
    /* 模板一 */
    em{font-style: normal !important;}
    .jobTemplate_1 .jobpreviewBox1{margin-bottom: 24px;position: relative;}
    /* .jobTemplate_1 .items{background-repeat: repeat-y; background-image: url('{#$cfg_basehost#}/static/images/admin/wechat/job/background.png'); padding: 29px 15px 50px;}   */   /* 复制到公众号中后，父级如果有background-image，子级的背景色就会被清除，这里改成纯色背景 */
    .jobTemplate_1 .items{background-color: #ededed; padding: 29px 15px 50px;}
    .to-bg{margin-top: -46px;margin-left: -16px;}
    .to-bg img{width: 183px !important; height: 65px !important;margin-top: -1px; display: block;}
    .jobTemplate_1 .jobpreviewBox1:last-child{margin-bottom: 0px;}
    .jobTemplate_1 .to-title{color: #ffffff;font-size: 17px;height: 37px;background: #56BF56;border-radius: 10px 10px 0px 0px;float: left;line-height: 37px;padding: 0px 13px;font-weight: bold;}
    .jobTemplate_1 .to-content{border-radius: 0px 20px 20px 20px;clear: both;padding: 20px 16px 25px;background-color: #ffffff;}
    .jobTemplate_1 .toc-title,.toc-more .left .need .title,.toc-more .left .salary .title{font-size: 16px;color: #000000;font-weight: bold;position: relative;display: inline-block;}
    .jobTemplate_1 .toc-title span,.toc-more .left .need .title span,.toc-more .left .salary .title span{position: relative;z-index: 1;}
    .jobTemplate_1 .toc-title s,.toc-more .left .need .title s,.toc-more .left .salary .title s{height: 5px;background-color: #56BF56;display: block;margin-top: -7px;}
    .jobTemplate_1 .toc-addr{color: #333333;font-size: 13px;margin-bottom: 3px;margin-top: -5px;}
    .jobTemplate_1 .toc-text{color: #808080;font-size: 13px;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;line-height: 20px;}
    .jobTemplate_1 .toc-more{overflow: hidden;margin-top: 25px;display: flex;justify-content: space-between;align-items: end;}
    .jobTemplate_1 .toc-more .left{display: inline-block;}
    .jobTemplate_1 .toc-more .left .need{margin-bottom: 28px;}
    .jobTemplate_1 .toc-more .left .need .item,.toc-more .left .salary .item{color: #808080;font-size: 13px;margin-top: 10px;}
    .jobTemplate_1 .toc-more .left .salary .item em{font-size: 17px;font-weight: bold;color: #000000;vertical-align: -2px;font-family: DMMIT !important;}
    .jobTemplate_1 .toc-more .left .salary .item span{color: #13BF13;}
    .jobTemplate_1 .toc-more .right{padding-bottom: 2px;}
    .jobTemplate_1 .toc-more .right .code{width: 84px;height: 84px;background-color: #ffffff;box-sizing: border-box;padding: 5px;}
    .jobTemplate_1 .toc-more .right .code img{width: 100%;height: 100%;object-fit: cover;}
    .jobTemplate_1 .toc-more .right .text{text-align: center;margin-top: -8px;}
    .jobTemplate_1 .toc-more .right .text img{width: 14px;height: 14px;object-fit: cover;}
    .jobTemplate_1 .toc-more .right .text p{color: #9F9F9F;font-size: 12px;margin-top: -2px;margin-bottom: 2px;}
    .jobTemplate_1 .j-end{text-align: center;margin-top: 21px;padding-bottom: 27px;}
    .jobTemplate_1 .j-end span{color: #505887;font-size: 18px;font-weight: 400;}
    .jobTemplate_1 .j-end p{color: #13BF13;font-size: 12px;margin-top: 5px;letter-spacing: 3px;}
    /* 模板二 */
    .jobTemplate_2{background-color: #dde6fb;}
    .jobTemplate_2 .j-topimg{display: block;}
    .jobTemplate_2 .items{background-color:#dde6fb;border-bottom: 20px solid #3C41C1;padding-bottom: 40px;}
    .jobTemplate_2 .item{padding: 2px;background: linear-gradient(to right bottom,#ffffff 25%,#E3E3EA 100%);border-radius: 15px;margin: 15px 12px;}
    .jobTemplate_2 .item:first-of-type{margin-top: -40px;position: relative;}
    .jobTemplate_2 .ji-content{display: flex;align-items: flex-end;justify-content: space-between;background: linear-gradient(90deg, #F7F7F9 0%, #FFFFFF 100%);box-sizing: border-box;padding: 20px 12px 28px;border-radius: 15px;}
    .jobTemplate_2 .ji-left{margin-right: 22px;}
    .jobTemplate_2 .jil-title{color: #3C41C1;font-size: 17px;font-weight: bold;margin-bottom: 12px;}
    .jobTemplate_2 .jil-welfare{color: #505887;font-size: 13px;margin-bottom: 16px;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;line-height: 20px;}
    .jobTemplate_2 .jil-salary{height: 37px;background: #3C41C1;border-radius: 10px;line-height: 37px;color: #ffffff;font-size: 13px;display: inline-block;padding: 0px 12px;}
    .jobTemplate_2 .jil-salary span{font-size: 20px;font-weight: bold;font-family: DMMIT !important;vertical-align: -2px;}
    .jobTemplate_2 .ji-right{margin-bottom: -6px;}
    .jobTemplate_2 .ji-right .code{width: 84px;height: 84px;background-color: #ffffff;box-sizing: border-box;padding: 5px;}
    .jobTemplate_2 .ji-right .code img{width: 100%;height: 100%;object-fit: cover;}
    .jobTemplate_2 .ji-right .text{text-align: center;margin-top: -6px;}
    .jobTemplate_2 .ji-right .text img{width: 12px;height: 12px;object-fit: cover;}
    .jobTemplate_2 .ji-right .text p{color: #9F9F9F;font-size: 12px;margin-bottom: 2px;margin-top: -2px;}
    .jobTemplate_2 .j-end{text-align: center;margin-top: 21px;padding-bottom: 27px;}
    .jobTemplate_2 .j-end span{color: #505887;font-size: 18px;font-weight: 400;}
    .jobTemplate_2 .j-end p{color: #3C41C1;font-size: 12px;margin-top: 5px;letter-spacing: 3px;}
    /* 模板三 */
    .jobTemplate_3 .j-exhibiton{display: block;height: 227px;object-fit: cover;}
    .jobTemplate_3 .j-text{background-color: #FFF5F4;padding-top: 20px;padding-bottom: 15px;}
    .jobTemplate_3 .j-text img{width: 20px;height: 20px;object-fit: cover;display: block;margin: 4px auto 0px;}
    .jobTemplate_3 .jt-title{color: #B20000;font-size: 19px;text-align: center;}
    .jobTemplate_3 .jt-title s{display: block;width: 69px;height: 3px;background: #B20000;margin: 5px auto 0px;}
    .jobTemplate_3 .jt-text{font-size: 15px;color: #B20000;line-height: 26px;text-align: center;margin-top: 10px;}
    .jobTemplate_3 .jt-text span{font-weight: bold;}
    .jobTemplate_3 .jt-divide{color: #B20000;font-size: 11px;text-align: center;background: url({#$cfg_basehost#}/static/images/admin/wechat/job/dot_circle.png) no-repeat;background-size: 100%;background-position: center;margin: 9px 13px 0px;}
    .jobTemplate_3 .j-items{background-color: #B20000;border-radius: 10px;}
    .jobTemplate_3 .j-items .item{padding: 25px 3px 0px;}
    .jobTemplate_3 .jc-title{color: #ffffff;font-size: 19px;font-style: italic;padding-left: 10px;}
    .jobTemplate_3 .jc-details{color: #FFEEE5;font-size: 11px;margin-top: 4px;padding-left: 14px;}
    .jobTemplate_3 .jc-content{border-radius: 8px;height: 128px;box-sizing: border-box;padding: 17px 10px 0px;background-color: #FFF8F4;margin-top: 12px;}
    .jobTemplate_3 .jcc-salary{color: #B20000;font-size: 12px;}
    .jobTemplate_3 .jcc-salary span{font-weight: bold;font-size: 18px;vertical-align: -2px;font-family: DMMIT;}
    .jobTemplate_3 .jcc-label{color: #500202;font-size: 13px;background: url({#$cfg_basehost#}/static/images/admin/wechat/job/label.png) no-repeat;display: inline-block;height: 24px;line-height: 24px;background-size: cover;padding: 0px 9px 0px 7px;margin-top: 6px;border-radius: 5px;}
    .jobTemplate_3 .jcc-welfare{color: #2C2C3A;font-size: 12px;line-height: 19px;margin-top: 7px;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;}
    .jobTemplate_3 .jc-content .left{height: 100%;}
    .jobTemplate_3 .jc-content .right{float: right;margin-top: -175px;}
    .jobTemplate_3 .jc-content .right .code{width: 84px;height: 84px;background-color: #ffffff;box-sizing: border-box;padding: 5px;border-radius: 5px;}
    .jobTemplate_3 .jc-content .right .code img{width: 100%;height: 100%;object-fit: cover;}
    .jobTemplate_3 .jc-content .right .text{text-align: center;margin-top: -4px;}
    .jobTemplate_3 .jc-content .right .text img{width: 10px;height: 10px;object-fit: cover;}
    .jobTemplate_3 .jc-content .right .text p{color: #9F9F9F;font-size: 12px;margin: -6px 0px 0px;}
    .jobTemplate_3 .jc-end{font-size: 13px;color: #CC5E5E;margin-top: 16px;text-align: center;padding-bottom: 16px;font-family: DMMIT;}
    .jobTemplate_3 .j-end{text-align: center;margin-top: 21px;padding-bottom: 27px;}
    .jobTemplate_3 .j-end span{color: #B20000;font-size: 18px;font-weight: 400;}
    .jobTemplate_3 .j-end p{color: #B20000;font-size: 11px;margin-top: 5px;letter-spacing: 3px;}
</style>
</head>

<body>

<form action="" method="post" name="editform" id="editform" class="editform">
    <div class="btn-group config-nav" data-toggle="buttons-radio" style="margin-bottom: 20px;">
        {#if in_array("info", $installModuleArr)#}<button type="button" class="btn active" data-type="info">{#getModuleTitle name='info'#}</button>{#/if#}
        {#if in_array("shop", $installModuleArr)#}<button type="button" class="btn {#if !in_array("info", $installModuleArr)#} active{#/if#}" data-type="shop">{#getModuleTitle name='shop'#}</button>{#/if#}
        {#if in_array("job", $installModuleArr)#}<button type="button" class="btn {#if !in_array("info", $installModuleArr) && !in_array("shop", $installModuleArr)#} active{#/if#}" data-type="job">{#getModuleTitle name='job'#}</button>{#/if#}
        <button class="btn" disabled>限时免费，更多模块持续开发中，敬请期待。</button>
    </div>
    {#if in_array("info", $installModuleArr)#}
        <dl class="clearfix">
            <dt><label for="module">城市分站：</label></dt>
            <dd style="overflow: visible; padding-left: 140px;" >
              <div class="choseCity">
                  <input type="hidden" id="info_cityid" name="info[cityid]" placeholder="请选择城市分站" value="">
              </div>
            </dd>
        </dl>
    <div class="item" data-type="info">


        <dl class="clearfix">
            <dt><label for="info_typeid">信息分类：</label></dt>
            <dd><select name="info[typeid]" id="info_typeid" class="input-large"></select></dd>
        </dl>

        <dl class="clearfix">
            <dt><label>发布时间：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="info[date]" value="" checked />不限</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[date]" value="1" />1天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[date]" value="3" />3天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[date]" value="7" />7天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[date]" value="15" />15天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[date]" value="30" />30天内</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>信息属性：</label></dt>
            <dd class="radio">
                <label><input type="checkbox" name="info[attr][]" value="rec" />推荐</label>&nbsp;&nbsp;
                <label><input type="checkbox" name="info[attr][]" value="fire" />火急</label>&nbsp;&nbsp;
                <label><input type="checkbox" name="info[attr][]" value="top" />置顶中</label>&nbsp;&nbsp;
                <label><input type="checkbox" name="info[attr][]" value="read" />有阅读红包</label>&nbsp;&nbsp;
                <label><input type="checkbox" name="info[attr][]" value="share" />有分享红包</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
          <dt><label for="info_userid">指定用户：</label></dt>
          <dd><input type="text" name="info[userid]" id="info_userid" class="input-xlarge" placeholder="用户ID，多个用逗号分开，非必填" /></dd>
        </dl>

        <dl class="clearfix">
          <dt><label for="info_ids">指定信息：</label></dt>
          <dd><textarea rows="3" class="input-xxlarge" name="info[ids]" id="info_ids" placeholder="信息ID，多个用逗号分开，非必填"></textarea></dd>
        </dl>

        <dl class="clearfix">
            <dt><label>显示顺序：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="info[orderby]" value="0" checked title="置顶、火急、推荐、自定义、发布时间" />默认</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[orderby]" value="1" />最新</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[orderby]" value="2" />浏览量</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>风格模板：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="info[template]" value="1" checked />模板一</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[template]" value="2" />模板二</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[template]" value="3" />模板三</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>二维码类型：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="info[qr]" value="1" checked />普通链接</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[qr]" value="2" />公众号</label>&nbsp;&nbsp;
                <label><input type="radio" name="info[qr]" value="3" />小程序</label>&nbsp;&nbsp;

                <div style="font-size: 12px; padding-top: 10px; color: #999; line-height: 2em;">
                    <code>普通链接：</code> 扫码后可以直接打开信息详情；<br>
                    <code>公 众 号：</code> 扫码后会先引导关注公众号，然后系统会自动发送信息链接到公众号对话框；<br>
                    <code>小 程 序：</code> 扫码后会自动打开小程序对应的信息详情页；                    
                </div>
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>信息数量：</label></dt>
            <dd>
                <div class="clearfix">
                    <label>
                        <input type="radio" class="pageType" data-type="info" name="info[pageType]" value="1" checked />
                        生成前 <input style="padding: 0 6px; width: 30px;" class="input-mini" type="text" name="info[count]" value="50" />条（共<span class="info_totalCount">100</span>条）
                    </label>&nbsp;&nbsp;
                    <label>
                        <input type="radio" class="pageType" data-type="info" name="info[pageType]" value="2" />生成指定页码&nbsp;&nbsp;
                        <select class="input-small" style="width: 90px; height: 27px; line-height: 27px; padding: 0;" name="info[pageSize]" id="info_pageSize">
                            <option value="10">每页10条</option>
                            <option value="20">每页20条</option>
                            <option value="30">每页30条</option>
                            <option value="50">每页50条</option>
                            <option value="100">每页100条</option>
                        </select>
                    </label>
                </div>

                <div id="pageInfo" class="pagination pagination-centered pageInfo_info" style="margin: 10px 0; transform:scale(.8); transform-origin: 0 0 0; text-align: left;"></div>
                <input type="hidden" id="info_page" name="info[page]" />
            </dd>
        </dl>

        <!-- <dl class="clearfix">
          <dt>&nbsp;</dt>
          <dd>
            <button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">生成模板</button>
          </dd>
        </dl> -->

    </div>
    {#/if#}
    {#if in_array("shop", $installModuleArr)#}
    <div class="item {#if in_array("info", $installModuleArr)#} fn-hide{#/if#}" data-type="shop">

        <!-- <dl class="clearfix">
            <dt><label for="module">城市分站：</label></dt>
            <dd style="overflow: visible; padding-left: 140px;" >
              <div class="choseCity">
              </div>
            </dd>
        </dl> -->
         <input type="hidden" id="shop_cityid" name="shop[cityid]" placeholder="请选择城市分站" value="">

        <dl class="clearfix">
            <dt><label for="shop_typeid">信息分类：</label></dt>
            <dd><select name="shop[typeid]" id="shop_typeid" class="input-large"></select></dd>
        </dl>

        <dl class="clearfix">
            <dt><label>发布时间：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="shop[date]" value="" checked />不限</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[date]" value="1" />1天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[date]" value="3" />3天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[date]" value="7" />7天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[date]" value="15" />15天内</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[date]" value="30" />30天内</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>信息模式：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="shop[shopstate]" value="0"  checked/>混合</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[shopstate]" value="1" />团购</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[shopstate]" value="2" />电商</label>&nbsp;&nbsp;
                
            </dd>
        </dl>

        <dl class="clearfix">
          <dt><label for="shop_userid">指定用户：</label></dt>
          <dd><input type="text" name="shop[userid]" id="shop_userid" class="input-xlarge" placeholder="用户ID，多个用逗号分开，非必填" /></dd>
        </dl>

        <dl class="clearfix">
          <dt><label for="shop_ids">指定信息：</label></dt>
          <dd><textarea rows="3" class="input-xxlarge" name="shop[ids]" id="shop_ids" placeholder="信息ID，多个用逗号分开，非必填"></textarea></dd>
        </dl>

        <dl class="clearfix">
            <dt><label>显示顺序：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="shop[orderby]" value="1" checked />发布时间</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[orderby]" value="2" />浏览量</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[orderby]" value="3" />销量</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>风格模板：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="shop[template]" value="1" checked />模板一</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[template]" value="2" />模板二</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[template]" value="3" />模板三</label>&nbsp;&nbsp;
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>二维码类型：</label></dt>
            <dd class="radio">
                <label><input type="radio" name="shop[qr]" value="1" checked />普通链接</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[qr]" value="2" />公众号</label>&nbsp;&nbsp;
                <label><input type="radio" name="shop[qr]" value="3" />小程序</label>&nbsp;&nbsp;

                <div style="font-size: 12px; padding-top: 10px; color: #999; line-height: 2em;">
                    <code>普通链接：</code> 扫码后可以直接打开信息详情；<br>
                    <code>公 众 号：</code> 扫码后会先引导关注公众号，然后系统会自动发送信息链接到公众号对话框；<br>
                    <code>小 程 序：</code> 扫码后会自动打开小程序对应的信息详情页；                    
                </div>
            </dd>
        </dl>

        <dl class="clearfix">
            <dt><label>信息数量：</label></dt>
            <dd>
                <div class="clearfix">
                    <label>
                        <input type="radio" class="pageType" data-type="shop" name="shop[pageType]" value="1" checked />
                        生成前 <input style="padding: 0 6px; width: 30px;" class="input-mini" type="text" name="shop[count]" value="50" />条（共<span class="shop_totalCount">100</span>条）
                    </label>&nbsp;&nbsp;
                    <label>
                        <input type="radio" class="pageType" data-type="shop" name="shop[pageType]" value="2" />生成指定页码&nbsp;&nbsp;
                        <select class="input-small" style="width: 90px; height: 27px; line-height: 27px; padding: 0;" name="shop[pageSize]" id="shop_pageSize">
                            <option value="10">每页10条</option>
                            <option value="20">每页20条</option>
                            <option value="30">每页30条</option>
                            <option value="50">每页50条</option>
                            <option value="100">每页100条</option>
                        </select>
                    </label>
                </div>

                <div id="pageInfo" class="pagination pagination-centered pageInfo_shop" style="margin: 10px 0; transform:scale(.8); transform-origin: 0 0 0; text-align: left;"></div>
                <input type="hidden" id="shop_page" name="shop[page]" />
            </dd>
        </dl>

        
      </div>
    {#/if#}
    {#if in_array("job", $installModuleArr)#}
    <div class="item {#if in_array("info", $installModuleArr)#} fn-hide{#/if#}" data-type="job">

    <!-- <dl class="clearfix">
        <dt><label for="module">城市分站：</label></dt>
        <dd style="overflow: visible; padding-left: 140px;" >
          <div class="choseCity">
          </div>
        </dd>
    </dl> -->
    <input type="hidden" id="job_cityid" name="job[cityid]" placeholder="请选择城市分站" value="">

    <dl class="clearfix">
        <dt><label for="job_typeid">职位类别：</label></dt>
        <dd><select name="job[typeid]" id="job_typeid" class="input-large"></select></dd>
    </dl>

    <dl class="clearfix">
        <dt><label>发布时间：</label></dt>
        <dd class="radio">
            <label><input type="radio" name="job[date]" value="" checked />不限</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[date]" value="1" />1天内</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[date]" value="3" />3天内</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[date]" value="7" />7天内</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[date]" value="15" />15天内</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[date]" value="30" />30天内</label>&nbsp;&nbsp;
        </dd>
    </dl>

    <dl class="clearfix">
        <dt><label>信息属性：</label></dt>
        <dd class="radio">
            <label><input type="checkbox" name="jobflagstate[]" value="1" />置顶中</label>&nbsp;&nbsp;
            <label><input type="checkbox" name="jobflagstate[]" value="2" />刷新中</label>&nbsp;&nbsp;
        </dd>
    </dl>

    <dl class="clearfix">
        <dt><label for="job_companyid">指定公司：</label></dt>
        <dd><input type="text" name="job[companyid]" id="job_companyid" class="input-xlarge" placeholder="公司ID，多个用逗号分开，非必填" /></dd>
    </dl>

    <dl class="clearfix">
        <dt><label for="job_ids">指定职位：</label></dt>
        <dd><textarea rows="3" class="input-xxlarge" name="job[ids]" id="job_ids" placeholder="职位ID，多个用逗号分开，非必填"></textarea></dd>
    </dl>

    <dl class="clearfix">
        <dt><label>显示顺序：</label></dt>
        <dd class="radio">
            <label><input type="radio" name="job[orderby]" value="1" checked />发布时间</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[orderby]" value="2" />浏览量</label>&nbsp;&nbsp;
        </dd>
    </dl>

    <dl class="clearfix">
        <dt><label>风格模板：</label></dt>
        <dd class="radio">
            <label><input type="radio" name="job[template]" value="1" checked />模板一</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[template]" value="2" />模板二</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[template]" value="3" />模板三</label>&nbsp;&nbsp;
        </dd>
    </dl>

    <dl class="clearfix">
        <dt><label>二维码类型：</label></dt>
        <dd class="radio">
            <label><input type="radio" name="job[qr]" value="1" checked />普通链接</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[qr]" value="2" />公众号</label>&nbsp;&nbsp;
            <label><input type="radio" name="job[qr]" value="3" />小程序</label>&nbsp;&nbsp;

            <div style="font-size: 12px; padding-top: 10px; color: #999; line-height: 2em;">
                <code>普通链接：</code> 扫码后可以直接打开信息详情；<br>
                <code>公 众 号：</code> 扫码后会先引导关注公众号，然后系统会自动发送信息链接到公众号对话框；<br>
                <code>小 程 序：</code> 扫码后会自动打开小程序对应的信息详情页；
            </div>
        </dd>
    </dl>

    <dl class="clearfix">
        <dt><label>信息数量：</label></dt>
        <dd>
            <div class="clearfix">
                <label>
                    <input type="radio" class="pageType" data-type="job" name="job[pageType]" value="1" checked />
                    生成前 <input style="padding: 0 6px; width: 30px;" class="input-mini" type="text" name="job[count]" value="50" />条（共<span class="job_totalCount">100</span>条）
                </label>&nbsp;&nbsp;
                <label>
                    <input type="radio" class="pageType" data-type="job" name="job[pageType]" value="2" />生成指定页码&nbsp;&nbsp;
                    <select class="input-small" style="width: 90px; height: 27px; line-height: 27px; padding: 0;" name="job[pageSize]" id="job_pageSize">
                        <option value="10">每页10条</option>
                        <option value="20">每页20条</option>
                        <option value="30">每页30条</option>
                        <option value="50">每页50条</option>
                        <option value="100">每页100条</option>
                    </select>
                </label>
            </div>

            <div id="pageInfo" class="pagination pagination-centered pageInfo_job" style="margin: 10px 0; transform:scale(.8); transform-origin: 0 0 0; text-align: left;"></div>
            <input type="hidden" id="job_page" name="job[page]" />
        </dd>
    </dl>


    </div>
    {#/if#}
    <dl class="clearfix">
      <dt>&nbsp;</dt>
      <dd>
        <button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">生成模板</button>
      </dd>
    </dl>
</form>

<div class="wechat_demo" id="list" data-totalpage="1" data-atpage="1">
    <div class="wechat_main1">
        <div class="wechat_main">
            <h1 class="rich_media_title">火鸟门户推文助手功能演示</h1>
            <div id="meta_content" class="rich_media_meta_list">
                <span class="rich_media_meta"><a href="https://www.kumanyun.com" target="_blank">酷曼软件</a></span>
                <em id="publish_time" class="rich_media_meta rich_media_meta_text">{#$smarty.now|date_format:'%Y-%m-%d %H:%M'#}</em>
            </div>
            <!-- 正文内容 -->
            <div id="wechatPreview">
                <div class="wechat_empty">请先点击左边的【生成模板】按钮</div>
            </div>
        </div>
    </div>
    <button class="btn btn-large btn-primary" type="button" id="copyWechat">一键复制</button>
</div>

<script id="infoTemplate_1" type="text/html">
    <section class="previewBox1">
        <section class="bg_white previewConBox">
          <section class="previewCon">
            <section class="titleBox">
              <span>{$typename}</span>
              <s class="arr"></s>
            </section>
            <section class="textCon">
              <p>{$desc}</p>
            </section>
            <section class="fbInfo">
              <section class="fb_left">
                <section class="headIcon"><img src="{$photo}" onerror="this.src='{#$cfg_basehost#}/static/images/noPhoto_60.jpg'" alt=""></section>
                <span>{$nickname}</span>
              </section>
              <section class="fb_right">
                <span>{$address}</span>
              </section>
            </section>
            {$pics}
            <section class="qrBox">
              <section class="left_text">
                <p>长按二维码了解更多<br/>
                ……{$click}人感兴趣</p>
                <p>{#$cfg_shortname#} | 本地生活信息</p>
              </section>
              <section class="arr"></section>
              <section class="right_qr">
                <img src="{$qr}" alt="">
              </section>
            </section>
          </section>
        </section>
    </section>
</script>

<script id="infoTemplate_2" type="text/html">
    <section class="previewBox2">
        <section class="previewConBox ">
          <span class="title_text">{$typename}</span>
          <section class="bg_wrap">
            <section class="previewCon">
              <section class="titleBox">
                <section class="title_bg">
                  <section class="flexWrap">
                  <span>{$typename}</span>
                  NO.{$i}</section>
                </section>
              </section>
              <section class="white_con">
                <section class="fbBox">
                  <s class="line"></s><span>{$nickname}</span><em>（{$address}）</em>
                </section>
                <section class="textCon">
                  <p>{$desc}</p>
                </section>
                {$pics}
                <section class="qrBox">
                  <section class="left_text">
                    <section class="text_gzh">
                      <s class="line"></s>
                      <section class="gzh_info">
                        <section class="h2">{#$cfg_shortname#}</section>
                        <p>本/地/生/活/分/类/信/息</p>
                      </section>
                    </section>
                    <section class="tips">
                      <section>长按二维码识别<br>了解更多</section>
                      <s class="arr"></s>
                    </section>
                  </section>
                  <section class="right_qr"><img src="{$qr}" alt=""></section>
                </section>
              </section>
            </section>
          </section>
        </section>
    </section>
</script>

<script id="infoTemplate_3" type="text/html">
  <section class="previewBox3">
    <section class="bg_wrap previewConBox">
      <section class="previewCon">
        <section class="fbBox">
          <span class="titleText">{$nickname}</span>
          <span class="typename">#{$typename}</span>
        </section>
        <section class="textCon">
          <p>{$desc}</p>
          <p>({$address})</p>
        </section>
        {$pics}
        <section class="qrBox">
          <section class="left_text">
            <section class="text_gzh">
              <s class="line"></s>
              <section class="gzh_info">
                <section class="h2">{#$cfg_shortname#}</section>
                <p>本/地/生/活/分/类/信/息</p>
              </section>
            </section>
            <section class="tips">
              <section>长按二维码识别<br>了解更多</section>
              <s class="arr"></s>
            </section>
          </section>
          <section class="right_qr"><img src="{$qr}" alt=""></section>
        </section>
      </section>
    </section>
  </section>
</script>

<!-- 商城模板一 -->
<script id="shopTemplate_1" type="text/html">
  <section class="previewBox1">
      <section class="previewConBox">
          <section class="number">NO.{$i}</section>
          <section class="proInfo">
              <section class="proImg">
                <section class="imgbox"><img src="{$litpic}" alt=""></section>
                  
                  <section class="bg_front"></section>
                  <section class="recStarbox">
                      <!-- <section class="rec">
                          <span><em>推荐</em><s class="bg_b"></s></span><span class="stars"></span>
                          <s class="bg_y"></s>
                      </section>
                      <section class="click">
                          <span><em>{$click}人</em><s class="bg_b"></s></span><span>关注</span>
                          <s class="bg_y"></s>
                      </section> -->
                      <section class="rec">
                      	<em>推荐</em>
                      </section>
                      <section class="click">{$click}人关注</section>
                  </section>
              </section>
              <section class="prodetail">
                  <section class="h2"><span class="shopname">{$shopname} </span> <span class="proTitle">{$title}</span></section>
                  <section class="pro">
                      <section class="proPrice">￥<b>{$price}</b></section>
                      <!-- <section class="shangquan">湖东邻里中心</section> -->
                  </section>
              </section>
          </section>
          <section class="line"></section>
          <section class="recInfo">
              <section class="recText">
                  <section class="tip">长按识别二维码<span>立即抢购</span></section>
                  <section class="yhTip">原价<span>{$mprice1}元</span>优惠套餐</section>
              </section>
              <section class="qrCode"><img src="{$qr}" alt=""></section>
          </section>
      </section>
  </section>
</script>

<!-- 模板二 -->
<script id="shopTemplate_2" type="text/html">
  <section class="previewBox2">
      <section class="previewConBox">
          <section class="proInfo">
              <section class="proImg">
                  <section class="imgbox"><img src="{$litpic}" alt=""></section>
                  <section class="bg_front"></section>
                  <section class="picList">
                      {$pics}
                  </section>
              </section>
              <section class="prodetail">
                  <section class="h2"><span class="proTitle">{$title}</span></section>
                  <section class="pro">
                      <span class="hot"><b>HOT</b> <em>热卖中</em></span>
                      {$quan}
                  </section>
              </section>
          </section>
          <section class="line"></section>
          <section class="recInfo">
              <section class="recText">
                  <section class="yhTip"><span class="price">￥<b>{$price}</b></span>{$mprice}</section>
                  <section class="tip"><span>好物推荐</span>长按识别二维码<s></s></section>
                  
              </section>
              <section class="qrCode"><img src="{$qr}" alt=""></section>
          </section>
      </section>
  </section>
</script>

<!-- 模板3 -->
<script id="shopTemplate_3" type="text/html">
      <section class="previewBox3">
          <section class="num">NO.{$i}</section>
          <section class="previewConBox">
              <section class="proInfo">
                  <section class="proImg">
                    <section class="imgbox"> <img src="{$litpic}" alt=""></section>
                      <section class="bg_front"></section>
                      <section class="prodetail">
                          <section class="h2"><span class="proTitle">{$title}</span></section>
                          <section class="pro">
                              <section class="hot"><span class="shop_logo"><img src="{$storeLogo}" alt=""></span><span class="shop_name">{$storeTitle}</span></section>
                              {$quan}
                          </section>
                      </section>
                  </section>
              
              </section>
              <section class="recInfo">
                  <section class="recText">
                      <section class="yhTip"><span class="price">￥<b>{$price}</b></span>{$mprice}</section>
                      <section class="tip">好物推荐 下单立享优惠</section>
                      
                  </section>
                  <section class="qrCodeBox">
                      <section class="qrCode"><img src="{$qr}" alt=""></section>
                      <section class="qrTip"><s></s>长按识别二维码</section>
                  </section>
              </section>
          </section>
      </section>
  </script>

<!-- 招聘模板 -->
<script id="jobTemplate_1" type="text/html">
    <section class="jobpreviewBox1">
        <section class="to-title">{$title}</section>
        <section class="to-content">
            <section class="toc-title">
                <section style="display:inline-block"><span>{$ctitle}</span><s></s></section>
                <section class="to-bg"><img src="{#$cfg_basehost#}/static/images/admin/wechat/job/bgcolor1.png"></section>
            </section>
            <section class="toc-addr">{$addressDetail}招{$number}人</section>
            <section class="toc-text">{$welfare}</section>
            <section class="toc-more">
                <section class="left">
                    <section class="need">
                        <section class="title"><span>任职要求</span><s></s></section>
                        <section class="item">{$educational_name}，{$experience_name}工作经验</section>
                    </section>
                    <section class="salary">
                        <section class="title"><span>薪资待遇</span><s></s></section>
                        <section class="item">
                            <em>{$show_salary}</em>{$salary_unit}<span>{$dy_salary}</span>
                        </section>
                    </section>
                </section>
                <section class="right">
                    <section class="code"><img src="{$qr}" alt=""></section>
                    <section class="text">
                        <img src="{#$cfg_basehost#}/static/images/admin/wechat/job/up_arrow_gray.png">
                        <p>长按了解更多</p>
                    </section>
                </section>
            </section>
        </section>
    </section>
  </script>
<script id="jobTemplate_2" type="text/html">
    <section class="item">
        <section class="ji-content">
            <section class="ji-left">
                <section class="jil-title">{$title}</section>
                <section class="jil-welfare">{$welfare}</section>
                <section class="jil-salary"><span>{$show_salary}</span>{$salary_unit}</section>
            </section>
            <section class="ji-right">
                <section class="code"><img src="{$qr}"></section>
                <section class="text">
                    <img src="{#$cfg_basehost#}/static/images/admin/wechat/job/up_arrow_gray.png">
                    <p>长按了解更多</p>
                </section>
            </section>
        </section>
    </section>
</script>
<script id="jobTemplate_3" type="text/html">
    <section class="item">
        <section class="jc-title">#{$title}</section>
        <section class="jc-details">招{$number}人，{$educational_name}，{$experience_name}经验</section>
        <section class="jc-content">
            <section class="left">
                <section class="jcc-salary"><span>{$show_salary}</span>{$salary_unit}</section>
                <section class="jcc-label">福利待遇</section>
                <section class="jcc-welfare">{$welfare}</section>
            </section>
            <section class="right">
                <section class="code"><img src="{$qr}" alt=""></section>
                <section class="text">
                    <img src="{#$cfg_basehost#}/static/images/admin/wechat/job/up_arrow-grey.png">
                    <p>长按了解更多</p>
                </section>
            </section>
        </section>
    </section>
</script>
{#$jsFile#}
</body>
</html>
