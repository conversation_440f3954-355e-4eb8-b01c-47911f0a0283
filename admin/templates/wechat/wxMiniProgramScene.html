<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>小程序码</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
<style>
    .wxScenelist {border-top: 1px solid #eaeaea; padding: 10px;}
    .wxScenelist .loading {text-align: center; line-height: 300px;}
    .item {position:relative; float:left; width:225px; overflow:hidden; padding:10px; margin:0 15px 15px 0; border:1px solid #ccc;}
    .item:hover {box-shadow:0 0 15px #ccc;}
    .item.selected {background:#ffffd5;}
    .item.blur img {-webkit-filter: blur(50px); -moz-filter: blur(50px);-ms-filter: blur(50px); filter: blur(50px);}
    .item.selected .check {background-position: -144px -74px;}
    .item img {width: 225px; height: 225px; display: block; background: #f5f5f5;}
    .item input {width: 94%; margin: 5px 0; cursor: text!important; background: #fff!important;}
    .item span {float: left;}
    .item em {float: right; font-style: normal;}
    .item .oper {position: absolute; left: 0; right: 0; top: 0; height: 25px; background: rgba(255, 255, 255, .8);}
    .item .oper .check {float: left; vertical-align: middle; margin: 5px 0 0 5px;}
    .item .oper .del {float: right; width: 20px; height: 20px; display: inline-block; vertical-align: middle; margin: 3px 3px 0 0; text-indent: -999em; background: url(../../../static/images/admin/pubIcon.png?v=5) no-repeat; background-position: -186px -9px; vertical-align: middle; cursor: pointer;}
</style>
</head>

<body>
<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
    <div class="f-left">
        <div class="btn-group" id="selectBtn">
            <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
            <ul class="dropdown-menu">
                <li><a href="javascript:;" data-id="1">全选</a></li>
                <li><a href="javascript:;" data-id="0">不选</a></li>
            </ul>
        </div>
        <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
        <a href="javascript:;" class="btn btn-primary" id="addNew">创建小程序码</a>
        <a href="javascript:;" class="orderby" style="margin-left: 50px;">按访问次数排序</a>
    </div>
    <div class="f-right">
        <div class="btn-group" id="pageBtn" data-id="20">
            <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
            <ul class="dropdown-menu pull-right">
                <li><a href="javascript:;" data-id="20">每页20条</a></li>
                <li><a href="javascript:;" data-id="30">每页30条</a></li>
                <li><a href="javascript:;" data-id="50">每页50条</a></li>
                <li><a href="javascript:;" data-id="100">每页100条</a></li>
            </ul>
        </div>
        <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
        <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
        <div class="btn-group" id="paginationBtn">
            <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
            <ul class="dropdown-menu" style="left:auto; right:0;">
                <li><a href="javascript:;" data-id="1">第1页</a></li>
            </ul>
        </div>
    </div>
</div>

<div class="wxScenelist clearfix" id="list" data-totalpage="1" data-atpage="1">
    <div class="loading">加载中...</div>
</div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<script id="addForm" type="text/html">
    <form action="###" class="quick-editForm smsForm" name="smsForm" onsubmit="return false;">
      <dl class="clearfix" style="padding: 50px 0;">
        <dt>链接地址：</dt>
        <dd><input class="input-xxlarge" type="text" name="url" id="url" value="" /></dd>
      </dl>
    </form>
</script>

<div class="hide">
    <span id="sKeyword"></span>
</div>

{#$jsFile#}
</body>
</html>
