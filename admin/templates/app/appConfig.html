<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>APP基本设置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", action = '{#$action#}', modelType = action = "siteConfig";
var imglist = {
    "android_guide": {#$android_guide#},
    "ios_guide": {#$ios_guide#}
};
</script>
<style media="screen">
    .editform dt {width: 200px;}
    .tpl-list.touch{margin-bottom: 20px;margin-top: 0px;margin-left: 0px;padding-left:0px;}
    #bottomButton img {width: 50px!important; height: 50px!important; display: block; margin: 0 auto;}
    #bottomButton .reupload {margin: 0 auto;}
    #bottomButton .table {width: auto;}
    #bottomButton .table th {min-width: 100px; height: 30px; text-align: center; line-height: 30px;}
    #bottomButton .table td {text-align: center; height: 34px; line-height: 31px;}
    .disabled .disabled {color: red;}
</style>
</head>

<body>
{#if $installWaimai#}
<div class="btn-group config-nav" data-toggle="buttons-radio" style="margin-left: 160px;">
  <button type="button" class="btn active" data-type="portal">门户平台</button>
  <button type="button" class="btn" data-type="business">商家版</button>
  <button type="button" class="btn" data-type="peisong">配送员</button>
</div>
{#/if#}

<form action="appConfig.php" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <div class="item" id="portal">
    <dl class="clearfix">
      <dt><label for="appname">APP名称：</label></dt>
      <dd><input class="input-medium" type="text" name="appname" id="appname" value="{#$appname#}" /></dd>
    </dl>
	<dl class="clearfix">
      <dt><label for="subtitle">描述（推广下载使用）：</label></dt>
      <dd><input class="input-xxlarge" type="text" name="subtitle" id="subtitle" value="{#$subtitle#}" /></dd>
    </dl>
	<dl class="clearfix">
		<dt><label>下载提示：</label></dt>
		<dd class="clearfix">
		  <label>
			<input id="downloadtips" name="downloadtips" value="1" type="checkbox"{#if $downloadtips#} checked{#/if#} />
			<span class="add-on">非APP端页面显示APP下载提示信息</span>
		  </label>
	  </dd>
	</dl>
	<dl class="clearfix">
		<dt><label>不同意隐私政策：</label></dt>
		<dd class="clearfix">
		  <label>
			<input id="disagreePrivacy" name="disagreePrivacy" value="1" type="checkbox"{#if $disagreePrivacy#} checked{#/if#} />
            <span class="add-on">强制退出APP</span>
        </label>
        <span class="input-tips" style="display:inline-block;"><s></s>安卓端上架应用市场审核时，请根据不同平台规则进行勾选！</span>
	  </dd>
	</dl>
    <dl class="clearfix">
      <dt><label>最新版本：</label></dt>
      <dd class="clearfix">
    		<div class="input-prepend">
          <span class="add-on">Android：</span>
          <input class="span2" id="android_version" name="android_version" type="text" value="{#$android_version#}" style="width: 120px;" />
        </div>
    		<div class="input-prepend">
          <span class="add-on">iOS：</span>
          <input class="span2" id="ios_version" name="ios_version" type="text" value="{#$ios_version#}" style="width: 120px;" />
        </div>
        <label>
          <input id="ios_shelf" name="ios_shelf" value="1" type="checkbox"{#if $ios_shelf#} checked{#/if#} />
          <span class="add-on">APP上架中</span>
          <span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>开启APP上架中将隐藏会员充值、提现等相关页面或入口</span>
        </label>
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>更新时间：</label></dt>
      <dd class="clearfix">
        <div class="input-prepend">
          <span class="add-on">Android：</span>
          <input class="span2 updateDate" id="android_update" name="android_update" type="text" value="{#$android_update#}" style="width: 120px;" />
        </div>
        <div class="input-prepend">
          <span class="add-on">iOS：</span>
          <input class="span2 updateDate" id="ios_update" name="ios_update" type="text" value="{#$ios_update#}" style="width: 120px;" />
        </div>
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>强制升级：</label></dt>
      <dd class="clearfix">
        <label>
          <input id="android_force" name="android_force" value="1" type="checkbox"{#if $android_force#} checked{#/if#} />
          <span class="add-on">Android</span>
        </label>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <label>
          <input id="ios_force" name="ios_force" value="1" type="checkbox"{#if $ios_force#} checked{#/if#} />
          <span class="add-on">iOS</span>
        </label>
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>更新内容：</label></dt>
      <dd class="clearfix">
    		<div class="input-prepend">
          <span class="add-on" style="vertical-align: top!important;">Android：</span>
          <textarea class="input-xlarge" style="height: 100px;" name="android_note" id="android_note">{#$android_note#}</textarea>
        </div>
    		<div class="input-prepend">
          <span class="add-on" style="vertical-align: top!important;">iOS：</span>
          <textarea class="input-xlarge" style="height: 100px;" name="ios_note" id="ios_note">{#$ios_note#}</textarea>
        </div>
    	</dd>
    </dl>
      <dl class="clearfix">
        <dt><label>文件大小：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2" id="android_size" name="android_size" type="text" value="{#$android_size#}" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>LOGO：<br /><small>尺寸：180*180</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="thumb clearfix listImgBox">
    		<div class="uploadinp filePicker thumbtn{#if $logo != ""#} hide{#/if#}" id="filePicker3" data-type="logo" data-count="1" data-size="5120" data-imglist=""><div></div><span></span></div>
            {#if $logo != ""#}
    		<ul id="listSection3" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_3_1"><a href='{#$cfg_attachment#}{#$logo#}' target="_blank" title=""><img style="max-width: 150px;" alt="" src="{#$cfg_attachment#}{#$logo#}" data-val="{#$logo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
    		{#else#}
    		<ul id="listSection3" class="listSection thumblist clearfix"></ul>
    		{#/if#}
    		<input type="hidden" name="logo" value="" class="imglist-hidden" id="logo">
    	</dd>
      </dl>
      <dl class="clearfix" id="tplList">
          <dt><label>首页模板设置：<br /><small></small>&nbsp;&nbsp;&nbsp;</label></dt>
          <dd>
        <div class="tpl-list touch">
          <ul class="clearfix">
            {#foreach from=$touchTplList item=tplItem#}
            <li class="{#if $touchTemplate == $tplItem.directory#} current{#/if#}{#if $disabledModuleArr && in_array($tplItem.directory, $disabledModuleArr)#} disabled{#/if#}">
              <a href="javascript:;" {#if $tplItem.tplname == 'diy'#}style="cursor: default;"{#/if#} data-id="{#$tplItem.directory#}" data-title="{#$tplItem.tplname#}" class="img" title="模板名称：{#if $tplItem.tplname == 'diy'#}DIY模板{#else#}{#$tplItem.tplname#}{#/if#}&#10;版权所有：{#$tplItem.copyright#}"><img src="{#if $tplItem.tplname == 'diy'#}/static/images/admin/diy_template_icon.png?v={#$cfg_staticVersion#}{#else#}{#$adminPath#}../static/images/admin/platform/app/{#$tplItem.directory#}/preview.jpg?v={#$cfg_staticVersion#}{#/if#}" /></a>
              <p>
                {#if $tplItem.tplname != 'diy'#}
                <span title="{#$tplItem.tplname#}">{#$tplItem.tplname#}({#$tplItem.directory#})</span><br />
                {#else#}
                <span>DIY模板</span><br />
                {#/if#}
                <a href="javascript:;" class="choose">{#if $touchTemplate == $tplItem.directory#}取消首页{#else#}设为首页{#/if#}</a>
                {#if $tplItem.tplname == 'diy'#}
                <br /><a href="../siteConfig/sitePageDiy.php?platform=app" target="_blank" class="edit">装修页面</a>
                {#/if#}
                <!--
                {#if $tplItem.directory != 'skin1' && $tplItem.directory != 'skin2' && $tplItem.directory != 'skin3' && $tplItem.directory != 'skin4' && $tplItem.directory != 'skin5' && $tplItem.directory != 'skin6' && $tplItem.directory != 'skin7' && $tplItem.directory != 'skin8' && $tplItem.directory != 'skin9' && $tplItem.directory != 'skin10'#}
                <br /><a href="javascript:;" class="disabled">{#if $disabledModuleArr && in_array($tplItem.directory, $disabledModuleArr)#}已禁用{#else#}禁用{#/if#}</a>
                {#/if#}
                -->
              </p>
            </li>
            {#/foreach#}
          </ul>
          <input type="hidden" name="touchTemplate" id="touchTemplate" value="{#$touchTemplate#}" />
          <input type="hidden" name="disabledModule" id="disabledModule" value="{#$disabledModule#}" />
        </div>
        <p class="help-inline">备注：<br />1. 设为首页代表APP首页将使用选择的模板/模块显示！<br />2. 禁用APP原生页面请到【<a href="siteConfig/siteFooterBtn.php" id="bottomButton">手机底部导航</a>】中设置；</p>
      </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="android_download">Android安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="android_download" id="android_download" value="{#$android_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="yyb_download">应用宝链接地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="yyb_download" id="yyb_download" value="{#$yyb_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="ios_download">iOS安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="ios_download" id="ios_download" value="{#$ios_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label>Android引导页：<br /><small>尺寸：720*1280</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="listImgBox hide">
    		<div class="list-holder">
    			<ul id="listSection0" class="clearfix listSection piece"></ul>
    			<input type="hidden" name="android_guide" value='{#$android_guide#}' class="imglist-hidden">
    		</div>
    		<div class="btn-section clearfix">
    			<div class="uploadinp filePicker" id="filePicker0" data-type="single" data-count="10" data-size="5120" data-imglist="android_guide"><div id="flasHolder0"></div><span>添加图片</span></div>
    			<div class="upload-tip">
    				<p><a href="javascript:;" class="hide deleteAllAtlas">删除所有</a>&nbsp;&nbsp;{#$atlasType|replace:"*.":""|replace:";":"、"#}&nbsp;&nbsp;单张最大5M<span class="fileerror"></span></p>
    			</div>
    		</div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>iOS引导页：<br /><small>尺寸：1125*2436</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="listImgBox hide">
    		<div class="list-holder">
    			<ul id="listSection1" class="clearfix listSection piece"></ul>
    			<input type="hidden" name="ios_guide" value='{#$ios_guide#}' class="imglist-hidden">
    		</div>
    		<div class="btn-section clearfix">
    			<div class="uploadinp filePicker" id="filePicker1" data-type="single" data-count="10" data-size="5120" data-imglist="ios_guide"><div id="flasHolder1"></div><span>添加图片</span></div>
    			<div class="upload-tip">
    				<p><a href="javascript:;" class="hide deleteAllAtlas">删除所有</a>&nbsp;&nbsp;{#$atlasType|replace:"*.":""|replace:";":"、"#}&nbsp;&nbsp;单张最大5M<span class="fileerror"></span></p>
    			</div>
    		</div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>开屏广告：<br /><small>尺寸：640*1136</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="thumb clearfix listImgBox">
    		<div class="uploadinp filePicker thumbtn{#if $ad_pic != ""#} hide{#/if#}" id="filePicker2" data-type="card" data-count="1" data-size="5120" data-imglist=""><div></div><span></span></div>
            {#if $ad_pic != ""#}
    		<ul id="listSection2" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_2_1"><a href='{#$cfg_attachment#}{#$ad_pic#}' target="_blank" title=""><img style="max-width: 150px;" alt="" src="{#$cfg_attachment#}{#$ad_pic#}" data-val="{#$ad_pic#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
    		{#else#}
    		<ul id="listSection2" class="listSection thumblist clearfix"></ul>
    		{#/if#}
    		<input type="hidden" name="ad_pic" value="" class="imglist-hidden" id="ad_pic">
            <br /><br />
            <input class="input-xxlarge" type="text" name="ad_link" id="ad_link" value="{#$ad_link#}" placeholder="广告链接，留空表示没有链接" />
            <div class="input-prepend input-append" style="display: block; margin-top: 15px;">
              <span class="add-on">广告倒计时</span>
              <input class="span1" id="ad_time" name="ad_time" type="number" min="1" value="{#$ad_time#}" />
              <span class="add-on">秒</span>
            </div>
            <div class="input-prepend input-append" style="display: block; margin-top: 15px;">
                <span class="add-on">优量汇媒体ID</span>
                <input class="span2" id="ad_TencentGDT_android_app_id" name="ad_TencentGDT_android_app_id" type="text" value="{#$ad_TencentGDT_android_app_id#}" />
                <span class="add-on">广告ID</span>
                <input class="span3" id="ad_TencentGDT_android_placement_id" name="ad_TencentGDT_android_placement_id" type="text" value="{#$ad_TencentGDT_android_placement_id#}" />
                <span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>只在安卓端生效，如果自定义广告和优量汇都配置，将优先显示优量汇广告。</span>
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="android_index">Android首页地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="android_index" id="android_index" value="{#if $android_index#}{#$android_index#}{#else#}{#$cfg_basehost#}/{#/if#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="ios_index">iOS首页地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="ios_index" id="ios_index" value="{#if $ios_index#}{#$ios_index#}{#else#}{#$cfg_basehost#}/{#/if#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="ios_test">iOS首页测试地址1：</label></dt>
        <dd>
			<input class="input-xxlarge" type="text" name="ios_test" id="ios_test" value="{#if $ios_test#}{#$ios_test#}{#else#}{#$cfg_basehost#}/{#/if#}" />
			<span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>此项非专业人员请勿修改！！！</span>
		</dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="ios_test_1">iOS首页测试地址2：</label></dt>
        <dd>
			<input class="input-xxlarge" type="text" name="ios_test_1" id="ios_test_1" value="{#if $ios_test_1#}{#$ios_test_1#}{#else#}{#$cfg_basehost#}/{#/if#}" />
			<span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>此项非专业人员请勿修改！！！</span>
		</dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="ios_test_1">iOS首页测试地址3：</label></dt>
        <dd>
			<input class="input-xxlarge" type="text" name="ios_test_2" id="ios_test_2" value="{#if $ios_test_2#}{#$ios_test_2#}{#else#}{#$cfg_basehost#}/{#/if#}" />
			<span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>此项非专业人员请勿修改！！！</span>
		</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>默认地图：</label></dt>
        <dd class="clearfix">
          <label><input type="radio" name="map_set" value="2" {#if $map_set == 2#}checked="checked"{#/if#}>百度</label>&nbsp;&nbsp;
		  <label><input type="radio" name="map_set" value="1" {#if $map_set == 1#}checked="checked"{#/if#}>谷歌</label>&nbsp;&nbsp;
		  <label><input type="radio" name="map_set" value="4" {#if $map_set == 4#}checked="checked"{#/if#}>高德</label>
          <span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>密钥封装在APP源码中，请在申请打包时填写好<a href="https://www.kumanyun.com/my/app.html" target="_blank">APP机密信息</a>，如要修改，需重新打包APP！</span>
        </dd>
      </dl>
	  <dl class="clearfix">
		  <dt><label>H5打开APP功能：</label></dt>
		  <dd class="clearfix">
			  <div class="input-prepend">
				  <span class="add-on">微信开放平台移动应用AppID</span>
				  <input class="span4" id="wx_appid" name="wx_appid" type="text" value="{#$wx_appid#}" />
			  </div>
			  <br />
			  <div class="input-prepend">
				  <span class="add-on">Android URLScheme</span>
				  <input class="span4" id="URLScheme_Android" name="URLScheme_Android" type="text" value="{#$URLScheme_Android#}" />
			  </div>
			  <br />
			  <div class="input-prepend">
				  <span class="add-on">iOS URLScheme</span>
				  <input class="span4" id="URLScheme_iOS" name="URLScheme_iOS" type="text" value="{#$URLScheme_iOS#}" />
			  </div>
			  <br />
			  <small>配置教程：<a href="https://help.kumanyun.com/help-192-715.html" target="_blank">https://help.kumanyun.com/help-192-715.html</a></small>
		  </dd>
	  </dl>
	  <dl class="clearfix">
		  <dt><label>本机号码一键登录：</label></dt>
		  <dd class="clearfix">
			  <div>
				  <label style="margin-right: 15px;"><input type="radio" name="umeng_phoneLoginState" value="0" {#if $umeng_phoneLoginState == 0#}checked="checked"{#/if#}>禁用</label>
				  <label><input type="radio" name="umeng_phoneLoginState" value="1" {#if $umeng_phoneLoginState == 1#}checked="checked"{#/if#}>启用</label>
			  </div>
			  <div class="input-prepend">
				  <span class="add-on">阿里云AppKey</span>
				  <input class="span4" id="umeng_aliyunAppKey" name="umeng_aliyunAppKey" type="text" value="{#$umeng_aliyunAppKey#}" />
			  </div>
			  <br />
			  <div class="input-prepend">
				  <span class="add-on">阿里云AppSecret</span>
				  <input class="span4" id="umeng_aliyunAppSecret" name="umeng_aliyunAppSecret" type="text" value="{#$umeng_aliyunAppSecret#}" />
			  </div>
			  <br />
			  <div class="input-prepend">
				  <span class="add-on">友盟安卓AppKey</span>
				  <input class="span4" id="umeng_androidAppKey" name="umeng_androidAppKey" type="text" value="{#$umeng_androidAppKey#}" />
			  </div>
			  <br />
			  <div class="input-prepend">
				  <span class="add-on">友盟苹果AppKey</span>
				  <input class="span4" id="umeng_iosAppKey" name="umeng_iosAppKey" type="text" value="{#$umeng_iosAppKey#}" />
			  </div>
			  <br />
			  <small>配置教程：<a href="https://help.kumanyun.com/help-192-717.html" target="_blank">https://help.kumanyun.com/help-192-717.html</a></small>
		  </dd>
	  </dl>
      <dl class="clearfix hide">
        <dt><label>融云设置：</label></dt>
        <dd class="clearfix">
          <div class="input-prepend">
            <span class="add-on">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Access Key ID</span>
            <input class="span4" id="rongKeyID" name="rongKeyID" type="text" value="{#$rongKeyID#}" />
          </div>
          <br />
          <div class="input-prepend">
            <span class="add-on">Access Key Secret</span>
            <input class="span4" id="rongKeySecret" name="rongKeySecret" type="text" value="{#$rongKeySecret#}" />
          </div>
          <label>请填写生产环境App Key；<a href="http://www.rongcloud.cn" target="_blank">申请地址：http://www.rongcloud.cn</a></label>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label class="sl">版权信息：</label></dt>
        <dd>
          <script id="copyright" name="copyright" type="text/plain" style="width:95.4%;height:200px">{#$copyright#}</script>
        </dd>
      </dl>
  </div>
  <div class="item hide" id="business">
    <dl class="clearfix">
      <dt><label for="business_appname">APP名称：</label></dt>
      <dd><input class="input-medium" type="text" name="business_appname" id="business_appname" value="{#$business_appname#}" /></dd>
    </dl>
      <dl class="clearfix">
        <dt><label>最新版本：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
          <span class="add-on">Android：</span>
          <input class="span2" id="business_android_version" name="business_android_version" type="text" value="{#$business_android_version#}" style="width: 120px;" />
        </div>
    		<div class="input-prepend">
          <span class="add-on">iOS：</span>
          <input class="span2" id="business_ios_version" name="business_ios_version" type="text" value="{#$business_ios_version#}" style="width: 120px;" />
        </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>更新时间：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2 updateDate" id="business_android_update" name="business_android_update" type="text" value="{#$business_android_update#}" style="width: 120px;" />
            </div>
    		<div class="input-prepend">
              <span class="add-on">iOS：</span>
              <input class="span2 updateDate" id="business_ios_update" name="business_ios_update" type="text" value="{#$business_ios_update#}" style="width: 120px;" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>强制升级：</label></dt>
        <dd class="clearfix">
          <label>
            <input id="business_android_force" name="business_android_force" value="1" type="checkbox"{#if $business_android_force#} checked{#/if#} />
            <span class="add-on">Android</span>
          </label>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <label>
            <input id="business_ios_force" name="business_ios_force" value="1" type="checkbox"{#if $business_ios_force#} checked{#/if#} />
            <span class="add-on">iOS</span>
          </label>
      	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>更新内容：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on" style="vertical-align: top!important;">Android：</span>
              <textarea class="input-xlarge" style="height: 100px;" name="business_android_note" id="business_android_note">{#$business_android_note#}</textarea>
            </div>
    		<div class="input-prepend">
              <span class="add-on" style="vertical-align: top!important;">iOS：</span>
              <textarea class="input-xlarge" style="height: 100px;" name="business_ios_note" id="business_ios_note">{#$business_ios_note#}</textarea>
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>文件大小：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2" id="business_android_size" name="business_android_size" type="text" value="{#$business_android_size#}" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>LOGO：<br /><small>尺寸：180*180</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="thumb clearfix listImgBox">
        <div class="uploadinp filePicker thumbtn{#if $business_logo != ""#} hide{#/if#}" id="filePicker4" data-type="logo" data-count="1" data-size="5120" data-imglist=""><div></div><span></span></div>
            {#if $business_logo != ""#}
        <ul id="listSection4" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_4_1"><a href='{#$cfg_attachment#}{#$business_logo#}' target="_blank" title=""><img style="max-width: 150px;" alt="" src="{#$cfg_attachment#}{#$business_logo#}" data-val="{#$business_logo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
        {#else#}
        <ul id="listSection4" class="listSection thumblist clearfix"></ul>
        {#/if#}
        <input type="hidden" name="business_logo" value="" class="imglist-hidden" id="business_logo">
      </dd>
      </dl>

      <dl class="clearfix">
        <dt><label for="business_android_download">Android安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="business_android_download" id="business_android_download" value="{#$business_android_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="business_yyb_download">应用宝链接地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="business_yyb_download" id="business_yyb_download" value="{#$business_yyb_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="business_ios_download">iOS安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="business_ios_download" id="business_ios_download" value="{#$business_ios_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="business_noticeCount">消息提醒循环次数：</label></dt>
        <dd>
			<input class="input-small" type="number" name="business_noticeCount" id="business_noticeCount" value="{#$business_noticeCount#}" />
			<span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>0表示无限循环</span>
		</dd>
      </dl>
      <dl class="clearfix">
        <dt><label class="sl">版权信息：</label></dt>
        <dd>
          <script id="business_copyright" name="business_copyright" type="text/plain" style="width:95.4%;height:200px">{#$business_copyright#}</script>
        </dd>
      </dl>
  </div>
  <div class="item hide" id="peisong">
    <dl class="clearfix">
      <dt><label for="peisong_appname">APP名称：</label></dt>
      <dd><input class="input-medium" type="text" name="peisong_appname" id="peisong_appname" value="{#$peisong_appname#}" /></dd>
    </dl>
      <dl class="clearfix">
        <dt><label>最新版本：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2" id="peisong_android_version" name="peisong_android_version" type="text" value="{#$peisong_android_version#}" style="width: 120px;" />
            </div>
    		<div class="input-prepend">
              <span class="add-on">iOS：</span>
              <input class="span2" id="peisong_ios_version" name="peisong_ios_version" type="text" value="{#$peisong_ios_version#}" style="width: 120px;" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>更新时间：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2 updateDate" id="peisong_android_update" name="peisong_android_update" type="text" value="{#$peisong_android_update#}" style="width: 120px;" />
            </div>
    		<div class="input-prepend">
              <span class="add-on">iOS：</span>
              <input class="span2 updateDate" id="peisong_ios_update" name="peisong_ios_update" type="text" value="{#$peisong_ios_update#}" style="width: 120px;" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>强制升级：</label></dt>
        <dd class="clearfix">
          <label>
            <input id="peisong_android_force" name="peisong_android_force" value="1" type="checkbox"{#if $peisong_android_force#} checked{#/if#} />
            <span class="add-on">Android</span>
          </label>
          &nbsp;&nbsp;&nbsp;&nbsp;
          <label>
            <input id="peisong_ios_force" name="peisong_ios_force" value="1" type="checkbox"{#if $peisong_ios_force#} checked{#/if#} />
            <span class="add-on">iOS</span>
          </label>
      	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>更新内容：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on" style="vertical-align: top!important;">Android：</span>
              <textarea class="input-xlarge" style="height: 100px;" name="peisong_android_note" id="peisong_android_note">{#$peisong_android_note#}</textarea>
            </div>
    		<div class="input-prepend">
              <span class="add-on" style="vertical-align: top!important;">iOS：</span>
              <textarea class="input-xlarge" style="height: 100px;" name="peisong_ios_note" id="peisong_ios_note">{#$peisong_ios_note#}</textarea>
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>文件大小：</label></dt>
        <dd class="clearfix">
    		<div class="input-prepend">
              <span class="add-on">Android：</span>
              <input class="span2" id="peisong_android_size" name="peisong_android_size" type="text" value="{#$peisong_android_size#}" />
            </div>
    	</dd>
      </dl>
      <dl class="clearfix">
        <dt><label>LOGO：<br /><small>尺寸：180*180</small>&nbsp;&nbsp;&nbsp;</label></dt>
        <dd class="thumb clearfix listImgBox">
        <div class="uploadinp filePicker thumbtn{#if $peisong_logo != ""#} hide{#/if#}" id="filePicker5" data-type="logo" data-count="1" data-size="5120" data-imglist=""><div></div><span></span></div>
            {#if $peisong_logo != ""#}
        <ul id="listSection5" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_5_1"><a href='{#$cfg_attachment#}{#$peisong_logo#}' target="_blank" title=""><img style="max-width: 150px;" alt="" src="{#$cfg_attachment#}{#$peisong_logo#}" data-val="{#$peisong_logo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
        {#else#}
        <ul id="listSection5" class="listSection thumblist clearfix"></ul>
        {#/if#}
        <input type="hidden" name="peisong_logo" value="" class="imglist-hidden" id="peisong_logo">
      </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="peisong_android_download">Android安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="peisong_android_download" id="peisong_android_download" value="{#$peisong_android_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="peisong_yyb_download">应用宝链接地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="peisong_yyb_download" id="peisong_yyb_download" value="{#$peisong_yyb_download#}" /></dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="peisong_ios_download">iOS安装包下载地址：</label></dt>
        <dd><input class="input-xxlarge" type="text" name="peisong_ios_download" id="peisong_ios_download" value="{#$peisong_ios_download#}" /></dd>
      </dl>
    <dl class="clearfix">
        <dt><label>默认地图：</label></dt>
        <dd class="clearfix">
            <label><input type="radio" name="peisong_map_set" value="2" {#if $peisong_map_set == 2#}checked="checked"{#/if#}>百度</label>&nbsp;&nbsp;
			<label><input type="radio" name="peisong_map_set" value="1" {#if $peisong_map_set == 1#}checked="checked"{#/if#}>谷歌</label>&nbsp;&nbsp;
			<label><input type="radio" name="peisong_map_set" value="4" {#if $peisong_map_set == 4#}checked="checked"{#/if#}>高德</label>
            <span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>密钥封装在APP源码中，请在申请打包时填写好<a href="https://www.kumanyun.com/my/app.html" target="_blank">APP机密信息</a>，如要修改，需重新打包APP！</span>
        </dd>
    </dl>
	<dl class="clearfix">
	  <dt><label for="peisong_noticeCount">消息提醒循环次数：</label></dt>
	  <dd>
		  <input class="input-small" type="number" name="peisong_noticeCount" id="peisong_noticeCount" value="{#$peisong_noticeCount#}" />
		  <span class="input-tips" style="display:inline-block;margin:10px 0 10px;"><s></s>0表示无限循环</span>
	  </dd>
	</dl>
    <dl class="clearfix">
      <dt><label class="sl">版权信息：</label></dt>
      <dd>
        <script id="peisong_copyright" name="peisong_copyright" type="text/plain" style="width:95.4%;height:200px">{#$peisong_copyright#}</script>
      </dd>
    </dl>
  </div>

  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$editorFile#}
{#$jsFile#}
</body>
</html>
