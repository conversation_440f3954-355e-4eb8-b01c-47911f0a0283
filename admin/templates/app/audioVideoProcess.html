<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>APP音视频处理配置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
<style media="screen">
.editform dt {width: 180px;}
</style>
</head>

<body>
<div class="alert alert-success" style="margin:20px 50px 0;"><button type="button" class="close" data-dismiss="alert">×</button>配置信息请到【<a href="https://www.qiniu.com/" target="_blank">七牛云平台</a>】申请！</div>

<form action="" method="post" name="editform" id="editform" class="editform">
    <input type="hidden" name="token" id="token" value="{#$token#}" />
    <dl class="clearfix">
      <dt><label for="access_key">AccessKey：</label></dt>
      <dd><input class="input-xxlarge" type="text" name="access_key" id="access_key" value="{#$access_key#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="secret_key">SecretKey：</label></dt>
      <dd><input class="input-xxlarge" type="text" name="secret_key" id="secret_key" value="{#$secret_key#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="bucket">存储空间（bucket）：</label></dt>
      <dd><input class="input-xlarge" type="text" name="bucket" id="bucket" value="{#$bucket#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="pipeline">数据处理队列名称：</label></dt>
      <dd><input class="input-xlarge" type="text" name="pipeline" id="pipeline" value="{#$pipeline#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="domain">外链域名：</label></dt>
      <dd>
          <div class="input-prepend input-append">
            <span class="add-on">http://</span>
            <input class="span3" id="domain" name="domain" type="text" value="{#$domain#}" />
            <span class="add-on">/</span>
          </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="audio_quality">音频质量：</label></dt>
      <dd>
          <select name="audio_quality" id="audio_quality" class="input-medium">
            {#html_options options=$audioQualityArr selected=$audio_quality#}
          </select>
      </dd>
    </dl>
    <dl class="clearfix formbtn">
      <dt>&nbsp;</dt>
      <dd>
        <input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" />
      </dd>
    </dl>
</form>

{#$jsFile#}
</body>
</html>
