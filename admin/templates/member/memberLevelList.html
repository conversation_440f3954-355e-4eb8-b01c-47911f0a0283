<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>会员等级</title>
{#$cssFile#}
</head>

<body>
<ul class="thead clearfix" style="margin:10px 20px 0!important;">
  <li class="row30 left">&nbsp;&nbsp;&nbsp;&nbsp;等级名称</li>
  <li class="row60 left">图 标</li>
  <li class="row10 left">操 作</li>
</ul>

<form class="list mb50" id="list">
  <input type="hidden" id="token" value="{#$token#}" />
  <ul class="root">
  {#if $levelList neq ""#}
    {#foreach from=$levelList key=k item=i#}
    <li class="clearfix tr">
      <div class="row30 left">&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" data-id="{#$i.id#}" value="{#$i.name#}" /></div>
      <div class="row60 left">
        {#if $i.icon#}
        <img src="{#$i.iconturl#}" class="img" alt="" style="height:40px;">
        {#/if#}
        <a href="javascript:;" class="upfile">上传图标</a>
        <input type="file" name="Filedata" value="" class="imglist-hidden Filedata hide" id="Filedata_{#$k#}">
        <input type="hidden" name="icon" class="icon" value="{#$i.icon#}">
      </div>
      <div class="row10 left"><a href="javascript:;" class="del" title="删除">编辑删除</a></div>
    </li>
    {#/foreach#}
  {#/if#}
  </ul>
  <div class="tr clearfix">
    <div class="row80 left">&nbsp;&nbsp;<a href="javascript:;" class="add-type" style="display:inline-block;" id="addNew">新增会员等级</a></div>
  </div>
  <button type="button" class="btn btn-success" id="saveBtn">保存</button>
</form>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
