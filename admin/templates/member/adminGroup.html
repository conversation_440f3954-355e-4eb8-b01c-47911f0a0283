<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>管理组</title>
{#$cssFile#}
</head>

<body>
<ul class="thead clearfix" style="margin:10px 20px 0!important;">
  <li class="row30 left">&nbsp;&nbsp;&nbsp;&nbsp;管理组</li>
  <li class="row70 left">&nbsp;&nbsp;&nbsp;操 作</li>
</ul>

<form class="list mb50" id="list">
  <input type="hidden" id="token" value="{#$token#}" />
  <ul class="root">
  {#if $groupList neq ""#}
    {#foreach from=$groupList item=i#}
    <li class="clearfix tr">
      <div class="row30 left">&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" data-id="{#$i.id#}" value="{#$i.groupname#}" /></div>
      <div class="row70 left"><a href="adminGroup.php?action=perm&id={#$i.id#}" class="edit" title="编辑权限">编辑权限</a><a href="javascript:;" class="del" title="删除">删除</a></div>
    </li>
    {#/foreach#}
  {#/if#}
  </ul>
  <div class="tr clearfix">
    <div class="row80 left">&nbsp;&nbsp;<a href="javascript:;" class="add-type" style="display:inline-block;" id="addNew">新增管理组</a></div>
  </div>
  <button type="button" class="btn btn-success" id="saveBtn">保存</button>
</form>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>