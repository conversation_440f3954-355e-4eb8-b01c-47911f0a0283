<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
  <title>用户账单记录</title>
  {#$cssFile#}
</head>

<body>
<div class="search">
  <label for="keyword">搜索：</label>
  <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value=""></div>
  <div class="btn-group" id="typeBtn" style="margin-left: 5px;">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部类型<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部类型</a></li>
      <li><a href="javascript:;" data-id="1">缴纳</a></li>
      <li><a href="javascript:;" data-id="2">提取</a></li>
    </ul>
  </div>
  <input class="input-xlarge" type="search" id="keyword" placeholder="用户名/昵称/用户ID/订单号">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
  &nbsp;&nbsp;
  <a href="javascript:;" class="btn btn-primary" id="configBtn">保障金设置</a>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="stateBtn"{#if $notice#} data-id="0"{#/if#}>
      {#if $notice#}
      <button class="btn dropdown-toggle" data-toggle="dropdown">待审核(<span class="state0"></span>)<span class="caret"></span></button>
      {#else#}
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      {#/if#}
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="0">待审核(<span class="state0"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">审核通过(<span class="state1"></span>)</a></li>
        <li><a href="javascript:;" data-id="2">审核失败(<span class="state2"></span>)</a></li>
      </ul>
    </div>
    <a href="?dopost=getList&do=export" class="btn btn-warning" id="export">导出保障金记录</a>
    <span class="help-inline">缴纳：<span id="totalAdd">0</span>&nbsp;&nbsp;&nbsp;&nbsp;已提取：<span id="totalLess">0</span>&nbsp;&nbsp;&nbsp;&nbsp;可提取：<span id="totalUsable">0</span></span>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row10 left">城市</li>
  <li class="row10 left">类型</li>
  <li class="row20 left">订单号</li>
  <li class="row15 left">会员</li>
  <li class="row10 left">金额变化</li>
  <li class="row15 left">时间</li>
  <li class="row10 left">状态</li>
  <li class="row10">操作</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script id="settingHtml" type="text/html">
    <form action="" class="quick-editForm" name="editForm">
        <input type="hidden" name="token" id="token" value="{#$token#}" />
      <dl class="clearfix">
          <dt>保障金说明：</dt>
          <dd><textarea class="input-xlarge" id="note" name="note" rows="3" placeholder="比如：保障金在1年内不能提取">{#$cfg_promotion_note#}</textarea></dd>
      </dl>
      <dl class="clearfix">
        <dt>最少缴纳：</dt>
        <dd>
            <div class="input-append" style="margin-bottom: 0;">
                <input class="input-mini" step="1" min="1" type="number" id="least" name="least" value="{#$cfg_promotion_least#}" />
                <span class="add-on">元</span>
            </div>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt>提取原因：</dt>
        <dd class="clearfix">
            <textarea class="input-xlarge" id="reason" name="reason" rows="5" placeholder="一行一个&#13;&#10;如：&#13;&#10;资金紧张&#13;&#10;不想交了">{#$cfg_promotion_reason#}</textarea>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt>提取限制：</dt>
        <dd class="clearfix">
            <div class="input-prepend input-append" style="margin-bottom: 0;">
                <span class="add-on">缴纳</span>
                <input class="input-mini" step="1" min="1" type="number" id="limitVal" name="limitVal" value="{#$cfg_promotion_limitVal#}" />
                <span class="add-on" style="padding: 0!important; height: 33px;">
                    <select class="input-mini" id="limitType" name="limitType" style="border: 0;box-sizing: border-box;line-height: 33px;height: 33px;">
                        <option value="1"{#if $cfg_promotion_limitType == 1#} selected{#/if#}>天</option>
                        <option value="2"{#if $cfg_promotion_limitType == 2#} selected{#/if#}>月</option>
                        <option value="3"{#if $cfg_promotion_limitType == 3#} selected{#/if#}>年</option>
                    </select>
                </span>
            </div>
            <span class="help-inline">内不可以提取</span>
            <div style="line-height: 1.5em; padding: 10px 0 5px 0; color: #999; font-size: 12px;">设置提取限制后，用户端将显示可提取金额；<br />并且申请提取后无须后台审核自动入账到余额；<br />如果需要后台审核，提取限制填写0即可；</div>
        </dd>
      </dl>
    </form>
</script>

<script>
  var adminPath = "{#$adminPath#}",cityList={#json_encode($cityArr)#};
</script>
{#$jsFile#}
</body>
</html>
