<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>会员等级费用设置</title>
{#$cssFile#}
<style media="screen">
  .table {width: auto;}
  .table th {min-width: 150px; height: 30px; text-align: center; line-height: 30px;}
  .table th:last-child {min-width: 50px;}
  .table td {text-align: center; height: 34px; line-height: 31px;}
  .level {font-size: 18px;}
  .input-append, .input-prepend {margin-bottom: 0;}
  .del {display: inline-block; vertical-align: middle;}
  .input-append select {margin: -5px -6px 0 -6px; border-radius: 0;}
  .setDiscount.curr {padding:2px 5px;background: #51a351;border-radius:3px;color:#fff;}
  .setDiscount.curr:hover {text-decoration: none;}
</style>
</head>

<body style="padding: 20px;">

<table class="table table-hover table-bordered table-striped" id="priceTab">
  <thead>
    <tr>
      <th>等级名称</th>
      <th>费用</th>
      <th>&nbsp;&nbsp;&nbsp;&nbsp;最低充值时间&nbsp;&nbsp;&nbsp;&nbsp;</th>
      <th>&nbsp;&nbsp;&nbsp;&nbsp;充值优惠&nbsp;&nbsp;&nbsp;&nbsp;</th>
    </tr>
  </thead>
  <tbody>
    {#if $levelList#}
    {#foreach from=$levelList item=level#}
    <tr data-id="{#$level.id#}">
      <td>{#$level.name#}</td>
      <td><div class="input-append"><input class="span1 price" type="text" value="{#$level.price#}"><span class="add-on">元/月</span></div></td>
      <td>
        <div class="input-append">
          <select class="input-small mintime" name="mintime">
            <option value="1"{#if $level.mintime == 1#} selected="selected"{#/if#}>1个月</option>
            <!-- <option value="2"{#if $level.mintime == 2#} selected="selected"{#/if#}>2个月</option> -->
            <option value="3"{#if $level.mintime == 3#} selected="selected"{#/if#}>3个月</option>
            <!-- <option value="4"{#if $level.mintime == 4#} selected="selected"{#/if#}>4个月</option> -->
            <!-- <option value="5"{#if $level.mintime == 5#} selected="selected"{#/if#}>5个月</option> -->
            <option value="6"{#if $level.mintime == 6#} selected="selected"{#/if#}>6个月</option><!--
            <option value="7"{#if $level.mintime == 7#} selected="selected"{#/if#}>7个月</option>
            <option value="8"{#if $level.mintime == 8#} selected="selected"{#/if#}>8个月</option>
            <option value="9"{#if $level.mintime == 9#} selected="selected"{#/if#}>9个月</option> -->
<!--             <option value="10"{#if $level.mintime == 10#} selected="selected"{#/if#}>10个月</option>
            <option value="11"{#if $level.mintime == 11#} selected="selected"{#/if#}>11个月</option> -->
            <option value="12"{#if $level.mintime == 12#} selected="selected"{#/if#}>12个月</option>
            <option value="12"{#if $level.mintime == 24#} selected="selected"{#/if#}>24个月</option>
            <option value="12"{#if $level.mintime == 36#} selected="selected"{#/if#}>36个月</option>
          </select>
        </div>
      </td>
      <td><a href="javascript:;" class="setDiscount" data-id="{#$level.id#}">查看</a></td>
    </tr>
    {#/foreach#}
    {#/if#}
  </tbody>
  <tbody>
    <tr>
      <td colspan="{#$levelList|@count + 2#}">
        <button type="button" class="btn btn-success btn-small" id="save">保存修改</button>
      </td>
    </tr>
  </tbody>
</table>
{#if $levelList#}
<div class="levelDiscount">
{#foreach from=$levelList item=list key=k#}
<div class="levelItem level_{#$list.id#}" style="display:none;">
  <p style="width:470px;text-align:center;"><span style="color:#f60;font-weight:bold;">{#$list.name#}</span> - 充值优惠</p>
  <table class="table table-hover table-bordered table-striped discountTab">
    <thead>
      <tr>
        <th>充值月数</th>
        <th style="padding:8px 41px;">优惠折扣（留空或0表示没有折扣）</th>
      </tr>
    </thead>
    <tbody>
      {#if $list['discount']#}
      {#foreach from=$list['discount'] item=discount#}
      <tr data-month="{#$discount.month#}">
        <td>{#$discount.month#}个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="{#$discount.discount#}" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      {#/foreach#}
      {#else#}
      <tr data-month="1">
        <td>1个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      <tr data-month="3">
        <td>3个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      <tr data-month="6">
        <td>6个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      <tr data-month="12">
        <td>12个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      <tr data-month="24">
        <td>24个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      <tr data-month="36">
        <td>36个月</td>
        <td><div class="input-append"><input class="span3 text-center discount" type="text" min="0" value="" placeholder="8折填写8, 88折填写8.8"><span class="add-on">折</span></div></td>
      </tr>
      {#/if#}
    </tbody>
    <tbody>
      <tr>
        <td colspan="2">
          <button type="button" class="btn btn-success btn-small discountSave" data-id="{#$list.id#}">保存修改</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
{#/foreach#}
</div>
{#/if#}

<script type="text/templates" id="trTemp">
  <tr>
    <td><div class="input-append"><input class="span1" type="text"><span class="add-on"><select class="input-mini"><option value="day" selected="selected">天</option><option value="month">月</option><option value="year">年</option></select></span></div></td>
    {#foreach from=$levelList item=level#}
    <td data-id="{#$level['id']#}"><div class="input-append"><input class="span1" type="text"><span class="add-on">&yen;</span></div></td>
    {#/foreach#}
    <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
  </tr>
</script>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
