<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>刷新置顶配置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
<style media="screen">
  .domain-rules {margin: 0 50px;}
  .domain-rules th {font-size: 14px; line-height: 3em; border-bottom: 1px solid #ededed; padding: 0 5px; text-align: left;}
  .domain-rules td {font-size: 14px; line-height: 3.5em; border-bottom: 1px solid #ededed; padding: 0 5px;}
  .domain-rules .input-append, .domain-rules .input-prepend {margin: 15px 0 0;}
  .domain-rules input {font-size: 16px;}
  .editform dt label.sl {margin-top: -10px;}
  .editform dt small {display: block; margin: -8px 12px 0 0;}
  .editform dt small i {font-style: normal;}

  .priceWrap .table {width: auto;}
  .priceWrap .table th {min-width: 105px; height: 30px; text-align: center; line-height: 30px;}
  .priceWrap .table th:last-child {min-width: 50px;}
  .priceWrap .table td {text-align: center; height: 34px; line-height: 31px;}
  .priceWrap .level {font-size: 18px;}
  .priceWrap .input-append, .input-prepend {margin-bottom: 0;}
  .priceWrap .del {display: inline-block; vertical-align: middle;}
  .priceWrap .input-append select {margin: -5px -6px 0 -6px; border-radius: 0;}

  #fenxiaoradio  {padding: 20px 10px 0 50px;}
  #fenxiaoradio label {!important;display: inline-block;}
  #agreement {width:530px;height:200px;}

  .input-prepend.input-append .add-on:first-child, .input-prepend.input-append .btn:first-child {margin-right: -1px;-webkit-border-radius: 4px 0 0 4px;-moz-border-radius: 4px 0 0 4px;border-radius: 4px 0 0 4px;margin-bottom: 10px;}
</style>
</head>

<body>

<form action="" method="post" name="editform" id="editform" class="editform">
  <div id="fenxiaoradio">
    <div>分佣开关：{#html_radios name="fenXiao" values=$fenXiaoSwitch checked=$fenXiaoSwitchChecked output=$fenXiaoSwitchNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>启用后，刷新置顶费用将参与分佣。</span></div>
  </div>
  <input type="hidden" name="token" id="token" value="{#$token#}" />

  <div class="btn-group config-nav" data-toggle="buttons-radio" style="margin-bottom: 20px;">
    {#if in_array("info", $installModuleArr)#}<button type="button" class="btn active" data-type="info">{#getModuleTitle name='info'#}</button>{#/if#}
    {#if in_array("house", $installModuleArr)#}<button type="button" class="btn{#if !in_array("info", $installModuleArr)#} active{#/if#}" data-type="house">{#getModuleTitle name='house'#}</button>{#/if#}
    {#if in_array("job", $installModuleArr)#}<button type="button" class="btn{#if !in_array("info", $installModuleArr) && !in_array("house", $installModuleArr)#} active{#/if#}" data-type="job">{#getModuleTitle name='job'#}</button>{#/if#}
    {#if in_array("car", $installModuleArr)#}<button type="button" class="btn{#if !in_array("info", $installModuleArr) && !in_array("house", $installModuleArr) && !in_array("job", $installModuleArr)#} active{#/if#}" data-type="car">{#getModuleTitle name='car'#}</button>{#/if#}

    {#if in_array("education", $installModuleArr)#}<button type="button" class="btn{#if !in_array("car", $installModuleArr) && !in_array("info", $installModuleArr) && !in_array("house", $installModuleArr) && !in_array("job", $installModuleArr)#} active{#/if#}" data-type="education">{#getModuleTitle name='education'#}</button>{#/if#}

    {#if in_array("sfcar", $installModuleArr)#}<button type="button" class="btn{#if !in_array("car", $installModuleArr) && !in_array("info", $installModuleArr) && !in_array("house", $installModuleArr) && !in_array("job", $installModuleArr)#} active{#/if#}" data-type="sfcar">{#getModuleTitle name='sfcar'#}</button>{#/if#}
  </div>

  {#if in_array("info", $installModuleArr)#}
  <div class="item">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="info_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="info_refreshFreeTimes" name="info_refreshFreeTimes" value="{#$info_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="info_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="info_refreshNormalPrice" name="info_refreshNormalPrice" value="{#$info_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="info_titleBlodlPrice">标题加粗：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini titleBlodlPrice" type="number" id="info_titleBlodlPrice" name="info_titleBlodlPrice" value="{#$info_titleBlodlPrice#}">
          <span class="add-on">{#echoCurrency type='short'#}</span>
        </div>
        <div class="input-prepend input-append">
            <span class="add-on" style="">时长</span>
          <input class="input-mini titleBlodlDay" type="number" id="info_titleBlodlDay" name="info_titleBlodlDay" value="{#$info_titleBlodlDay#}">
          <span class="add-on">天</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="info_titleRedPrice">标题加红：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini titleRedPrice" type="number" id="info_titleRedPrice" name="info_titleRedPrice" value="{#$info_titleRedPrice#}">
          <span class="add-on">{#echoCurrency type='short'#}</span>
        </div>
        <div class="input-prepend input-append">
            <span class="add-on" style="">时长</span>
          <input class="input-mini titleRedDay" type="number" id="info_titleRedDay" name="info_titleRedDay" value="{#$info_titleRedDay#}">
          <span class="add-on">天</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $info_refreshSmart#}
              {#foreach from=$info_refreshSmart item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="info_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="info_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="info_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="info_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="7">
                  <button type="button" class="btn btn-small addPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $info_topNormal#}
              {#foreach from=$info_topNormal item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="info_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="info_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="5">
                  <button type="button" class="btn btn-small addPrice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="info_topPlan[all][]" value="{#$info_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="info_topPlan[day][]" value="{#$info_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
  </div>
  {#/if#}

  {#if in_array("house", $installModuleArr)#}
  <div class="item{#if in_array("info", $installModuleArr)#} hide{#/if#}">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="house_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="house_refreshFreeTimes" name="house_refreshFreeTimes" value="{#$house_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="house_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="house_refreshNormalPrice" name="house_refreshNormalPrice" value="{#$house_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $house_refreshSmart#}
              {#foreach from=$house_refreshSmart item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="house_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="house_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="house_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="house_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="7">
                  <button type="button" class="btn btn-small addPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $house_topNormal#}
              {#foreach from=$house_topNormal item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="house_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="house_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="5">
                  <button type="button" class="btn btn-small addPrice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="house_topPlan[all][]" value="{#$house_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="house_topPlan[day][]" value="{#$house_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
  </div>
  {#/if#}

  {#if in_array("job", $installModuleArr)#}
  <div class="item{#if in_array("info", $installModuleArr) || in_array("house", $installModuleArr)#} hide{#/if#}">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="job_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="job_refreshFreeTimes" name="job_refreshFreeTimes" value="{#$job_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="job_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="job_refreshNormalPrice" name="job_refreshNormalPrice" value="{#$job_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th>推荐</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $job_refreshSmart#}
              {#foreach from=$job_refreshSmart key=myKey item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="job_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="job_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer"><input class="offerHidden" type="hidden" name="job_refresh[offer][]">0元</td>
                <td><label>是<input type="radio" value="1" name="job_refresh_rec_{#$myKey#}" {#if $refresh['rec']==1#}checked{#/if#}/></label> <label>否<input value="0" type="radio" name="job_refresh_rec_{#$myKey#}" {#if $refresh['rec']!=1#}checked{#/if#}/></label></td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="job_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="job_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer"><input class="offerHidden" type="hidden" name="job_refresh[offer][]">0元</td>
                <td><label>是<input type="radio" value="1" name="job_refresh_rec_{#$myKey#}" {#if $refresh['rec']==1#}checked{#/if#}/></label> <label>否<input value="0" type="radio" name="job_refresh_rec_{#$myKey#}" {#if $refresh['rec']!=1#}checked{#/if#}/></label></td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="8">
                  <button type="button" class="btn btn-small jobAddPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th>推荐</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $job_topNormal#}
              {#foreach from=$job_topNormal key=myKey item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="job_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><label>是<input type="radio" value="1" name="job_topNormal_rec_{#$myKey#}" {#if $top['rec']==1#}checked{#/if#}/></label> <label>否<input value="0" type="radio" name="job_topNormal_rec_{#$myKey#}" {#if $top['rec']!=1#}checked{#/if#}/></label></td></td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="job_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><label>是<input type="radio" value="1" name="job_topNormal_rec_{#$myKey#}" {#if $top['rec']==1#}checked{#/if#}/></label> <label>否<input value="0" type="radio" name="job_topNormal_rec_{#$myKey#}" {#if $top['rec']!=1#}checked{#/if#}/></label></td></td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="6">
                  <button type="button" class="btn btn-small jobTopAddprice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="job_topPlan[all][]" value="{#$job_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="job_topPlan[day][]" value="{#$job_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">投递置顶&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>

    <dl class="clearfix">
      <dt><label>套餐配置：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped deliveryTable">
            <thead>
            <tr>
              <th>标题</th>
              <th>次数</th>
              <th>价格</th>
              <th>折扣</th>
              <th>单价</th>
              <th>优惠</th>
              <th>推荐</th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            {#if $job_delivery#}
            {#foreach from=$job_delivery key=myKey item=refresh#}
            <tr>
              <td>
                <div>
                  <input class="input-medium" name="job_delivery[title][]" value="{#$refresh.title#}" type="text">
                </div>
              </td>
              <td>
                <div class="input-append">
                  <input class="span1 count" name="job_delivery[count][]" value="{#$refresh.count#}" type="number">
                  <span class="add-on">次</span>
                </div>
              </td>
              <td>
                <div class="input-append">
                  <input class="input-small price" step="0.01" name="job_delivery[price][]" value="{#$refresh.price#}" type="number" min="0.01">
                  <span class="add-on">元</span>
                </div>
              </td>
              <td class="discount">无</td>
              <td class="unit">0元</td>
              <td class="offer"><input class="offerHidden" type="hidden" name="job_delivery[offer][]">0元</td>
              <td><label>是<input type="radio" value="1" name="job_delivery_rec_{#$myKey#}" {#if $refresh['rec']==1#}checked{#/if#}/></label> <label>否<input value="0" type="radio" name="job_delivery_rec_{#$myKey#}" {#if $refresh['rec']!=1#}checked{#/if#}/></label></td>
              <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
            </tr>
            {#/foreach#}
            {#else#}
            <tr>
              <td>
                <div>
                  <input class="input-medium" name="job_delivery[title][]" value="" type="text">
                </div>
              </td>
              <td>
                <div class="input-append">
                  <input class="span1 count" name="job_delivery[count][]" value="" type="number" min="1">
                  <span class="add-on">次</span>
                </div>
              </td>
              <td>
                <div class="input-append">
                  <input class="input-small price" step="0.01" name="job_delivery[price][]" value="" type="number" min="0.01">
                  <span class="add-on">元</span>
                </div>
              </td>
              <td class="discount">无</td>
              <td class="unit">0元</td>
              <td class="offer"><input class="offerHidden" type="hidden" name="job_delivery[offer][]">0元</td>
              <td><label>是<input type="radio" value="1" name="job_delivery_rec_{#$myKey#}" /></label> <label>否<input value="0" type="radio" name="job_delivery_rec_{#$myKey#}" checked/></label></td>
              <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
            </tr>
            {#/if#}
            </tbody>
            <tbody>
            <tr>
              <td colspan="8">
                <button type="button" class="btn btn-small addPrice" data-type="delivery">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

  </div>
  {#/if#}

  {#if in_array("car", $installModuleArr)#}
  <div class="item{#if in_array("info", $installModuleArr) || in_array("house", $installModuleArr) || in_array("job", $installModuleArr)#} hide{#/if#}">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="car_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="car_refreshFreeTimes" name="car_refreshFreeTimes" value="{#$car_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="car_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="car_refreshNormalPrice" name="car_refreshNormalPrice" value="{#$car_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $car_refreshSmart#}
              {#foreach from=$car_refreshSmart item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="car_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="car_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="car_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="car_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="7">
                  <button type="button" class="btn btn-small addPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $car_topNormal#}
              {#foreach from=$car_topNormal item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="car_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="car_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="5">
                  <button type="button" class="btn btn-small addPrice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="car_topPlan[all][]" value="{#$job_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="car_topPlan[day][]" value="{#$job_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
  </div>
  {#/if#}

  {#if in_array("education", $installModuleArr)#}
  <div class="item{#if in_array("car", $installModuleArr) || in_array("info", $installModuleArr) || in_array("house", $installModuleArr) || in_array("job", $installModuleArr)#} hide{#/if#}">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="education_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="education_refreshFreeTimes" name="education_refreshFreeTimes" value="{#$education_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="car_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="education_refreshNormalPrice" name="education_refreshNormalPrice" value="{#$education_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $education_refreshSmart#}
              {#foreach from=$education_refreshSmart item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="education_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="education_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="education_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="education_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="7">
                  <button type="button" class="btn btn-small addPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $education_topNormal#}
              {#foreach from=$education_topNormal item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="education_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="education_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="5">
                  <button type="button" class="btn btn-small addPrice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="education_topPlan[all][]" value="{#$education_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$education_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$education_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$education_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$education_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$education_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$job_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="education_topPlan[day][]" value="{#$job_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
  </div>
  {#/if#}

  {#if in_array("sfcar", $installModuleArr)#}
  <div class="item{#if in_array("car", $installModuleArr) || in_array("info", $installModuleArr) || in_array("house", $installModuleArr) || in_array("job", $installModuleArr)#} hide{#/if#}">
    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">刷新配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="sfcar_refreshFreeTimes">免费次数：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini" type="number" id="sfcar_refreshFreeTimes" name="sfcar_refreshFreeTimes" value="{#$sfcar_refreshFreeTimes#}">
          <span class="add-on">次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="car_refreshNormalPrice">普通刷新：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini refreshNormalPrice" type="number" id="sfcar_refreshNormalPrice" name="sfcar_refreshNormalPrice" value="{#$sfcar_refreshNormalPrice#}">
          <span class="add-on">元/次</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="info_titleBlodlPrice">标题加粗：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini titleBlodlPrice" type="number" id="sfcar_titleBlodlPrice" name="sfcar_titleBlodlPrice" value="{#$sfcar_titleBlodlPrice#}">
          <span class="add-on">{#echoCurrency type='short'#}</span>
        </div>
        <div class="input-prepend input-append">
            <span class="add-on" style="">时长</span>
          <input class="input-mini titleBlodlDay" type="number" id="sfcar_titleBlodlDay" name="sfcar_titleBlodlDay" value="{#$sfcar_titleBlodlDay#}">
          <span class="add-on">天</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="info_titleRedPrice">标题加红：</label></dt>
      <dd>
        <div class="input-append">
          <input class="input-mini titleRedPrice" type="number" id="sfcar_titleRedPrice" name="sfcar_titleRedPrice" value="{#$sfcar_titleRedPrice#}">
          <span class="add-on">{#echoCurrency type='short'#}</span>
        </div>
        <div class="input-prepend input-append">
            <span class="add-on" style="">时长</span>
          <input class="input-mini titleRedDay" type="number" id="sfcar_titleRedDay" name="sfcar_titleRedDay" value="{#$sfcar_titleRedDay#}">
          <span class="add-on">天</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>智能刷新：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、单价、优惠的值按照普通刷新一次的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped refreshSmartTable">
            <thead>
              <tr>
                <th>次数</th>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>单价</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $sfcar_refreshSmart#}
              {#foreach from=$sfcar_refreshSmart item=refresh#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="sfcar_refresh[times][]" value="{#$refresh.times#}" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="sfcar_refresh[day][]" value="{#$refresh.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_refresh[price][]" value="{#$refresh.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 times" name="sfcar_refresh[times][]" value="" type="number">
                    <span class="add-on">次</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="sfcar_refresh[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_refresh[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="unit">0元</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="7">
                  <button type="button" class="btn btn-small addPrice" data-type="refresh">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>

    <dl class="clearfix">
      <dt><strong style="font-size: 16px;">置顶配置&nbsp;&nbsp;&nbsp;&nbsp;</strong></dt>
      <dd>&nbsp;</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>普通置顶：</label></dt>
      <dd>
        <h5 class="stit" style="margin-top: 0;"><span class="label label-info">折扣、优惠的值按照第一条的价格计算</span></h5>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped topNormalTable">
            <thead>
              <tr>
                <th>时长</th>
                <th>价格</th>
                <th>折扣</th>
                <th>优惠</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {#if $sfcar_topNormal#}
              {#foreach from=$sfcar_topNormal item=top#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="sfcar_topNormal[day][]" value="{#$top.day#}" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topNormal[price][]" value="{#$top.price#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/foreach#}
              {#else#}
              <tr>
                <td>
                  <div class="input-append">
                    <input class="span1 day" name="sfcar_topNormal[day][]" value="" type="number">
                    <span class="add-on">天</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topNormal[price][]" value="" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td class="discount">无</td>
                <td class="offer">0元</td>
                <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
              </tr>
              {#/if#}
            </tbody>
            <tbody>
              <tr>
                <td colspan="5">
                  <button type="button" class="btn btn-small addPrice" data-type="topNormal">增加一行</button>&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>计划置顶：</label></dt>
      <dd>
        <div class="priceWrap">
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>时长</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全天</td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[0]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[1]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[2]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[3]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[4]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[5]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
                <td>
                  <div class="input-append">
                    <input class="input-small price" step="0.01" name="sfcar_topPlan[all][]" value="{#$sfcar_topPlan[6]['all']#}" type="number">
                    <span class="add-on">元</span>
                  </div>
                </td>
              </tr>
                <tr>
                  <td>早8点-晚8点</td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[0]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[1]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[2]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[3]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[4]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[5]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                  <td>
                    <div class="input-append">
                      <input class="input-small price" step="0.01" name="sfcar_topPlan[day][]" value="{#$sfcar_topPlan[6]['day']#}" type="number">
                      <span class="add-on">元</span>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      </dd>
    </dl>
  </div>
  {#/if#}

  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

<script type="text/templates" id="refresh">
<tr>
  <td>
    <div class="input-append">
      <input class="span1 times" name="__refresh[times][]" value="" type="number">
      <span class="add-on">次</span>
    </div>
  </td>
  <td>
    <div class="input-append">
      <input class="span1 day" name="__refresh[day][]" value="" type="number">
      <span class="add-on">天</span>
    </div>
  </td>
  <td>
    <div class="input-append">
      <input class="input-small price" step="0.01" name="__refresh[price][]" value="" type="number">
      <span class="add-on">元</span>
    </div>
  </td>
  <td class="discount">无</td>
  <td class="unit">0元</td>
  <td class="offer">0元</td>
  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
</tr>
</script>

<script type="text/templates" id="delivery">
  <tr>
    <td>
      <div>
        <input class="input-medium" name="__delivery[title][]" value="" type="text">
      </div>
    </td>
    <td>
      <div class="input-append">
        <input class="span1 count" name="__delivery[count][]" value="" type="number" min="1">
        <span class="add-on">次</span>
      </div>
    </td>
    <td>
      <div class="input-append">
        <input class="input-small price" step="0.01" name="__delivery[price][]" value="" type="number" min="0.01">
        <span class="add-on">元</span>
      </div>
    </td>
    <td class="discount">无</td>
    <td class="unit">0元</td>
    <td class="offer"><input class="offerHidden" type="hidden" name="job_delivery[offer][]">0元</td>
    <td><label>是<input type="radio" value="1" /></label> <label>否<input value="0" type="radio" checked/></label></td>
    <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
  </tr>
</script>

<script type="text/templates" id="topNormal">
<tr>
  <td>
    <div class="input-append">
      <input class="span1 day" name="__topNormal[day][]" value="" type="number">
      <span class="add-on">天</span>
    </div>
  </td>
  <td>
    <div class="input-append">
      <input class="input-small price" step="0.01" name="__topNormal[price][]" value="" type="number">
      <span class="add-on">元</span>
    </div>
  </td>
  <td class="discount">无</td>
  <td class="offer">0元</td>
  <td><a href="javascript:;" class="del" title="删除"><i class="icon-trash"></i></a></td>
</tr>
</script>

{#$jsFile#}
</body>
</html>
