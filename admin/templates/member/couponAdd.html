<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>var adminPath = "{#$adminPath#}";</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="add" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="amount">金额：</label></dt>
    <dd>
      <input class="input-small" type="number" name="amount" id="amount" maxlength="4" value="{#$amount#}" />
      <span class="input-tips"><s></s>请输入金额</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="count">生成数量：</label></dt>
    <dd>
      <input class="input-small" type="number" name="count" id="count" maxlength="4" value="1" />
      <span class="input-tips" style="display: inline-block"><s></s>请输入生成数量 建议2w以内</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>过期时间：</label><input type="hidden" name="level" id="level" value="{#$level#}" /></dt>
    <dd class="radio" style="overflow: inherit; padding-left: 140px;">
      <div class="input-prepend" style="margin-bottom: 0;">
        <input class="input-medium" type="text" name="expire" id="expire" value="">
      </div>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>
{#$jsFile#}
</body>
</html>
