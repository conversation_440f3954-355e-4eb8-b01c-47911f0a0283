<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>分销商列表</title>
{#$cssFile#}
<style>
	.backcount {cursor: pointer;}
</style>
</head>
<body>
<div class="search">
  <label>搜索：
 <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value=""></div>
 {#if $fenxiaoType#}
 <select class="chosen-select" id="ctype" style="width: auto;">
   <option value="">分销级别</option>
   <option value="">全部</option>
   {#foreach from=$fenxiaoLevel item='item' key='index'#}
   <option value="{#$index#}">{#$item.name#}</option>
   {#/foreach#}
 </select>
 {#else#}
<input type="hidden" id="ctype" value="" />
 {#/if#}
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
  &nbsp;&nbsp;&nbsp;&nbsp;
  <label for=""><input class="input-middle" type="number" min="1" id="userid" placeholder="请输入用户ID"></label>
  <button type="button" class="btn btn-primary" id="addBtn">添加分销商</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <div class="btn-group hide" id="batchAudit">
      <button class="btn dropdown-toggle" data-toggle="dropdown">批量审核<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="待审核">待审核</a></li>
        <li><a href="javascript:;" data-id="已审核">已审核</a></li>
        <li><a href="javascript:;" data-id="拒绝审核">拒绝审核</a></li>
      </ul>
    </div>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <div class="btn-group" id="stateBtn"{#if $notice#} data-id="0"{#/if#}>
      {#if $notice#}
      <button class="btn dropdown-toggle" data-toggle="dropdown">待审核(<span class="totalGray"></span>)<span class="caret"></span></button>
      {#else#}
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      {#/if#}
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="0">待审核(<span class="totalGray"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">已审核(<span class="totalAudit"></span>)</a></li>
        <li><a href="javascript:;" data-id="2">拒绝审核(<span class="totalRefuse"></span>)</a></li>
      </ul>
    </div>
    <a href="?dopost=getList&do=export" class="btn btn-warning" style="margin-left: 10px;" id="export">导出分销商数据</a>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3 left">&nbsp;</li>
  <li class="row10 left">分站</li>
  <li class="row15 left">用户信息</li>
  <li class="row15 left">昵称/真实姓名</li>
  <li class="row15 left">申请时间</li>
  <li class="row10 left">推荐人|下线</li>
  <li class="row10">佣金总额</li>
  <li class="row10">状态</li>
  <li class="row12">操作</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="type"></span>
  <span id="city"></span>
</div>

<script id="quickEdit" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
	  <dl class="clearfix">
        <dt>分销商等级：</dt>
        <dd>
          <select id="fenxiaoLevel_">
			  {#foreach from=$fenxiaoLevel key=k item=v#}
			  <option value="{#$k#}">{#$v.name#}</option>
			  {#/foreach#}
		  </select>
        </dd>
      </dl>
    <dl class="clearfix">
      <dt>应返：</dt>
      <dd>
        <span id="total_"></span>
      </dd>
    </dl>
	<dl class="clearfix">
      <dt>已返：</dt>
      <dd>
        <span id="count_"></span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>调整已返：</dt>
      <dd><input type="text" id="count" name="count" type="number" style="width: 100px;" /> 次</dd>
    </dl>
  </form>
</script>

<script>var adminPath = "{#$adminPath#}", pid = {#$pid#}, cityList={#json_encode($cityArr)#};</script>
{#$jsFile#}
</body>
</html>
