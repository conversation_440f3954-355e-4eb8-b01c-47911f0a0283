<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
  <title>资金沉淀记录</title>
  {#$cssFile#}
</head>

<body>
<div class="search">
  <label for="keyword">搜索：</label>
  <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value=""></div>
  <input class="input-xlarge" type="search" id="keyword" placeholder="用户昵称/订单号/标题" value="{#$sKeyword#}">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <div class="btn-group" id="leimuBtn" data-id="{#$source#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#if $source#}{#$leimuallarr[$source]#}{#else#}模块筛选{#/if#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="" data-nolist="true">所有模块</a></li>
      {#foreach $leimuallarr as $k => $v#}
      <li><a href="javascript:;" data-id="{#$k#}" data-nolist="true">{#$v#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <a href="?dopost=getList&do=export" class="btn btn-warning" id="export">导出资金沉淀数据</a>
    <span class="help-inline">总金额：<span id="totalMoney">0</span>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row10 left">分站</li>
  <li class="row10 left">模块</li>
  <li class="row10 left">订单号</li>
  <li class="row20 left">用户</li>
  <li class="row20 left">标题</li>
  <li class="row10 left">金额</li>
  <li class="row20 left">时间</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword">{#$sKeyword#}</span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>
  var adminPath = "{#$adminPath#}",cityList={#json_encode($cityArr)#};
</script>
{#$jsFile#}
</body>
</html>
