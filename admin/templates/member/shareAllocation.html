<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>财务分账记录</title>
{#$cssFile#}
<style>
.icon-question-sign {vertical-align: middle; margin: -2px 0 0 3px;}
.tooltip-inner {text-align: left;}
.list td a.retry {margin-left: 15px; color: green;}
</style>
</head>

<body>
<div class="search">
  <label for="keyword">搜索：</label>
  <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value=""></div>
  <select class="chosen-select" id="platform" style="width: auto;">
    <option value="">分账平台</option>
    <option value="">全部</option>
    <option value="wxpay">微信服务商</option>
    <option value="alipay">支付宝服务商</option>
    <option value="rfbp_icbc">工行E商通</option>
  </select>
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" autocomplete="off" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" autocomplete="off" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="stateBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
		<li><a href="javascript:;" data-id="1">成功(<span class="state1"></span>)</a></li>
        <li><a href="javascript:;" data-id="0">失败(<span class="state0"></span>)</a></li>
      </ul>
    </div>
    <a href="?dopost=getList&do=export" class="btn btn-warning" style="margin-left: 10px;" id="export">导出分账数据</a>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row2">&nbsp;</li>
  <li class="row5 left">城市</li>
  <li class="row15 left">商家</li>
  <li class="row20 left">订单内容</li>
  <li class="row25 left">分账订单</li>
  <li class="row10 left">结算金额</li>
  <li class="row13 left">时间</li>
  <li class="row10 left">状态</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="sPlatform"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script id="quickEdit" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
    <p style="color: #f00; font-size: 12px;">注意：此金额不得大于分账平台设置的最大佣金比例金额，调整并分账成功后，还需要修改店铺对应的管理会员的账户余额！</p>
    <dl class="clearfix">
      <dt>调整金额：</dt>
      <dd><input type="text" id="amount" name="amount" type="text" style="width: 100px;" /> 元</dd>
    </dl>
  </form>
</script>

<script>
  var adminPath = "{#$adminPath#}",cityList={#json_encode($cityArr)#};
</script>
{#$jsFile#}
</body>
</html>
