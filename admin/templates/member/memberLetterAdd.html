<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>新增消息</title>
{#$cssFile#}
<script>var adminPath = '{#$adminPath#}';</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="dopost" id="dopost" value="add" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>接收用户：</label></dt>
    <dd class="radio">
      <label><input type="radio" name="userType" value="0" checked="checked">所有会员</label>&nbsp;&nbsp;
      <label><input type="radio" name="userType" value="1">个人会员</label>&nbsp;&nbsp;
      <label><input type="radio" name="userType" value="2">企业会员</label>&nbsp;&nbsp;
      <label><input type="radio" name="userType" value="3">在线会员</label>&nbsp;&nbsp;
      <label><input type="radio" name="userType" value="4">指定会员</label>&nbsp;&nbsp;
      <label><input type="radio" name="userType" value="5">所有设备（APP专用）</label>&nbsp;&nbsp;
      <label title="此项只能发送微信模板消息"><input type="radio" name="userType" value="6">微信用户（微信菜单中已同步的所有用户）</label>&nbsp;&nbsp;
    </dd>
  </dl>
  <dl class="clearfix hide" id="userIds">
    <dt><label for="users">会员名：</label></dt>
    <dd>
      <textarea name="users" id="users" rows="5" style="width: 50%;" placeholder="多个会员名用,分隔"></textarea>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="title">标题：</label></dt>
    <dd><input class="input-xxlarge" type="text" name="title" id="title" data-regex=".{2,60}" maxlength="60" value="{#$title#}" placeholder="如果是发送模板消息，这里填写微信模板ID" /></dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="body">内容：</label></dt>
    <dd>
      <textarea name="body" id="body" rows="12" style="width: 50%;" placeholder='站内信和邮件支持html代码；&#13;&#10;如果发送短信，使用阿里云/阿里大于/腾讯云等平台，此处直接填写营销短信的模板ID；&#13;&#10;&#13;&#10;如果发送微信模板消息，这里填写微信模板配置；&#13;&#10;如：&#13;&#10;{&#13;&#10;    "first":{"value":"标题"},&#13;&#10;    "keyword1":{"value":"内容"},&#13;&#10;    "keyword2":{"value":"2020-06-17"},&#13;&#10;    "keyword3":{"value":"已发送"},&#13;&#10;    "remark":{"value":"备注"}&#13;&#10;}'></textarea>
    </dd>
  </dl>
  <dl class="clearfix mtype mtype1">
    <dt><label>发送方式：</label></dt>
    <dd class="radio">
      <label><input type="radio" name="type" value="0" checked="checked">站内消息</label>&nbsp;&nbsp;
      <label><input type="radio" name="type" value="1">邮件</label>&nbsp;&nbsp;
      <label><input type="radio" name="type" value="2">短信</label>&nbsp;&nbsp;
      <label><input type="radio" name="type" value="3">APP推送</label>&nbsp;&nbsp;
      <label><input type="radio" name="type" value="4">微信模板消息</label>&nbsp;&nbsp;
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="url">链接：</label></dt>
    <dd>
        <input class="input-xxlarge" type="text" name="url" id="url" value="{#$url#}" placeholder="非必填项" />
        <br />
        <div class="mtype mtype2 hide" style="font-size: 12px; padding-top: 10px;">
            微信模板消息支持跳转到小程序，链接格式为：<code>miniprogram://appid|path</code><br />
            如：<code>miniprogram://wxa5b8a049003e43ee|/pages/packages/info/index/index</code>  打开分类信息首页<br />
            如：<code>miniprogram://wxa5b8a049003e43ee|/pages/redirect/index?url=/circle/</code>  打开圈子H5首页
        </div>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认发送</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
