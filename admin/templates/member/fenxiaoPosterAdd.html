<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title></title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", modelType = 'member',action = 'member';
</script>
<style>
.editform dt {width: 200px;}
.jcrop-holder{text-align:left;}
.jcrop-holder img {max-width:none;}
.jcrop-vline, .jcrop-hline{font-size:0; position:absolute; background:white url('/static/images/admin/Jcrop.gif') top left repeat;}
.jcrop-vline{height:100%; width:1px !important;}
.jcrop-hline{width:100%; height:1px !important;}
.jcrop-handle{font-size:1px; width:7px !important; height:7px !important; border:1px #eee solid; background-color:#333; *width:9px; *height:9px;}
.jcrop-tracker{width:100%; height:100%;}
.custom .jcrop-vline,.custom .jcrop-hline{background:yellow;}
.custom .jcrop-handle{border-color:black; background-color:#C7BB00; -moz-border-radius:3px; -webkit-border-radius:3px;}

.spic img{height: 500px !important;max-width: 1200px !important}


</style>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt>海报背景图：<br /><small>推荐尺寸：<code>1126px * 2000px</code></small>&nbsp;&nbsp;&nbsp;</dt>
    <dd class="cjImg">
      <input name="litpic" type="hidden" id="litpic_" value="{#$litpic#}" />
      <div class="spic{#if $litpic == "" #} hide{#/if#}">
        <div class="sholder" id="litpic_Preview">
          {#if $litpic#}
          <img src="{#$litpicpath#}" data-val="{#$litpic#}" class="crop" id="crop" data-width="{#$imgwidth#}" data-height="{#$imgheight#}">
          {#/if#}
        </div>
        <a href="javascript:;" class="reupload">重新上传</a>
      </div>
      <iframe id="iframe" src ="/include/upfile.inc.php?mod=member&type=card&obj=litpic_&filetype=image" style="width:100%; height:25px;{#if $litpic != "" #}display: none;{#/if#}" scrolling="no" frameborder="0" marginwidth="0" marginheight="0"></iframe>

      <input type="hidden" name="xAxis" id="xAxis" value="{#$xAxis#}" />
      <input type="hidden" name="yAxis" id="yAxis" value="{#$yAxis#}" />
      <input type="hidden" name="codewidth" id="codewidth" value="{#$codewidth#}" />
      <input type="hidden" name="codeheight" id="codeheight" value="{#$codeheight#}" />
      <input type="hidden" name="imgwidth" id="imgwidth" value="{#$imgwidth#}" />
      <input type="hidden" name="imgheight" id="imgheight" value="{#$imgheight#}" />
      <input type="hidden" name="cropwidth" id="cropwidth" value="{#$cropwidth#}" />
      <input type="hidden" name="cropheight" id="cropheight" value="{#$cropheight#}" />
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="url">自定义链接：</label></dt>
    <dd>
      <input class="input-xxlarge" type="text" name="url" id="url" value="{#$url#}" placeholder="留空默认打开注册页" />
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
