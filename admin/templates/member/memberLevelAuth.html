<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>会员等级特权设置</title>
{#$cssFile#}
<style media="screen">
  .table {width: auto;}
  .table th {min-width: 150px; height: 30px; text-align: center; line-height: 30px;}
  .table td {text-align: center; height: 34px; line-height: 31px;}
  .level {font-size: 18px;}
  .input-append, .input-prepend {margin-bottom: 0;}
  .del {display: inline-block; vertical-align: middle;}
</style>
</head>

<body style="padding: 20px;">

<table class="table table-hover table-bordered table-striped">
  <thead>
    <tr>
      <th></th>
      <th class="level">发布费用</th>
      <th class="level">普通会员</th>
      {#foreach from=$levelList item=levelTitle#}
      <th class="level">{#$levelTitle.name#}</th>
      {#/foreach#}
    </tr>
  </thead>
  <tbody>

	{#$is_shop = 0#}
	{#$is_waimai = 0#}
    {#foreach from=$moduleArr item=module#}

	{#if $module.name == 'shop'#}
	{#$is_shop = 1#}
	{#/if#}

	{#if $module.name == 'waimai'#}
	{#$is_waimai = 1#}
	{#/if#}

    {#if $module.name == 'article'#}
    {#*
    <tr>
      <td>新闻投稿</td>
      <td data-id="0" data-type="amount" data-module="article">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['article']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="article">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['article']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="article"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['article']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    *#}
    {#/if#}

    {#if $module.name == 'info'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="info">
          <div class="input-append hide" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['info']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
          按时长收费
      </td>
      <td data-id="0" data-type="count" data-module="info">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['info']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="info"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['info']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'sfcar'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="sfcar">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['sfcar']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="sfcar">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['sfcar']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="sfcar"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['sfcar']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'house'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="house">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['house']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="house">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['house']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="house"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['house']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'tieba'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="tieba">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['tieba']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="tieba">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['tieba']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="tieba"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['tieba']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'huodong'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="huodong">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['huodong']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="huodong">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['huodong']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="huodong"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['huodong']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'vote'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="vote">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['vote']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="vote">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['vote']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="vote"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['vote']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'live'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="live">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['live']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="live">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['live']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="live"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['live']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    <tr>
      <td>直播时间</td>
      <td data-id="0" colspan="2" data-module="livetime">
          <div class="input-append" title="最多直播时长"><input class="span1" type="text" value="{#$fabuAmount['livetime']#}"><span class="add-on">小时/条</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="livetime"><div class="input-append" title="最多直播时长"><input class="span1" type="text" value="{#$list['privilege']['livetime']#}"><span class="add-on">小时/条</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'car'#}
    <tr>
      <td>{#$module.title#}</td>
      <td data-id="0" data-type="amount" data-module="car">
          <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['car']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
      </td>
      <td data-id="0" data-type="count" data-module="car">
          <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['car']#}"><span class="add-on">条/周</span></div>
      </td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="car"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['car']#}"><span class="add-on">条/周</span></div></td>
      {#/foreach#}
    </tr>
    {#/if#}

    {#if $module.name == 'education'#}
    <tr>
        <td>{#$module.title#}</td>
        <td data-id="0" data-type="amount" data-module="education">
            <div class="input-append" title="超出免费条数收取"><input class="span1" type="text" value="{#$fabuAmount['education']#}"><span class="add-on">{#echoCurrency type="short"#}/条</span></div>
        </td>
        <td data-id="0" data-type="count" data-module="education">
            <div class="input-append"><input class="span1" type="text" value="{#$fabuFreeCount['education']#}"><span class="add-on">条/周</span></div>
        </td>
        {#foreach from=$levelList item=list#}
        <td data-id="{#$list['id']#}" data-module="education"><div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['education']#}"><span class="add-on">条/周</span></div></td>
        {#/foreach#}
    </tr>
    {#/if#}

    {#/foreach#}

	{#if $is_shop || $is_waimai#}
    <tr>
      <td>配送费优惠</td>
      <td colspan="" rowspan="" headers=""></td>
      <td colspan="" rowspan="" headers=""></td>
      {#foreach from=$levelList item=list key=key#}
      <td data-id="{#$list['id']#}" data-module="delivery">
        <label for=""><input type="radio" name="yhtype_{#$key#}" value="discount"{#if $list['privilege']['delivery'][0]['type'] == 'discount'#} checked{#/if#}><div class="input-append"><input class="span1" type="text" value="{#if $list['privilege']['delivery'][0]['type'] == 'discount'#}{#$list['privilege']['delivery'][0]['val']#}{#/if#}"><span class="add-on">折</span></div></label>
        <label for=""><input type="radio" name="yhtype_{#$key#}" value="count"{#if $list['privilege']['delivery'][0]['type'] == 'count'#} checked{#/if#}><div class="input-append" title="0表示不限次数"><input class="span1" type="text" value="{#if $list['privilege']['delivery'][0]['type'] == 'count'#}{#$list['privilege']['delivery'][0]['val']#}{#/if#}" placeholder="免费"><span class="add-on">次</span></div></label>
      </td>
      {#/foreach#}
    </tr>
	{#/if#}

	{#if $is_waimai#}
    <tr>
      <td>外卖商品优惠</td>
      <td colspan="" rowspan="" headers=""></td>
      <td colspan="" rowspan="" headers=""></td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="waimai">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['waimai']#}"><span class="add-on">折</span></div></td>
      {#/foreach#}
    </tr>
	{#/if#}

	{#if $is_shop#}
    <tr>
      <td>商城商品优惠</td>
      <td colspan="" rowspan="" headers=""></td>
      <td colspan="" rowspan="" headers=""></td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="shop">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<div class="input-append"><input class="span1" type="text" value="{#$list['privilege']['shop']#}"><span class="add-on">折</span></div></td>
      {#/foreach#}
    </tr>
	{#/if#}

	{#if $is_waimai#}
    <tr class="quan">
      <td>平台券赠送<a href="javascript:;" class="add-type" id="addQuan">新增</a></td>
      <td colspan="" rowspan="" headers="" class="noTd"><div class="input-append" style="height: 5px;width: 10px;">
          </div></td>
      <td colspan="" rowspan="" headers="" class="noTd"><div class="input-append" style="height: 5px;width: 10px;">
          </div></td>
      {#foreach from=$levelList item=list#}
      <td data-id="{#$list['id']#}" data-module="quan">
        {#if $list['privilege']['quan']#}
        {#foreach from=$list['privilege']['quan'] item=selquan#}
          <div class="input-append">
            <select name="" id="" class="input-medium">
              <option value="0">请选择</option>
              {#foreach from=$quanList item=quan#}
              <option value="{#$quan.id#}"{#if $selquan.qid == $quan.id#} selected="selected"{#/if#}>{#$quan.name#}</option>
              {#/foreach#}
            </select>
            <input class="span1" type="text" value="{#$selquan.num#}"><span class="add-on">张</span>
          </div>
        {#/foreach#}
        {#else#}
          <div class="input-append">
            <select name="" id="" class="input-medium">
              <option value="0">请选择</option>
              {#foreach from=$quanList item=quan#}
              <option value="{#$quan.id#}">{#$quan.name#}</option>
              {#/foreach#}
            </select>
            <input class="span1" type="text" value="{#$list['privilege']['quan']['val']#}"><span class="add-on">张</span>
          </div>
        {#/if#}
      </td>
      {#/foreach#}
    </tr>
	{#/if#}
  </tbody>
  <tbody>
    <tr>
      <td colspan="{#$levelList|@count + 3#}">
        <button type="button" class="btn btn-success btn-small" id="save">保存修改</button>
      </td>
    </tr>
  </tbody>
</table>

<script id="quanList" type="text/html">
  <select name="" id="" class="input-medium">
    <option value="0">请选择</option>
    {#foreach from=$quanList item=quan#}
    <option value="{#$quan.id#}">{#$quan.name#}</option>
    {#/foreach#}
  </select>
</script>
<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
