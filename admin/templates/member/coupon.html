<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$bonusName#}</title>
{#$cssFile#}
  <style>
    #import {overflow: hidden;position: relative;cursor: pointer}
    #import span{cursor: pointer}
    #Filedata {position: absolute; left: 0; top: 0; right: 0; bottom: 0; opacity: 0; filter: alpha(opacity=0); cursor: pointer;}

  </style>
</head>

<body>
<div class="search">
  <label for="keyword">搜索：</label>
  {#*
  <select class="chosen-select" id="cityid" style="width: auto;">
    <option value="">选择分站城市</option>
    {#foreach from=$cityArr item=city#}
    <option value="{#$city.id#}">{#$city.name#}</option>
    {#/foreach#}
  </select>&nbsp;&nbsp;
  *#}
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <div class="btn-group" id="stateBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="0">未使用(<span class="state0"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">已使用(<span class="state1"></span>)</a></li>
      </ul>
    </div>
    <a href="couponAdd.php" class="btn btn-primary" id="addNew">生成{#$bonusName#}</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <a href="" class="btn btn-warning" id="export">导出数据</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <div class="btn btn-info" id="import"><span>导入{#$bonusName#}</span><input type="file" accept=".xls" id="Filedata" name="Filedata"></div>
    <a href="/include/data/moneycouponImportTemp.xls?v=2" style="margin-left: 10px;">下载导入模板</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <span class="help-inline">未使用：<span id="totalAdd">0</span>元&nbsp;&nbsp;&nbsp;&nbsp;已使用：<span id="totalLess">0</span>元</span>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row17 left">密码</li>
  <li class="row10 left">金额</li>
  <li class="row15 left">过期时间</li>
  <li class="row10 left">状态</li>
  <li class="row15 left">生成时间</li>
  <li class="row15 left">使用会员</li>
  <li class="row15 left">使用时间</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
