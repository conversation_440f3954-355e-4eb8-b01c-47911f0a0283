<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>会员登录记录</title>
{#$cssFile#}
<style type="text/css">
.statistics label {display: inline-block;margin-right: 15px;cursor: text;line-height: 35px;}
</style>
</head>

<body>
<div class="search">
  <label>搜索：
  <div class="btn-group" id="ctype" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">登录方式<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部</a></li>
      <li><a href="javascript:;" data-id="电脑端">电脑端</a></li>
      <li><a href="javascript:;" data-id="H5">H5端</a></li>
      <li><a href="javascript:;" data-id="公众号">公众号</a></li>
      <li><a href="javascript:;" data-id="小程序">小程序</a></li>
      <li><a href="javascript:;" data-id="APP">APP</a></li>
      <li><a href="javascript:;" data-id="安卓APP">安卓APP</a></li>
      <li><a href="javascript:;" data-id="苹果APP">苹果APP</a></li>
      <li><a href="javascript:;" data-id="短信">短信</a></li>
      <li><a href="javascript:;" data-id="手机号码一键登录">手机号码一键登录</a></li>
      {#foreach from=$site_loginconnect item=l#}
      <li><a href="javascript:;" data-id="{#$l.code#}">{#$l.name#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  </label>
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入关键字，指定用户#123" title="输入#用户ID，可以快速搜索">
  &nbsp;&nbsp;登录日期&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="statistics">
      <a href="memberLoginLog.php?dopost=getList&do=export" class="btn btn-primary" id="export">导出</a>
      <label style="margin-left: 20px;">总次数：<span class="totalCount">0</span></label>
    </div>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row8 left">&nbsp;用户ID</li>
  <li class="row12 left">用户信息</li>
  <li class="row15 left">登录时间</li>
  <li class="row15 left">登录IP</li>
  <li class="row20 left">IP归属地</li>
  <li class="row20 left">登录方式</li>
  <li class="row10 left">设备信息</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="mtype"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>var adminPath = "{#$adminPath#}";</script>
{#$jsFile#}
</body>
</html>
