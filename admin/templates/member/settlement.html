<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>结算设置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
<style>
  .editform dd .input-prepend input[type=radio] {display: inline-block; vertical-align: middle; margin: -3px 2px 0 0; border-radius: 1em;}
  .editform dd .input-prepend input[type=checkbox] {display: inline-block; vertical-align: middle; margin: -2px 3px 0 0;}
</style>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix" style="float:left; width: 450px;">
    <dt><label for="pointName">打赏结算：</label></dt>
    <dd>
      <div class="input-prepend input-append">
        <span class="add-on">结算佣金</span>
        <input class="input-mini" type="text" name="rewardFee" value="{#$rewardFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="pointName">分站管理员：</label></dt>
    <dd>
      <div class="input-prepend input-append">
        <span class="add-on">结算佣金</span>
        <input class="input-mini" type="text" name="fzrewardFee" value="{#$fzrewardFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div><br />
      <span class="input-tips" style="display:block;margin:0 0 10px;"><s></s>城市分站高级设置中可以对各分站独立配置结算佣金比例</span>
    </dd>
  </dl>

  <hr />

  <dl class="clearfix" style="float:left; width: 450px;">
    <dt><label>商家结算：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商家买单</span>
        <input class="input-mini" type="text" name="businessMaidanFee" value="{#$businessMaidanFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商家买单({#$payname#})</span>
        <input class="input-mini" type="text" name="businessBonusMaidanFee" value="{#$businessBonusMaidanFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#if in_array("tuan", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">团购佣金</span>
        <input class="input-mini" type="text" name="tuanFee" value="{#$tuanFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("travel", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">旅游佣金</span>
        <input class="input-mini" type="text" name="travelFee" value="{#$travelFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("homemaking", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">家政佣金</span>
        <input class="input-mini" type="text" name="homemakingFee" value="{#$homemakingFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("education", $installModuleArr)#}
      <div class="input-prepend input-append">
        <span class="add-on">教育佣金</span>
        <input class="input-mini" type="text" name="educationFee" value="{#$educationFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div><br />
      {#/if#}
      {#if in_array("shop", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商城佣金</span>
        <input class="input-mini" type="text" name="shopFee" value="{#$shopFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("huodong", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">活动佣金</span>
        <input class="input-mini" type="text" name="huodongFee" value="{#$huodongFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("live", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">直播佣金</span>
        <input class="input-mini" type="text" name="liveFee" value="{#$liveFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("video", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">视频佣金</span>
        <input class="input-mini" type="text" name="videoFee" value="{#$videoFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("awardlegou", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">有奖乐购佣金</span>
        <input class="input-mini" type="text" name="awardlegouFee" value="{#$awardlegouFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("paimai", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商品拍卖佣金</span>
        <input class="input-mini" type="text" name="paimaiFee" value="{#$paimaiFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
    </dd>
  </dl>
    <dl class="clearfix">
    <dt><label>分站管理员：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商家买单</span>
        <input class="input-mini" type="text" name="fzbusinessMaidanFee" value="{#$fzbusinessMaidanFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#if in_array("tuan", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">团购佣金</span>
        <input class="input-mini" type="text" name="fztuanFee" value="{#$fztuanFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("travel", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">旅游佣金</span>
        <input class="input-mini" type="text" name="fztravelFee" value="{#$fztravelFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("homemaking", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">家政佣金</span>
        <input class="input-mini" type="text" name="fzhomemakingFee" value="{#$fzhomemakingFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("education", $installModuleArr)#}
      <div class="input-prepend input-append">
        <span class="add-on">教育佣金</span>
        <input class="input-mini" type="text" name="fzeducationFee" value="{#$fzeducationFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div><br />
      {#/if#}
      {#if in_array("shop", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商城佣金</span>
        <input class="input-mini" type="text" name="fzshopFee" value="{#$fzshopFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("waimai", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">外卖佣金</span>
        <input class="input-mini" type="text" name="fzwaimaiFee" value="{#$fzwaimaiFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">跑腿佣金</span>
        <input class="input-mini" type="text" name="fzwaimaiPaotuiFee" value="{#$fzwaimaiPaotuiFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("huodong", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">活动佣金</span>
        <input class="input-mini" type="text" name="fzhuodongFee" value="{#$fzhuodongFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("live", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">直播佣金</span>
        <input class="input-mini" type="text" name="fzliveFee" value="{#$fzliveFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("video", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">视频佣金</span>
        <input class="input-mini" type="text" name="fzvideoFee" value="{#$fzvideoFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("awardlegou", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">有奖乐购佣金</span>
        <input class="input-mini" type="text" name="fzawardlegouFee" value="{#$fzawardlegouFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("paimai", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">拍卖佣金</span>
        <input class="input-mini" type="text" name="fzpaimaiFee" value="{#$fzpaimaiFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      {#if in_array("job", $installModuleArr)#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">招聘佣金</span>
        <input class="input-mini" type="text" name="fzjobFee" value="{#$fzjobFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      {#/if#}
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">刷新置顶佣金</span>
        <input class="input-mini" type="text" name="roofFee" value="{#$roofFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">房产经纪人套餐分佣</span>
        <input class="input-mini" type="text" name="setmealFee" value="{#$setmealFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">发布信息佣金</span>
        <input class="input-mini" type="text" name="fabulFee" value="{#$fabulFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">会员升级佣金</span>
        <input class="input-mini" type="text" name="levelFee" value="{#$levelFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">商家入驻佣金</span>
        <input class="input-mini" type="text" name="storeFee" value="{#$storeFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">分销商入驻佣金</span>
        <input class="input-mini" type="text" name="fenxiaoFee" value="{#$fenxiaoFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">激励佣金</span>
        <input class="input-mini" type="text" name="jiliFee" value="{#$jiliFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">付费查看电话佣金</span>
        <input class="input-mini" type="text" name="payPhoneFee" value="{#$payPhoneFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
    </dd>
  </dl>

  <hr />

  <dl class="clearfix">
    <dt><label>充值设置：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">充值优惠开关：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="chongzhiCheckType" value="0"{#if !$chongzhiCheckType#} checked{#/if#}>关闭</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="chongzhiCheckType" value="1"{#if $chongzhiCheckType#} checked{#/if#}>开启</label>&nbsp;</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">优惠比例：</span>
        <input class="input-mini" type="text" name="chongzhiyhFee" value="{#$chongzhiyhFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">优惠限制：</span>
        <input class="input-mini" type="text" name="chongzhilimit" value="{#$chongzhilimit#}">
        <span class="add-on" style="display: inline-block;">元</span>
        <span class="help-inline">最低充值金额才可享受优惠</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">充值送{#$pointName#}开关：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="chongzhiSongJiFen" value="0"{#if !$chongzhiSongJiFen#} checked{#/if#}>关闭</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="chongzhiSongJiFen" value="1"{#if $chongzhiSongJiFen#} checked{#/if#}>开启</label>&nbsp;</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">赠送比例：</span>
        <input class="input-mini" type="text" name="chongzhijfFee" value="{#$chongzhijfFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">赠送限制</span>
        <input class="input-mini" type="text" name="chongzhiJfLimit" value="{#$chongzhiJfLimit#}">
        <span class="add-on" style="display: inline-block;">元</span>
        <span class="help-inline">最低充值金额才送{#$pointName#}</span>
      </div>
    </dd>
  </dl>

  <hr />

  <dl class="clearfix">
    <dt><label>商家自动提现：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">功能开关：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="businessAutoWithdrawState" value="0"{#if !$businessAutoWithdrawState#} checked{#/if#}>关闭</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="businessAutoWithdrawState" value="1"{#if $businessAutoWithdrawState#} checked{#/if#}>开启</label>&nbsp;</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">提现时间：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="businessAutoWithdrawCycle" value="0" value="0"{#if $businessAutoWithdrawCycle == 0#} checked{#/if#}>随时</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="businessAutoWithdrawCycle" value="1" value="1"{#if $businessAutoWithdrawCycle == 1#} checked{#/if#}>每周</label>&nbsp;</span>
        <select name="businessAutoWithdrawCycleWeek" class="input-mini" style="margin-right: 0;">
          <option value="1"{#if $businessAutoWithdrawCycleWeek == 1#} selected{#/if#}>一</option>
          <option value="2"{#if $businessAutoWithdrawCycleWeek == 2#} selected{#/if#}>二</option>
          <option value="3"{#if $businessAutoWithdrawCycleWeek == 3#} selected{#/if#}>三</option>
          <option value="4"{#if $businessAutoWithdrawCycleWeek == 4#} selected{#/if#}>四</option>
          <option value="5"{#if $businessAutoWithdrawCycleWeek == 5#} selected{#/if#}>五</option>
          <option value="6"{#if $businessAutoWithdrawCycleWeek == 6#} selected{#/if#}>六</option>
          <option value="0"{#if !$businessAutoWithdrawCycleWeek#} selected{#/if#}>日</option>
        </select>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="businessAutoWithdrawCycle" value="2" value="2"{#if $businessAutoWithdrawCycle == 2#} checked{#/if#}>每月</label>&nbsp;</span>
        <input class="input-mini" type="text" name="businessAutoWithdrawCycleDay" value="{#$businessAutoWithdrawCycleDay#}">
        <span class="add-on" style="display: inline-block;">日</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">最小金额：</span>
        <input class="input-mini" type="text" name="businessAutoWithdrawAmount" value="{#$businessAutoWithdrawAmount#}">
        <span class="add-on" style="display: inline-block;">元</span>
        <span class="help-inline">商家账户余额大于等于此金额才会自动提现</span>
      </div>
      <div style="font-size: 12px; color: rgb(153, 153, 153);">开启此功能后，将会对满足条件的商家自动发起提现，提现的账户为商家会员绑定的微信账户；<br />请确保商家会员已经绑定微信并且做好实名认证，注意：实名认证的姓名必须和绑定的微信认证姓名一致，否则会打款失败；<br />说明：该功能开启后只会生成提现记录，不执行自动转账功能，需要管理员进入提现管理页面审核；<br />另外，还需要新增执行文件为<code>member_autoWithdraw.php</code>的计划任务！</div>
    </dd>
  </dl>

  <hr />

  <dl class="clearfix">
    <dt><label>提现设置：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">起提金额</span>
        <input class="input-mini" type="text" name="minWithdraw" value="{#$minWithdraw#}">
        <span class="add-on" style="display: inline-block;">元</span>
        <span class="help-inline">单次限制，大于等于0.01</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">最多提现</span>
        <input class="input-mini" type="text" name="maxWithdraw" value="{#$maxWithdraw#}">
        <span class="add-on" style="display: inline-block;">元</span>
        <span class="help-inline">单次限制，0为不限制</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">手续费：</span>
        <input class="input-mini" type="text" name="withdrawFee" value="{#$withdrawFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
        <span class="help-inline">会员提现手续费，注意：分站和骑手提现没有手续费；</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">提现扣{#$pointName#}：</span>
        <input class="input-mini" type="text" name="withdrawJfFee" value="{#$withdrawJfFee#}">
        <span class="add-on" style="display: inline-block;">%</span>
        <span class="help-inline">会员提现扣{#$pointName#}比例，注意：分站和骑手提现不扣{#$pointName#}；</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on" style="vertical-align: top!important;">每天最多</span>
        <input class="input-mini" type="text" style="vertical-align: top;" name="maxCountWithdraw" value="{#$maxCountWithdraw#}">
        <span class="add-on" style="display: inline-block; vertical-align: top!important;">次</span>
        <input class="input-mini" type="text" style="vertical-align: top;" name="maxAmountWithdraw" value="{#$maxAmountWithdraw#}">
        <span class="add-on" style="display: inline-block; vertical-align: top!important;">元</span>
        <span class="help-inline">0为不限制，例如：<br />[2次，10元]：每天最多申请2次，每天最多提现10元；<br />[3次，0元]：每天最多申请3次，每天不限制提现金额；<br />[0次，10元]：每天不限制提现次数，每天最多提现10元；<br />[0次，0元]：每天不限制提现次数，每天不限制提现金额</span>
      </div>


      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">会员提现周期：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawCycle" value="0" value="0"{#if !$withdrawCycle#} checked{#/if#}>不限制</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawCycle" value="1" value="1"{#if $withdrawCycle == 1#} checked{#/if#}>每周</label>&nbsp;</span>
        <select name="withdrawCycleWeek" class="input-mini" style="margin-right: 0;">
          <option value="1"{#if $withdrawCycleWeek == 1#} selected{#/if#}>一</option>
          <option value="2"{#if $withdrawCycleWeek == 2#} selected{#/if#}>二</option>
          <option value="3"{#if $withdrawCycleWeek == 3#} selected{#/if#}>三</option>
          <option value="4"{#if $withdrawCycleWeek == 4#} selected{#/if#}>四</option>
          <option value="5"{#if $withdrawCycleWeek == 5#} selected{#/if#}>五</option>
          <option value="6"{#if $withdrawCycleWeek == 6#} selected{#/if#}>六</option>
          <option value="0"{#if !$withdrawCycleWeek#} selected{#/if#}>日</option>
        </select>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawCycle" value="2" value="2"{#if $withdrawCycle == 2#} checked{#/if#}>每月</label>&nbsp;</span>
        <input class="input-mini" type="text" name="withdrawCycleDay" value="{#$withdrawCycleDay#}">
        <span class="add-on" style="display: inline-block;">日</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">骑手提现周期：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="courierWithdrawCycle" value="0" value="0"{#if !$courierWithdrawCycle#} checked{#/if#}>不限制</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="courierWithdrawCycle" value="1" value="1"{#if $courierWithdrawCycle == 1#} checked{#/if#}>每周</label>&nbsp;</span>
        <select name="courierWithdrawCycleWeek" class="input-mini" style="margin-right: 0;">
          <option value="1"{#if $courierWithdrawCycleWeek == 1#} selected{#/if#}>一</option>
          <option value="2"{#if $courierWithdrawCycleWeek == 2#} selected{#/if#}>二</option>
          <option value="3"{#if $courierWithdrawCycleWeek == 3#} selected{#/if#}>三</option>
          <option value="4"{#if $courierWithdrawCycleWeek == 4#} selected{#/if#}>四</option>
          <option value="5"{#if $courierWithdrawCycleWeek == 5#} selected{#/if#}>五</option>
          <option value="6"{#if $courierWithdrawCycleWeek == 6#} selected{#/if#}>六</option>
          <option value="0"{#if !$courierWithdrawCycleWeek#} selected{#/if#}>日</option>
        </select>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="courierWithdrawCycle" value="2" value="2"{#if $courierWithdrawCycle == 2#} checked{#/if#}>每月</label>&nbsp;</span>
        <input class="input-mini" type="text" name="courierWithdrawCycleDay" value="{#$courierWithdrawCycleDay#}">
        <span class="add-on" style="display: inline-block;">日</span>
      </div>

      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">提现平台：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="checkbox" name="withdrawPlatform[]" value="weixin"{#if $withdrawPlatform && in_array("weixin", $withdrawPlatform)#} checked{#/if#}>微信</label>&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="checkbox" name="withdrawPlatform[]" value="alipay"{#if $withdrawPlatform && in_array("alipay", $withdrawPlatform)#} checked{#/if#}>支付宝</label>&nbsp;&nbsp;&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="checkbox" name="withdrawPlatform[]" value="bank"{#if $withdrawPlatform && in_array("bank", $withdrawPlatform)#} checked{#/if#}>银联</label>(只支持线下转账)</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">微信接口：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawWxVersion" value="2"{#if $withdrawWxVersion==2#} checked{#/if#}>企业付款到零钱</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawWxVersion" value="3"{#if $withdrawWxVersion==3#} checked{#/if#}>商家转账到零钱【新】</label>&nbsp;</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on">付款方式：</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawCheckType" value="0"{#if !$withdrawCheckType#} checked{#/if#}>会员申请后自动付款</label>&nbsp;</span>
        <span class="add-on" style="display: inline-block;"><label><input type="radio" name="withdrawCheckType" value="1"{#if $withdrawCheckType#} checked{#/if#}>会员申请后手动审核付款</label>&nbsp;</span>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on" style="vertical-align: top!important;">会员提现说明：</span>
        <textarea class="input-xxlarge" style="height: 200px;" name="withdrawNote" id="withdrawNote">{#$withdrawNote#}</textarea>
      </div>
      <div class="input-prepend input-append" style="display:block;">
        <span class="add-on" style="vertical-align: top!important;">骑手提现说明：</span>
        <textarea class="input-xxlarge" style="height: 200px;" name="courierwithdrawNote" id="courierwithdrawNote">{#$courierwithdrawNote#}</textarea>
      </div>
      <br />
      <p>备注：<br />微信提现到零钱配置教程：<a href="https://help.kumanyun.com/help-107-790.html" target="_blank">https://help.kumanyun.com/help-107-790.html</a><br />微信支付API证书配置教程：<a href="https://help.kumanyun.com/help-66-605.html" target="_blank">https://help.kumanyun.com/help-66-605.html</a><br />微信商家转账到零钱开通地址：<a href="https://pay.weixin.qq.com/index.php/public/product/detail?pid=108&productType=0" target="_blank">https://pay.weixin.qq.com/index.php/public/product/detail?pid=108&productType=0</a><br />支付宝需要签约《单笔转账到账户》功能，开通地址：<a href="https://openhome.alipay.com/platform/publicAppManage.htm#/apps" target="_blank">https://openhome.alipay.com/platform/publicAppManage.htm#/apps</a><br /><br />微信限额介绍：<a href="https://pay.weixin.qq.com/docs/merchant/products/batch-transfer-to-balance/introduction.html" target="_blank">https://pay.weixin.qq.com/docs/merchant/products/batch-transfer-to-balance/introduction.html</a>；<br />支付宝限额介绍：<a href="https://opendocs.alipay.com/open/309#s2" target="_blank">https://opendocs.alipay.com/open/309#s2</a><br /><br />微信企业付款到零钱查账地址：<a href="https://pay.weixin.qq.com/index.php/core/sp_transfer/transferquery" target="_blank">https://pay.weixin.qq.com/index.php/core/sp_transfer/transferquery</a><br />微信商家转账到零钱查账地址：<a href="https://pay.weixin.qq.com/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch" target="_blank">https://pay.weixin.qq.com/xdc/mchtranstemplate/index.php/xphp/cgi/page/batch</a><br />支付宝查账地址：<a href="https://mbillexprod.alipay.com/enterprise/fundList.htm#/transfer" target="_blank">https://mbillexprod.alipay.com/enterprise/fundList.htm#/transfer</a><br /><br />请确保微信商户平台和支付宝账户余额充足，否则将无法提现。</p>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
