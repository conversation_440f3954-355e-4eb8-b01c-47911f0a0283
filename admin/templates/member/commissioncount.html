<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>佣金统计</title>
{#$cssFile#}
</head>
<script type="text/javascript">

</script>
<body>
<div class="search">
  <label for="keyword">搜索：</label>
  <select class="chosen-select" id="cityid" style="width: auto;">
    <option value="">选择分站城市</option>
    {#foreach from=$cityArr item=city#}
    <option value="{#$city.id#}"{#if $cityid == $city.id#} selected{#/if#}>{#$city.name#}</option>
    {#/foreach#}
  </select>&nbsp;&nbsp;
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>&nbsp;&nbsp;&nbsp;&nbsp;
  <a href="javascript:;" class="btn btn-primary" id="export">导出</a>&nbsp;&nbsp;&nbsp;&nbsp;
  <div class="btn-group" id="leimuBtn">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部信息</a></li>
      {#foreach $leimuallarr as $k => $v#}
      <li><a href="javascript:;" data-id="{#$k#}">{#$v#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <div class="btn-group" id="stateBtn">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(数量:<span class="totalCount"></span>)(金额:<span class="totalMoney"></span>)</button>
    <ul class="dropdown-menu" id="typeinfolist"></ul>
  </div>
</div>

<div class="filter clearfix">
  <div class="f-left" style="margin-top: 7px; padding-left: 0;">
    {#if $updateCityAdminAmount#}
    <a href="javascript:;" class="btn btn-primary" id="updataPro"style="margin-right: 10px;">更新余额</a>
    {#/if#}
    <span class="help-inline">总佣金：<span id="totalAdd">0</span>&nbsp;&nbsp;&nbsp;&nbsp;可提现: <span id="fzmoney">0</span> {#if $mtype==3#}&nbsp;&nbsp;&nbsp;&nbsp;<a href="userWithdraw.php"  id="userWithdraw"">发起提现</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="catRecord.php"  id="catrecord"">查看提现记录</a>{#/if#}</span>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row5 left">分站</li>
  <li class="row10 left">模块</li>
  <li class="row10 left">类目</li>
  <li class="row10 left">会员</li>
  <li class="row10 left">佣金</li>
  <li class="row10 left">交易后余额</li>
  <li class="row30 left">信息</li>
  <li class="row12 left">时间</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>
  var adminPath = "{#$adminPath#}";
  var cityid = "{#$cityid#}";
  var cityList = {#$cityList#};
</script>
<script id="quickEdit" type="text/html">
  <form action="" class="quick-editForm" name="editForm">

    <dl class="clearfix">
      <dt>操作：</dt>
      <dd>
        <label><input type="radio" name="type" value="1" checked />增加</label>&nbsp;&nbsp;
        <label><input type="radio" name="type" value="0" />减少</label>&nbsp;&nbsp;
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>操作金额：</dt>
      <dd><input type="text" id="opmoney" name="money" placeholder="{#echoCurrency type='symbol'#}" /></dd>
    </dl>

    <dl class="clearfix">
      <dt>操作说明：</dt>
      <dd><input type="text" id="opeinfo" name="info" /></dd>
    </dl>
    <input type="hidden" name="cityid" value="{#$cityid#}">

  </form>
</script>
{#$jsFile#}
</body>
</html>
