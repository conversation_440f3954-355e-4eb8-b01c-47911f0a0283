<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var thumbSize = {#$thumbSize#}, thumbType = "{#$thumbType#}",  //缩略图配置
	adminPath = "{#$adminPath#}", typeListArr = {#$typeListArr#}, typeid = {#$typeid#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="title">图片名称：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="title" id="title" value="{#$title#}" maxlength="30" data-regex=".{2,30}" />
      <span class="input-tips"><s></s>请输入图片名称，2-30位</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="addr">所属分类：</label></dt>
    <dd>
      <span id="typeList">
        <select name="typeid" id="typeid" class="input-large"></select>
      </span>
      <span class="input-tips"><s></s>请选择图片所属分类</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>缩略图：<br />245*80&nbsp;&nbsp;&nbsp;</dt>
		<dd class="thumb fn-clear listImgBox">
			<div class="uploadinp filePicker thumbtn{#if $litpic != ""#} hide{#/if#}" id="filePicker1" data-type="thumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
			{#if $litpic != ""#}
			<ul id="listSection1" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_1"><a href='{#$cfg_attachment#}{#$litpic#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$litpic#}" data-val="{#$litpic#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
			{#else#}
			<ul id="listSection1" class="listSection thumblist fn-clear"></ul>
			{#/if#}
			<input type="hidden" name="litpic" value="{#$litpic#}" class="imglist-hidden" id="litpic">
		</dd>
  </dl>
  <dl class="clearfix">
    <dt>大图：<br />900*180&nbsp;&nbsp;&nbsp;</dt>
		<dd class="thumb fn-clear listImgBox">
			<div class="uploadinp filePicker thumbtn{#if $big != ""#} hide{#/if#}" id="filePicker2" data-type="card"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
			{#if $big != ""#}
			<ul id="listSection2" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_1_1"><a href='{#$cfg_attachment#}{#$big#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$big#}" data-val="{#$big#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
			{#else#}
			<ul id="listSection2" class="listSection thumblist fn-clear"></ul>
			{#/if#}
			<input type="hidden" name="big" value="{#$big#}" class="imglist-hidden" id="big">
		</dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="rec">推荐：</label></dt>
    <dd class="radio">
      <label><input type="checkbox" name="rec" id="rec" value="1"{#if $rec == 1#} checked{#/if#} /></label>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
