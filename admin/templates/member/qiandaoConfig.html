<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>签到设置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", modelType = 'siteConfig';
var lianqian = {#$lianqian#}, zongqian = {#$zongqian#}, teshu = {#$teshu#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>签到状态：</label></dt>
    <dd class="radio">
      {#html_radios name="qiandaoState" values=$qiandaoState checked=$qiandaoStateChecked output=$qiandaoStateNames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>补签状态：</label></dt>
    <dd class="radio">
      {#html_radios name="buqianState" values=$buqianState checked=$buqianStateChecked output=$buqianStateNames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="pointName">补签扣除：</label></dt>
    <dd>
      <div class="input-append">
        <input class="span1" id="buqianPrice" name="buqianPrice" type="number" min="1" value="{#$buqianPrice#}">
        <span class="add-on">积分</span>
      </div>
    </dd>
  </dl>
  <div class="thead" style="margin-top: 0;">&nbsp;&nbsp;奖励配置</div>
  <dl class="clearfix">
    <dt><label for="pointRatio">普通奖励：</label></dt>
    <dd>
      <div class="input-prepend input-append" style="margin-bottom:0">
        <span class="add-on">首次奖励</span>
        <input class="input-mini" id="firstReward" name="firstReward" type="number" min="1" value="{#$firstReward#}">
        <span class="add-on">积分</span>
      </div>
      <div class="input-prepend input-append" style="margin-bottom:0">
        <span class="add-on">日常奖励</span>
        <input class="input-mini" id="reward" name="reward" type="number" min="1" value="{#$reward#}">
        <span class="add-on">积分</span>
      </div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="pointRatio">连签奖励：</label></dt>
    <dd>
      <a href="javascript:;" class="btn btn-small addNew" data-id="lianqian" id="lianqianBtn">新增连签奖励规则</a>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="pointRatio">总签奖励：</label></dt>
    <dd>
      <a href="javascript:;" class="btn btn-small addNew" data-id="zongqian" id="zongqianBtn">新增总签奖励规则</a>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="pointRatio">特殊奖励：</label></dt>
    <dd>
      <a href="javascript:;" class="btn btn-small addNew" data-id="teshu" id="teshuBtn">新增特殊奖励规则</a>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>签到规则：</dt>
    <dd><script id="body" name="body" type="text/plain" style="width:85%;height:300px">{#$body#}</script></dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

<script id="lianqian" type="text/html">
  <div class="item">
    <div class="input-prepend input-append">
      <span class="add-on">连续签到</span>
      <input class="input-mini day" name="lianqian_day[]" type="number" min="1">
      <span class="add-on">天，奖励</span>
      <input class="input-mini reward" name="lianqian_reward[]" type="number" min="1">
      <span class="add-on">积分</span>
    </div>
    <span title="删除" class="remove"><i class="icon-remove"></i></span>
  </div>
</script>

<script id="zongqian" type="text/html">
  <div class="item">
    <div class="input-prepend input-append">
      <span class="add-on">总签到</span>
      <input class="input-mini day" name="zongqian_day[]" type="number" min="1">
      <span class="add-on">天，奖励</span>
      <input class="input-mini reward" name="zongqian_reward[]" type="number" min="1">
      <span class="add-on">积分</span>
    </div>
    <span title="删除" class="remove"><i class="icon-remove"></i></span>
  </div>
</script>

<script id="teshu" type="text/html">
  <div class="item">
    <div class="input-prepend input-append">
      <span class="add-on">日期</span>
      <input class="input-small date" name="teshu_date[]" type="text">
      <span class="add-on">标题</span>
      <input class="input-mini title" name="teshu_title[]" type="text">
      <span class="add-on">颜色</span>
      <div class="color_pick" style="margin-left: 0; border-left: 1px solid #ccc;"><em style="background:#cccccc;"></em></div>
      <input type="hidden" class="color-input" name="teshu_color[]" value="#cccccc">
      <span class="add-on">奖励</span>
      <input class="input-mini reward" name="teshu_reward[]" type="number" min="1">
      <span class="add-on">积分</span>
    </div>
    <span title="删除" class="remove"><i class="icon-remove"></i></span>
  </div>
</script>

{#$editorFile#}
{#$jsFile#}
</body>
</html>
