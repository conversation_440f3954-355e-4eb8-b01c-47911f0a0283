<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>现金消费管理</title>
{#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 10px 0;"><button type="button" class="close" data-dismiss="alert">×</button>该数据用于统计用户在每笔交易中(使用了第三方支付通道，如微信/支付宝等)，平台支付给第三方的手续费！该功能需要提前配置好支付方式中的手续费比例，<a id="sitePaymentLink" href="siteConfig/sitePayment.php">点击前往配置>></a></div>

<div class="search">
  <label for="keyword">搜索：</label>
  <div class="btn-group" id="leimuBtn" noList="1">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息<span class="caret"></span></button>
    <ul class="dropdown-menu" >
      <li><a href="javascript:;" data-id="">全部信息</a></li>
      {#foreach $leimuallarr as $k => $v#}
      <li><a href="javascript:;" data-id="{#$k#}">{#$v#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <div class="btn-group" id="shouruBtn" noList="1">
    <button class="btn dropdown-toggle" data-toggle="dropdown">支付方式<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="" data-nolist="true">所有方式</a></li>
      {#foreach $typeallarr as $k => $v#}
      <li><a href="javascript:;" data-id="{#$k#}" data-nolist="true">{#$v#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字">
  &nbsp;&nbsp;从&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>&nbsp;&nbsp;&nbsp;&nbsp;
</div>


<div class="filter clearfix">
  <div class="f-left">
<!--    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>-->
<!--    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>-->
<!--    <div class="btn-group" id="stateBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">充值(<span class="state0"></span>)</a></li>
        <li><a href="javascript:;" data-id="2">佣金(<span class="state1"></span>)</a></li>
      </ul>
    </div>-->
    <a href="" class="btn btn-primary" id="export">导出</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <span class="help-inline">手续费总金额：<span id="totalLess">0</span></span>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
<!--  <li class="row3">&nbsp;</li>-->
  <li class="row10 left">&nbsp;&nbsp;类目</li>
  <li class="row20 left">交易会员</li>
  <li class="row15 left">手续费/订单金额</li>
  <li class="row30 left">交易方式/订单号/第三方订单号</li>
  <li class="row15 left">时间</li>
  <li class="row10">订单信息</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
