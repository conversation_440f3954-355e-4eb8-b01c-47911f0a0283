<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>数据库内容替换</title>
{#$cssFile#}
</head>

<body>
<div class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt>点击选择表：</dt>
    <dd>
      <div class="dbtable">
        <ul class="theader clearfix">
          <li class="row40">表名</li>
          <li class="row10 center">行数</li>
          <li class="row10 center">大小</li>
          <li class="row40">注释</li>
        </ul>
        <div class="tbody" id="tablist"><p><center><br />数据表加载中...<br /><br /></center></p></div>
      </div>
    </dd>
  </dl>
  <dl class="clearfix hide" id="fields">
    <dt>点击选择字段：</dt>
    <dd>
      <small></small>
      <div></div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>查找：</dt>
    <dd><input type="text" name="rpstring" id="rpstring" class="input-xxlarge" /></dd>
  </dl>
  <dl class="clearfix">
    <dt>替换：</dt>
    <dd><input type="text" name="tostring" id="tostring" class="input-xxlarge" /></dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="button" name="button" id="btnSubmit">确认提交</button><span class="help-inline">此操作极为危险，请小心使用。</span></dd>
  </dl>
</div>

<script>var adminPath = "{#$adminPath#}";</script>
{#$jsFile#}
</body>
</html>