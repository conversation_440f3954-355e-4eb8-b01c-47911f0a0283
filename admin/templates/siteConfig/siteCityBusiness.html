<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>城市区域商圈</title>
{#$cssFile#}
</head>
<style>
.markditu {margin-right: 15px;}
</style>
<body>
<div class="search">
  <div class="btn-group">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$typename#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="siteCityBusiness.php?cid={#$cid#}">所有区域</a></li>
      {#foreach from=$area item=q#}
      <li><a href="siteCityBusiness.php?cid={#$cid#}&qid={#$q.id#}">{#$q.typename#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <button type="button" class="btn btn-primary ml30">新增商圈</button>
</div>
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px 10px 0;">
  <li class="row2">&nbsp;</li>
  <li class="row15 left">区域</li>
  <li class="row20 left">名称</li>
  <li class="row12">开放时间</li>
  <li class="row12">联系电话</li>
  <li class="row15">经纬度</li>
  <li class="row12">缩略图</li>
  <li class="row12">操作</li>
</ul>
<div class="list mt124" id="list">
  <table>
    <tbody>
      {#if $list|count == 0#}
      <tr><td style="height: 200px;">暂无信息。</td></tr>
      {#/if#}
      {#foreach from=$list key=k item=a#}
      <tr class="citylist" data-id="{#$a.id#}">
        <td class="row2"></td>
        <td class="row15 left">{#$a.typename#}</td>
        <td class="row20 left"><input type="text" data-id="{#$a.id#}" value="{#$a.name#}" /><label style="display:inline-block; margin-left:10px;"><input type="checkbox" class="hot" value="1"{#if $a.hot == 1#} checked{#/if#}>热门</label></td>
        <td class="row12"><div class="input-prepend input-append"><input class="input-mini openStart" type="text" name="openStart" id="openStart" value="{#$a.openStart#}"><span class="add-on">到</span><input class="input-mini openEnd" type="text" name="openEnd" id="openEnd" value="{#$a.openEnd#}"></div></td>
        <td class="row12"><input class="input-large" type="text" name="tel" id="tel" value="{#$a.tel#}" /></td>
        <td class="row15"><a data-lnglat="{#$a.lnglat#}" data-id="{#$a.id#}" data-city="{#$a.cityname#}" href="javascript:;" class="markditu" title="标注区域位置"><img src="/static/images/admin/markditu.jpg"></a><input disabled class="input-large" type="hidden" name="lng" id="lng" value="{#$a.lng#}"/><input disabled class="input-large" type="hidden" name="lat" id="lat" value="{#$a.lat#}"/><input class="input-large" type="text" name="address" id="address" value="{#$a.address#}"/></td>
        <td class="row12">{#if !empty($a.litpic)#}<img src="{#getFilePath($a.litpic)#}" class="img" alt="" style="height:40px;">{#/if#}<a style="color:#2672ec;" href="javascript:;" class="upfile" title="删除">上传图标</a><input type="file" name="Filedata" value="" class="imglist-hidden Filedata hide" id="Filedata_{#$k+1#}"><input type="hidden" name="icon" class="icon" value=""></td>
        <td class="row12"><a href="javascript:;" class="del">删除</a></td>
      </tr>
      {#/foreach#}
    </tbody>
  </table>
</div>
<div class="fix-btn"><button type="button" class="btn btn-success" id="saveBtn">保存图片</button></div>
<script id="addNew" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
    <dl class="clearfix">
      <dt>所属区域：</dt>
      <dd>
        <select id="qBtn" name="qBtn" style="width:130px;">
          <option value="">--区域--</option>
          {#foreach from=$area item=q#}
          <option value="{#$q.id#}">{#$q.typename#}</option>
          {#/foreach#}
        </select>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>商圈名称：</dt>
      <dd><input class="input-large" type="text" name="name" id="name" /></dd>
    </dl>
    <dl class="clearfix">
      <dt>属性：</dt>
      <dd><label><input type="checkbox" name="hot" id="hot" value="1" />热门</label></dd>
    </dl>
  </form>
</script>


<script>var adminPath = "{#$adminPath#}", cid = {#$cid#};</script>
{#$jsFile#}
</body>
</html>
