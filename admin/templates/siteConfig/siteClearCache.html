<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>清除页面缓存</title>
{#$cssFile#}
<style>
    #modules label {min-width: 85px;}
</style>
</head>

<body>
<form action="?action=do" method="post" name="editform" id="editform" class="editform">
    <dl class="clearfix">
      <dt><label>重要缓存：</label></dt>
      <dd class="radio">
          {#if $redis#}
          <label><input type="checkbox" name="memory" value="redis" /><span>redis缓存<small style="color: #999;"> (<font color="#ff0000">非必要勿删</font>{#if in_array("shop", $installModuleArr)#}，如果商城模块有商品处于活动中状态，清除redis缓存后，请到商品活动管理列表将已审核的商品重新操作已审核，否则将影响下单！{#/if#})</small></span></label>
          <br />
          {#/if#}
          <label><input type="checkbox" name="staticPath" value="1" /><span>纯静态页面<small style="color: #999;"> (<font color="#ff0000">非必要勿删</font>，如果修改了模板后没有生效，可以尝试勾选此项进行清除，或者手动删除网站根目录/templates_c/html/中的文件)</small></span></label>
          <br />
          <label><input type="checkbox" name="staticlog" value="1" /><span>系统日志/log/<small style="color: #999;"> (<font color="#ff0000">非必要勿删</font>，该日志用于技术人员排查系统问题)</small></span></label>
      </dd>
    </dl>
  <dl class="clearfix">
    <dt><label>模板缓存：</label></dt>
    <dd class="radio" id="modules">
      <label><input type="checkbox" name="module[]" value="siteConfig" checked /><span>基本配置</span></label>
      <label><input type="checkbox" name="module[]" value="member" checked /><span>会员中心</span></label>
      {#foreach from=$moduleList item=module#}
      <label><input type="checkbox" name="module[]" value="{#$module.name#}" checked /><span>{#$module.title#}</span></label>
      {#/foreach#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>其它缓存：</label></dt>
    <dd class="radio">
        <label><input type="checkbox" name="type" value="1" checked /><span>后台缓存</span></label>
        <br />
        <label><input type="checkbox" name="static" value="1" checked /><span>静态资源文件<small style="color: #999;"> (主要用于css/js/图标等文件，如果修改了该类型的文件后页面没有生效，可以尝试勾选此项进行清除)</small></span></label>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-success" type="submit" name="button" id="btnSubmit">确认提交</button>&nbsp;&nbsp;&nbsp;&nbsp;<label><input id="selectAll" type="checkbox" checked /><span>反选</span></label></dd>
  </dl>
</form>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
