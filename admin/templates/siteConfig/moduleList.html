<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>网站模块管理</title>
{#$cssFile#}
<style media="screen">
  .filter a {display: inline-block; vertical-align: middle; color: #999; line-height: 1.3em; text-align: center; font-size: 12px; margin-top: 12px;}
  .filter a.curr {color: #2672ec;}
  #list .img {margin-right: 5px;}
  #list .label {font-weight: 500; text-shadow: none;}
  .list .tr div {padding-top: 5px;}
  .list .filter {background: transparent; padding: 0;}
  .filter .check {margin-right: 0;}
  .list li.placeholder {height: 48px;}
  .list .tr .color_pick {padding: 2px; border-left: 1px solid #ccc; margin-right: 15px; margin-top: 8px; height: 25px;}
  .color_pick em {width: 25px; height: 25px;}
  .upfile {font-size: 12px;}
  input[type=text] {font-size: 12px;}
  .thead li {text-indent: 0;}
  .split {display: inline-block; vertical-align: middle; width: 1px; height: 25px; background-color: #ccc; margin: 0 5px;}
  .thead .split {height: 15px; margin: 0 5px 0 3px;}
</style>
</head>

<body>
<div class="search" style="position:relative;">
  {#if $installMoudulePurview#}
  <button class="btn btn-info" data-toggle="dropdown" id="installNew" style="margin-right: 15px;">安装更多模块</button>
  {#/if#}
  <div class="btn-group operBtn">
    <button class="btn dropdown-toggle" data-toggle="dropdown">更多操作<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-platform="subnav">导入系统默认数据</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="pc" data-type="open">启用所有电脑端</a></li>
      <li><a href="javascript:;" data-platform="pc" data-type="close">停用所有电脑端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="h5" data-type="open">启用所有H5端</a></li>
      <li><a href="javascript:;" data-platform="h5" data-type="close">停用所有H5端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="app" data-type="open">启用所有APP端</a></li>
      <li><a href="javascript:;" data-platform="app" data-type="close">停用所有APP端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="wx" data-type="open">启用所有微信小程序端</a></li>
      <li><a href="javascript:;" data-platform="wx" data-type="close">停用所有微信小程序端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="bd" data-type="open">启用所有百度小程序端</a></li>
      <li><a href="javascript:;" data-platform="bd" data-type="close">停用所有百度小程序端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="qm" data-type="open">启用所有QQ小程序端</a></li>
      <li><a href="javascript:;" data-platform="qm" data-type="close">停用所有QQ小程序端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="dy" data-type="open">启用所有抖音小程序端</a></li>
      <li><a href="javascript:;" data-platform="dy" data-type="close">停用所有抖音小程序端</a></li>
	  <li class="divider"></li>
      <li><a href="javascript:;" data-platform="state" data-type="open">启用所有模块</a></li>
      <li><a href="javascript:;" data-platform="state" data-type="close">停用所有模块</a></li>
    </ul>
  </div>
</div>

<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:0 10px;">
  <li class="row7">模块</li>
  <li class="row10 left">导航名</li>
  <li class="row17 left">导航链接</li>
  <li class="row10 left">图标</li>
  <li class="row15 left">配置</li>
  <li class="row22 left">前端页面开关&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<s class="split"></s>&nbsp;&nbsp;小程序</li>
  <li class="row9">排序</li>
  <li class="row10 left">操作</li>
</ul>

<div class="list mb50" id="list">
  <ul class="root"></ul>
  <div class="tr clearfix">
    <div class="row7"></div>
    <div class="row80 left"><a href="javascript:;" class="add-type" style="display:inline-block; margin-left: 0;" id="addNew">添加自定义导航</a></div>
  </div>
  <div class="fix-btn">
    <button type="button" class="btn btn-success" id="saveBtn">保存修改</button>
    <span class="help-inline" style="line-height: 35px; margin: 10px 0 0 20px;">修改完请及时保存~</span>
  </div>
  <!-- <button type="button" class="btn btn-success" id="saveBtn">保存</button> -->
</div>

<script id="editForm" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
	<dl class="clearfix">
      <dt>模块标识：</dt>
      <dd id="name"></dd>
    </dl>
    <dl class="clearfix">
      <dt>模块名称：</dt>
      <dd><input class="input-xlarge" type="text" name="title" id="title" value="" /></dd>
    </dl>
	<dl class="clearfix">
      <dt>模块图标：</dt>
      <dd>
	    <input class="input-xlarge" type="text" name="icon" id="icon" value="" /><br />
	    图片位于：/static/images/admin/nav/
      </dd>
    </dl>
    <!-- <dl class="clearfix">
      <dt style="color: red; font-weight: 700;">子域名规则：<small style="display: block; color: #666; text-align: left; font-weight: 500; line-height: 20px;">错误的修改将导致网站无法正常访问<br />请谨慎操作！！！</small></dt>
      <dd><textarea id="domainRules" name="domainRules" style="width:95%; height:150px;" /></textarea></dd>
    </dl>
    <dl class="clearfix">
      <dt style="color: red; font-weight: 700;">子目录规则：<small style="display: block; color: #666; text-align: left; font-weight: 500; line-height: 20px;">错误的修改将导致网站无法正常访问<br />请谨慎操作！！！</small></dt>
      <dd><textarea id="catalogRules" name="catalogRules" style="width:95%; height:150px;" /></textarea></dd>
    </dl> -->
    <dl class="clearfix hide">
      <dt>所属目录：</dt>
      <dd><select name="parentid" id="parentid" class="input-large"></select></dd>
    </dl>
    <dl class="clearfix">
      <dt>状态：</dt>
      <dd class="clearfix">
        <label><input type="radio" name="state" value="0" />启用</label>
        <label><input type="radio" name="state" value="1" />停用</label>
      </dd>
    </dl>
    <dl class="clearfix wx">
      <dt>小程序：</dt>
      <dd class="clearfix">
        <label><input type="radio" name="wx" value="1" />启用</label>
        <label><input type="radio" name="wx" value="0" />停用</label>
      </dd>
    </dl>
	<dl class="clearfix">
      <dt>排序：</dt>
      <dd><input class="input-mini" type="number" min="0" name="weight" id="weight" value="" /></dd>
    </dl>
  </form>
</script>

<script>
  var moduleList = {#$moduleList#}, action = '{#$action#}', adminPath = "{#$adminPath#}", token = '{#$token#}', defaultindex = '{#$cfg_defaultindex#}';
</script>
{#$jsFile#}
</body>
</html>
