<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>编辑模板</title>
{#$cssFile#}
<link rel='stylesheet' type='text/css' href='../../static/css/ui/codemirror.css' />
<style>
.left-nav {width:65px; z-index: 1;}
.left-nav li {text-align: center; height: 34px; line-height: 34px;}
.left-nav li.current {width: 63px; height: 35px; line-height: 35px;}
.left-nav li a {padding: 0 0 0 2px; -webkit-transition: none; -moz-transition: none; transition: none;}
.left-nav li.current a {padding: 0;}

/* 文件树 */
.file-tree {position: fixed; left: 90px; top: 15px; bottom: 15px; width: 235px; background-color: #e8eaee; z-index: 2;}
.file-tree dl {padding: 0; margin: 0;}
.file-tree dt {height: 36px; line-height: 36px; font-size: 12px; color: #fff; background: #2a2e36; font-weight: 500; padding: 0 15px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
.file-tree dd {position: absolute; left: 0; top: 36px; right: 0; bottom: 0; overflow-x: hidden; overflow-y: auto; margin: 0;}
.file-tree ul {padding: 0; margin: 0; list-style: none; display: none;}
.file-tree li {padding: 0; margin: 0; padding: 0 15px 0 13px; border-left: 2px solid transparent; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; cursor: pointer;}
.file-tree li:hover {background-color: #d7dbe4;}
.file-tree li.current {border-left: 2px solid #4777d7; background: #cacdd6;}


/* 底部按钮 */
.code-btns {display: none; position: absolute; left: 0; right: 0; bottom: 0; height: 50px; z-index: 3; background: #a91515;}
.code-btns a {float: right; height: 50px; width: 100%; display: inline-block; margin: 0; font-size: 16px; line-height: 50px; text-align: center; color: #fff; webkit-transition: background-color 0.2s; -moz-transition: background-color 0.2s; -ms-transition: background-color 0.2s; -o-transition: background-color 0.2s; transition: background-color 0.2s;}
.code-btns a:hover {background-color: #f00; text-decoration: none;}


/* 编辑器 */
.code-edit {position: fixed; left: 340px; top: 15px; bottom: 15px; right: 20px; z-index: 3;}
.code-edit .nofile {position: absolute; left: 0; top: 50%; right: 0; height: 70px; line-height: 70px; margin-top: -70px; text-align: center; font-size: 50px; color: #d1d1d1;}

.CodeMirror-wrap {position: absolute; left: 0; right: 0; top: 0; bottom: 50px; height: auto;}
.CodeMirror-fullscreen {position: fixed; top: 0; left: 0; right: 0; bottom: 0; height: auto; z-index: 9;}
.CodeMirror-foldmarker {color: #ff0; text-shadow: #b9f 1px 1px 2px, #b9f -1px -1px 2px, #b9f 1px -1px 2px, #b9f -1px 1px 2px; font-family: arial; line-height: .3; cursor: pointer;}
.CodeMirror-foldgutter {width: .7em;}
.CodeMirror-foldgutter-open, .CodeMirror-foldgutter-folded {cursor: pointer;}
.CodeMirror-foldgutter-open:after {content: "\25BE";}
.CodeMirror-foldgutter-folded:after {content: "\25B8";}
.cm-matchhighlight {background: rgba(255, 255, 255, 0.3);}
.CodeMirror-selection-highlight-scrollbar {background-color: green;}

/* 皮肤 */
.cm-s-ihuoniao.CodeMirror {background: #282c34; color: #abb2bf; line-height: 1.5; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;}
.cm-s-ihuoniao div.CodeMirror-selected {background: rgba(221,240,255,0.2);}
.cm-s-ihuoniao .CodeMirror-line::selection, .cm-s-ihuoniao .CodeMirror-line > span::selection, .cm-s-ihuoniao .CodeMirror-line > span > span::selection {background: rgba(221,240,255,0.2);}
.cm-s-ihuoniao .CodeMirror-line::-moz-selection, .cm-s-ihuoniao .CodeMirror-line > span::-moz-selection, .cm-s-ihuoniao .CodeMirror-line > span > span::-moz-selection {background: rgba(221,240,255,0.2);}
.cm-s-ihuoniao .CodeMirror-gutters {background: #25282c; color: #c5c8c6; border-right: 0px; padding: 0 3px 0 5px;}
.cm-s-ihuoniao .CodeMirror-guttermarker {color: white;}
.cm-s-ihuoniao .CodeMirror-guttermarker-subtle {color: #8F938F;}
.cm-s-ihuoniao .CodeMirror-linenumber {color: #8F938F;}
.cm-s-ihuoniao .CodeMirror-cursor {border-left: 1px solid #A7A7A7;}
.cm-s-ihuoniao span.cm-comment {color: #5c6370;}
.cm-s-ihuoniao span.cm-atom {color: #DE8E30;}
.cm-s-ihuoniao span.cm-number {color: #DE8E30;}
.cm-s-ihuoniao span.cm-property {color: #abb2bf;}
.cm-s-ihuoniao span.cm-attribute {color: #d18c4b;}
.cm-s-ihuoniao span.cm-keyword {color: #c678dd;}
.cm-s-ihuoniao span.cm-string {color: #8ac379;}
.cm-s-ihuoniao span.cm-variable {color: #AEB2F8;}
.cm-s-ihuoniao span.cm-variable-2 {color: #BEBF55;}
.cm-s-ihuoniao span.cm-variable-3 {color: #DE8E30;}
.cm-s-ihuoniao span.cm-def {color: #abb2bf;}
.cm-s-ihuoniao span.cm-tag {color: #fd5d57;}
.cm-s-ihuoniao span.cm-bracket {color: #abb2bf;}
.cm-s-ihuoniao span.cm-link {color: #ae81ff;}
.cm-s-ihuoniao span.cm-qualifier,.cm-s-ihuoniao span.cm-builtin {color: #d19a66;}
.cm-s-ihuoniao .CodeMirror-activeline-background {background: rgba(255, 255, 255, 0.031);}
.cm-s-ihuoniao .CodeMirror-matchingbracket {border: 1px solid rgba(255,255,255,0.25);	color: #8F938F !important; margin: -1px -1px 0 -1px;}

.CodeMirror-dialog {position: absolute; left: 0; right: 0; background: inherit; z-index: 15; padding: .1em .8em; overflow: hidden; color: inherit;}
.CodeMirror-dialog-top {border-bottom: 1px solid #eee; top: 0;}
.CodeMirror-dialog-bottom {border-top: 1px solid #eee; bottom: 0;}
.CodeMirror-dialog input {border: none; outline: none; background: transparent; width: 20em; color: inherit; font-family: monospace;}
.CodeMirror-dialog button {font-size: 70%;}

.CodeMirror-vscrollbar::-webkit-scrollbar-thumb {background-color: rgba(110,112,114,.9)!important;}
</style>
</head>

<body>
<!-- 文件类型 -->
<div class="left-nav">
  <ul>
    <li class="current" data-type="html"><a href="javascript:;">页面</a></li>
    <li data-type="css"><a href="javascript:;">样式</a></li>
    <li data-type="js"><a href="javascript:;">脚本</a></li>
    </li>
  </ul>
</div>

<!-- 文件树 -->
<div class="file-tree">
  <dl>
    <dt title="{#$action#}：{#$title#}（{#$template#}）">{#$action#}：{#$title#}（{#$template#}）</dt>
    <dd>
      <ul style="display: block;">
        {#foreach from=$htmlArray item=html#}
        <li data-title="{#$html#}" title="{#$html#}">{#$html#}</li>
        {#/foreach#}
      </ul>
      <ul>
        {#foreach from=$cssArray item=css#}
        <li data-title="{#$css#}" title="{#$css#}">{#$css#}</li>
        {#/foreach#}
      </ul>
      <ul>
        {#foreach from=$jsArray item=js#}
        <li data-title="{#$js#}" title="{#$js#}">{#$js#}</li>
        {#/foreach#}
      </ul>
    </dd>
  </dl>
</div>

<!-- 编辑器 -->
<div class="code-edit">
  <div class="nofile">选择左边的文件进行编辑！</div>
  <textarea class="hide" id="code" name="code"></textarea>
  <div class="code-btns"><a href="javascript:;" class="submit" title="保存修改">保存修改</a></div>
</div>


<script>
  var action = "{#$action#}", template = "{#$template#}", title = "{#$title#}", touch = "{#$touch#}", adminPath = "{#$adminPath#}", filetype = "html";
</script>
{#$jsFile#}
</body>
</html>
