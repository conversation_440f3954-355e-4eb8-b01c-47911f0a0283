<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>隐私保护通话绑定记录</title>
{#$cssFile#}
<style>
    .icon-question-sign {vertical-align: middle; margin: -3px 0 0 3px;}
    .tooltip-inner {max-width: 400px;}
</style>
</head>

<body>
<div class="search">
  <label>搜索：</label>
  <div class="btn-group" id="ctype" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">归属地<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部</a></li>
      {#foreach from=$cityNameArr item=city#}
      <li><a href="javascript:;" data-id="{#$city.cityCode#}">{#$city.cityName#} - <code>{#$city.cityCode#}</code></a></li>
      {#/foreach#}
    </ul>
  </div>
  <div class="btn-group" id="leimuBtn">
    <button class="btn dropdown-toggle" data-toggle="dropdown">模块信息<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="" data-nolist="true">所有模块</a></li>
      {#foreach $leimuallarr as $k => $v#}
      <li><a href="javascript:;" data-id="{#$k#}" data-nolist="true">{#$v#}</a></li>
      {#/foreach#}
    </ul>
  </div>
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的信息">
  &nbsp;&nbsp;绑定时间&nbsp;&nbsp;<input class="input-small" type="text" id="stime" placeholder="开始日期">&nbsp;&nbsp;到&nbsp;&nbsp;<input class="input-small" type="text" id="etime" placeholder="结束日期">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn hide" data-toggle="dropdown" id="batchAudit">解绑</button>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <div class="btn-group" id="stateBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">使用中(<span class="used"></span>)</a></li>
        <li><a href="javascript:;" data-id="2">已解绑(<span class="unbind"></span>)</a></li>
      </ul>
    </div>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row17 left">号码</li>
  <li class="row25 left">所属信息</li>
  <li class="row12 left">发起会员</li>
  <li class="row12 left">接听会员</li>
  <li class="row12 left">绑定/过期时间</li>
  <li class="row12 left">解绑时间</li>
  <li class="row7">操作</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="cityCode"></span>
  <span id="start"></span>
  <span id="end"></span>
</div>

<script>var adminPath = "{#$adminPath#}", type = "";</script>
{#$jsFile#}
</body>
</html>
