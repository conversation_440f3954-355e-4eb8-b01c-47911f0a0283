<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>付费查看电话基本设置</title>
{#$cssFile#}
<style media="screen">
	.editform dt {width: 200px;}
    .editform dd .input-prepend input[type=radio] {display: inline-block; vertical-align: middle; margin: -3px 2px 0 0; border-radius: 1em;}
    .editform dd .input-prepend input[type=checkbox] {display: inline-block; vertical-align: middle; margin: -2px 3px 0 0;}
</style>
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
<!-- <div class="alert alert-success" style="margin:10px 90px 10px 20px;"><button type="button" class="close" data-dismiss="alert">×</button>目前分类信息模块可以使用此功能，后续将会有更多模块接入，敬请期待~</div> -->
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>功能状态：</label></dt>
    <dd class="radio">
      {#html_radios name="payPhoneState" values=$payPhoneState checked=$payPhoneStateChecked output=$payPhoneStateNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>关闭后，页面中的电话号码将免费展现！</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="payPhonePrice">价格：</label></dt>
    <dd>
        <div class="input-append" style="display: inline-block; margin: 0;">
            <input class="input-mini" type="number" min="0" id="payPhonePrice" name="payPhonePrice" value="{#$payPhonePrice#}">
            <span class="add-on">元/次</span>
        </div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>会员免费查看：</label></dt>
    <dd class="radio">
      {#html_radios name="payPhoneVipFree" values=$payPhoneVipFree checked=$payPhoneVipFreeChecked output=$payPhoneVipFreeNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>开启后，VIP会员无需付费即可查看电话号码！</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>是否开启分销：</label></dt>
    <dd class="radio">
      {#html_radios name="payPhoneFenxiao" values=$payPhoneFenxiao checked=$payPhoneFenxiaoChecked output=$payPhoneFenxiaoNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>开启后，会员付费查看电话将参与分销流程！</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="payPhoneFenxiaoFee">分销佣金比例：</label></dt>
    <dd>
        <div class="input-append" style="display: inline-block; margin: 0;">
            <input class="input-mini" type="number" min="0" id="payPhoneFenxiaoFee" name="payPhoneFenxiaoFee" value="{#$payPhoneFenxiaoFee#}">
            <span class="add-on">%</span>
        </div>
        <span class="input-tips" style="display:inline-block;"><s></s>用于分发佣金的金额占支付金额的百分比</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>看广告解锁电话：</label></dt>
    <dd>
        
        <div class="input-prepend input-append">
            <span class="add-on">付费查看入口：</span>
            <span class="add-on" style="display: inline-block;"><label><input type="radio" name="payPhone_entrance" value="0"{#if !$payPhone_entrance#} checked{#/if#}>开启</label>&nbsp;</span>
            <span class="add-on" style="display: inline-block;"><label><input type="radio" name="payPhone_entrance" value="1"{#if $payPhone_entrance#} checked{#/if#}>关闭</label>&nbsp;</span>
        </div>
        <span class="input-tips" style="display:inline-block; margin-bottom: 10px;"><s></s>默认开启，页面提供两种方式供用户选择；<a href="{#$cfg_basehost#}/static/images/admin/payPhone_demo.png" target="_blank">查看展示效果</a><br />在支持看广告解锁电话的场景下，可以选择关闭付费查看电话的入口。</span>

        <div class="input-prepend input-append" style="display: block;">
            <span class="add-on">优量汇安卓App媒体ID：</span>
            <input class="input-small" type="text" id="tencentGDT_app_id" name="tencentGDT_app_id" value="{#$tencentGDT_app_id#}">
            <span class="add-on">广告位ID：</span>
            <input class="input-medium" type="text" id="tencentGDT_placement_id" name="tencentGDT_placement_id" value="{#$tencentGDT_placement_id#}">
            <span class="add-on">密钥：</span>
            <input class="input-xlarge" style="width: 300px;" type="text" id="tencentGDT_secret" name="tencentGDT_secret" value="{#$tencentGDT_secret#}">
        </div>
        <div class="input-prepend input-append" style="display: block;">
            <span class="add-on">微信小程序广告位ID：</span>
            <input class="input-large" type="text" id="payPhone_wxmini" name="payPhone_wxmini" value="{#$payPhone_wxmini#}">
        </div>

        <span class="input-tips" style="display:block;"><s></s>该功能只支持【微信小程序端】和【安卓APP端】，其他端继续使用付费解锁；<br />配置教程：<a href="https://help.kumanyun.com/help-413-1162.html" target="_blank">https://help.kumanyun.com/help-413-1162.html</a>，回调URL <code>{#$cfg_basehost#}/api/tencent_gdt_notify.php</code></span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>模块开关：</label></dt>
    <dd class="radio"> 
      <label style="width: auto; margin-right: 15px;"><input type="checkbox" name="payPhoneModule[]" value="business"{#if 'business'|in_array:$payPhoneModule#} checked{#/if#} />商家</label>       
      {#if in_array("info", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="payPhoneModule[]" value="info"{#if 'info'|in_array:$payPhoneModule#} checked{#/if#} />{#getModuleTitle name='info'#}</label>{#/if#}
      {#if in_array("sfcar", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="payPhoneModule[]" value="sfcar"{#if 'sfcar'|in_array:$payPhoneModule#} checked{#/if#} />{#getModuleTitle name='sfcar'#}</label>{#/if#}
      {#if in_array("job", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="payPhoneModule[]" value="job"{#if 'job'|in_array:$payPhoneModule#} checked{#/if#} />{#getModuleTitle name='job'#}</label>{#/if#}
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
