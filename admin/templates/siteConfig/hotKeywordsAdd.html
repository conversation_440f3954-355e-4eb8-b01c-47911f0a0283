<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}", cityid = {#$cityid#}, cityList = {#$cityList#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="module">模块：</label></dt>
    <dd style="overflow: visible; padding-left: 140px;">
      <select class="chosen-select" id="module" name="module" style="width: auto; min-width: 150px;">
        <option value="">请选择</option>
        {#foreach from=$moduleList item=mod#}
        <option value="{#$mod.name#}"{#if $module == $mod.name#} selected{#/if#}>{#$mod.title#}</option>
        {#/foreach#}
      </select>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="module">城市：</label></dt>
    <dd style="overflow: visible; padding-left: 140px;">
      <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value="{#$cityid#}"></div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="keyword">关键字：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="keyword" id="keyword" data-regex=".{1,30}" maxlength="30" value="{#$keyword#}" />
      <div class="color_pick"><em style="background:{#$color#};"></em></div>
      <span class="input-tips"><s></s>请输入关键字，1-30个汉字</span>
      <input type="hidden" name="color" id="color" value="{#$color#}" />
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="href">链接地址：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="href" id="href" value="{#$href#}" data-regex=".*" />
      <span class="input-tips" style="display:inline-block;"><s></s>请输入链接地址，留空表示站内链接</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="weight">排序：</label></dt>
    <dd>
      <input class="input-mini" type="number" name="weight" id="weight" min="0" data-regex="[1-9]\d*" value="{#$weight#}" />
      <span class="input-tips"><s></s>必填，排序越大，越排在前面</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>打开方式：</label></dt>
    <dd class="radio">
      {#html_radios name="target" values=$targetopt checked=$target output=$targetnames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>字体：</label></dt>
    <dd class="radio">
      {#html_radios name="blod" values=$blodopt checked=$blod output=$blodnames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>显示属性：</label></dt>
    <dd class="radio">
      {#html_radios name="state" values=$stateopt checked=$state output=$statenames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
