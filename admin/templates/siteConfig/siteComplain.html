<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>网站举报管理</title>
{#$cssFile#}
<style media="screen">
  .list td {font-size: 14px;}
</style>
</head>

<body>
<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <select class="chosen-select" id="typeList" style="width: auto;">
    <option value="">选择模块</option>
    <option value="user">会员用户</option>
    {#foreach from=$moduleList item=module#}
    <option value="{#$module.name#}">{#$module.title#}</option>
    {#/foreach#}
  </select>
  <div class="choseCity"><input type="hidden" id="cityList" name="cityList" placeholder="请选择城市分站" value=""></div>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" id="delBtn">删除</button>
    <div class="btn-group" id="stateBtn" {#if $notice#} data-id="0"{#/if#}>
    {#if $notice#}
    <button class="btn dropdown-toggle" data-toggle="dropdown">待处理(<span class="totalGray"></span>)<span class="caret"></span></button>
    {#else#}
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
    {#/if#}
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="0">待处理(<span class="totalGray"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">已处理(<span class="totalAudit"></span>)</a></li>
      </ul>
    </div>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row10 left">城市</li>
  <li class="row30 left">举报信息</li>
  <li class="row15 left">联系方式</li>
  <li class="row12">状态</li>
  <li class="row20 left">处理结果</li>
  <li class="row10">操 作</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
</div>

<script id="quickEdit" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
    <dl class="clearfix">
      <dt></dt>
      <dd><textarea id="note" name="note" style="width:94%; height:100px;" /></textarea></dd>
    </dl>
  </form>
</script>

<script>var action = '{#$action#}', adminPath = "{#$adminPath#}", cityList = {#$cityList#};</script>
{#$jsFile#}
</body>
</html>
