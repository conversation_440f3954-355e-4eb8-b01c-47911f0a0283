<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>内存优化配置</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
<div class="alert alert-success" style="margin:10px;"><button type="button" class="close" data-dismiss="alert">×</button>redis缓存配置教程：<a href="https://help.kumanyun.com/help-196-664.html" target="_blank">https://help.kumanyun.com/help-196-664.html</a></div>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix"><dt><strong>redis缓存：</strong></dt><dd></dd></dl>
  <dl class="clearfix">
    <dt><label for="redisState">启用状态：</label></dt>
    <dd class="radio">
      {#html_radios name="redis[state]" values=$redisState checked=$redisStateChecked output=$redisStateNames separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="name">redis服务器地址：</label></dt>
    <dd>
      <input class="input-large" type="text" name="redis[server]" id="server" data-regex=".{2,30}" maxlength="30" value="{#$cfg_memory['redis']['server']#}" placeholder="127.0.0.1" />
      <span class="input-tips"><s></s>请输入redis服务器地址。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="port">redis服务器端口：</label></dt>
    <dd>
      <input class="input-large" type="text" name="redis[port]" id="port" data-regex=".{2,6}" maxlength="10" value="{#$cfg_memory['redis']['port']#}" placeholder="6379" />
      <span class="input-tips"><s></s>请输入redis服务器端口。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="requirepass">requirepass：</label></dt>
    <dd>
      <input class="input-large" type="text" name="redis[requirepass]" id="requirepass" data-regex=".{5,30}" maxlength="30" value="{#$cfg_memory['redis']['requirepass']#}" />
      <span class="input-tips"><s></s>请输入requirepass。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="prefix">内存变量前缀：</label></dt>
    <dd>
      <input class="input-large" type="text" name="prefix" id="prefix" data-regex="" maxlength="30" value="{#$cfg_memory['prefix']#}" placeholder="huoniao_" />
      <span class="input-tips"><s></s>请输入redis服务器内存变量前缀：</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="db">使用数据库：</label></dt>
    <dd class="radio">
      <label for="db">
        <select name="redis[db]" id="db" class="input-medium">
          {#html_options options=$dbList selected=$db#}
        </select>
      </label>
      <span class="input-tips" style="display:inline-block;"><s></s>同一台服务器部署多个火鸟系统时请设置不同的变量前缀或数据库</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>状态：</dt>
    <dd class="singel-line">
      <a href="javascript:;" id="checkRedis">点击检测是否可用</a>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>