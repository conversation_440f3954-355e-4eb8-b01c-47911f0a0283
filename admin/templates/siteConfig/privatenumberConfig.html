<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>隐私通话保护基本设置</title>
{#$cssFile#}
<style media="screen">
	.editform dt {width: 200px;}
</style>
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
<div class="alert alert-success" style="margin:10px 90px 10px 20px;"><button type="button" class="close" data-dismiss="alert">×</button>配置教程：<a href="https://help.kumanyun.com/help-214-780.html" target="_blank">https://help.kumanyun.com/help-214-780.html</a></div>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label>功能状态：</label></dt>
    <dd class="radio">
      {#html_radios name="privatenumberState" values=$privatenumberState checked=$privatenumberStateChecked output=$privatenumberStateNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>关闭后，页面中将直接使用真实号码！</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="app_key">APP_KEY：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="app_key" id="app_key" value="{#$app_key#}" />
      <span class="input-tips" style="display: inline-block;"><s></s>应用APP_KEY</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="app_secret">APP_Secret：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="app_secret" id="app_secret" value="{#$app_secret#}" />
      <span class="input-tips" style="display: inline-block;"><s></s>应用APP_Secret</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="sound">拨通等待音：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="sound" id="sound" value="{#$sound#}" />
      <span class="input-tips" style="display: inline-block;"><s></s>设置个性化通话前等待音，即主叫听到的回铃音。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="duration">绑定有效时间：</label></dt>
    <dd>
        <div class="input-append" style="display: inline-block; margin: 0;">
            <input class="input-mini" type="number" min="0" id="duration" name="duration" value="{#$duration#}">
            <span class="add-on">秒</span>
        </div>
      <span class="input-tips" style="display: inline-block;"><s></s>两个号码的绑定关系保持时间，0表示永不过期，最大7776000秒（90天），到达指定时间后，绑定关系将自动解除。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="maxDuration">通话最长时间：</label></dt>
    <dd>
        <div class="input-append" style="display: inline-block; margin: 0;">
            <input class="input-mini" type="number" min="0" id="maxDuration" name="maxDuration" value="{#$maxDuration#}">
            <span class="add-on">分钟</span>
        </div>
      <span class="input-tips" style="display: inline-block;"><s></s>单次通话进行的最长时间，0表示不限制，最大1440分钟（24小时），通话到达指定时间后，电话将自动挂断。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>归属地状态：</label></dt>
    <dd class="radio">
      {#html_radios name="areaCodeState" values=$areaCodeState checked=$areaCodeStateChecked output=$areaCodeStateNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>开启后，虚拟号码将自动使用信息所在地的号码，开启前请先订购好所有分站的号码，并在<a href="siteConfig/siteCity.php" id="siteCity">分站管理</a>的高级设置中填写区号信息。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>负载规则：</label></dt>
    <dd class="radio">
      {#html_radios name="burdenState" values=$burdenState checked=$burdenStateChecked output=$burdenStateNames separator="&nbsp;&nbsp;"#}
      <span class="input-tips" style="display:inline-block;"><s></s>用于订购的号码全都处于使用状态中的情况，建议选择【显示真实号码】，如果选择【解绑之前的号码】会导致会员获取到的号码无法拨通的问题。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>频率限制：</label></dt>
    <dd>
        <div class="input-prepend input-append" style="display: inline-block; margin: 0;">
            <input class="input-mini" type="number" min="0" id="limit_time" name="limit_time" value="{#$limit_time#}">
            <span class="add-on">分钟内最多绑定</span>
            <input class="input-mini" type="number" min="0" id="limit_count" name="limit_count" value="{#$limit_count#}">
            <span class="add-on">次</span>
        </div>
      <span class="input-tips" style="display: inline-block;"><s></s>限制会员发起绑定功能，防止系统号码被频繁使用。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label>模块开关：</label></dt>
    <dd class="radio"> 
      <label style="width: auto; margin-right: 15px;"><input type="checkbox" name="module[]" value="business"{#if 'business'|in_array:$module#} checked{#/if#} />商家</label>       
      {#if in_array("info", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="module[]" value="info"{#if 'info'|in_array:$module#} checked{#/if#} />{#getModuleTitle name='info'#}</label>{#/if#}
      {#if in_array("sfcar", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="module[]" value="sfcar"{#if 'sfcar'|in_array:$module#} checked{#/if#} />{#getModuleTitle name='sfcar'#}</label>{#/if#}
      {#if in_array("job", $installModuleArr)#}<label style="width: auto; margin-right: 15px;"><input type="checkbox" name="module[]" value="job"{#if 'job'|in_array:$module#} checked{#/if#} />{#getModuleTitle name='job'#}</label>{#/if#}
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
