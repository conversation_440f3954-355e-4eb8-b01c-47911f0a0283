<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>配置第三方登录接口</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
{#if $code == 'wechat'#}
<div class="alert alert-success" style="margin:10px;"><button type="button" class="close" data-dismiss="alert">×</button>注意：打通微信登录后请不要随意变更网站应用的AppId，否则会导致已经使用微信登录过的账号失效！</div>
{#/if#}

<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="action" id="action" value="{#$action#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="code" id="code" value="{#$code#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="name">接口名称：</label></dt>
    <dd>
      <input class="input-large" type="text" name="name" id="name" data-regex=".{2,30}" maxlength="30" value="{#$name#}" />
      <span class="input-tips"><s></s>请输入接口名称，2-30个字符。</span>
    </dd>
  </dl>
  <div id="loginConfig">
    {#$config#}
  </div>
  <dl class="clearfix">
    <dt><label for="desc">接口说明：</label></dt>
    <dd>
      <textarea class="input-xxlarge" rows="5" name="desc" id="desc" data-regex=".*">{#$desc#}</textarea>
      <span class="input-tips"><s></s>请输入接口说明。</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>状态：</dt>
    <dd class="radio">
      {#html_radios name="state" values=$stateList checked=$state output=$stateName separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>