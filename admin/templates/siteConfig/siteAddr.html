<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>网站地区</title>
{#$cssFile#}
<style>
.list .tr .markditu {margin-left: 50px;}
.tooltip.bottom {white-space: normal;}
.thead li {text-indent: 0;}
</style>
</head>

<body>
<!-- <div class="search" style="position:relative;">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <button type="button" class="btn btn-success" id="searchBtn">搜索</button>
  <button type="button" class="btn btn-danger" id="batch" style="margin-left: 50px;">批量删除</button>
  <div class="tool">
    <a href="javascript:;" class="add-type" style="display:inline-block;" id="addNew_">添加新区域</a>&nbsp;|&nbsp;<a href="javascript:;" id="unfold">全部展开</a>&nbsp;|&nbsp;<a href="javascript:;" id="away">全部收起</a>
  </div>
</div> -->

<div class="search" style="padding: 15px 10px;">
  <label>选择区域：</label>
  <div class="btn-group" id="pBtn" data-id="{#$pid#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$pname#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">--{#$areaName[0]#}--</a></li>
      {#if $province#}
      {#foreach from=$province item=p#}
      <li><a href="javascript:;" data-id="{#$p.id#}">{#$p.typename#}</a></li>
      {#/foreach#}
      {#/if#}
    </ul>
  </div>
  <div class="btn-group" id="cBtn" data-id="{#$cid#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$cname#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">--{#$areaName[1]#}--</a></li>
      {#if $city#}
      {#foreach from=$city item=c#}
      <li><a href="javascript:;" data-id="{#$c.id#}">{#$c.typename#}</a></li>
      {#/foreach#}
      {#/if#}
    </ul>
  </div>
  <div class="btn-group" id="dBtn" data-id="{#$did#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$dname#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">--{#$areaName[2]#} --</a></li>
      {#if $district#}
      {#foreach from=$district item=d#}
      <li><a href="javascript:;" data-id="{#$d.id#}">{#$d.typename#}</a></li>
      {#/foreach#}
      {#/if#}
    </ul>
  </div>
  <div class="btn-group" id="tBtn" data-id="{#$tid#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$tname#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">--{#$areaName[3]#} --</a></li>
      {#if $town#}
      {#foreach from=$town item=d#}
      <li><a href="javascript:;" data-id="{#$d.id#}">{#$d.typename#}</a></li>
      {#/foreach#}
      {#/if#}
    </ul>
  </div>
  <div class="btn-group" id="vBtn" data-id="{#$vid#}">
    <button class="btn dropdown-toggle" data-toggle="dropdown">{#$vname#}<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">--{#$areaName[4]#} --</a></li>
      {#if $village#}
      {#foreach from=$village item=d#}
      <li><a href="javascript:;" data-id="{#$d.id#}">{#$d.typename#}</a></li>
      {#/foreach#}
      {#/if#}
    </ul>
  </div>
  <button type="button" class="btn btn-primary" id="customAreaNameBtn">自定义级别名称</button>
  <button type="button" class="btn btn-danger" id="batch" style="margin-left: 80px;">批量删除</button>
</div>

<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:0 10px;">
  <li class="row2">&nbsp;</li>
  <li class="row80 left">名称<small style="margin-left: 152px;" class="lnglatTips" data-toggle="tooltip" data-placement="bottom" title="用于按字母显示/排序">拼音 <i class="icon-question-sign"></i></small></small><small style="margin-left: 80px;"><a href="https://docs.qq.com/sheet/DTXZiYUFrZEdLb2xR" target="_blank">获取城市天气ID</a></small><small class="lnglatTips" style="margin-left: 60px;" data-toggle="tooltip" data-placement="bottom" title="此数据均由系统自动采集获取而来，数据准确度请根据实际使用情况做出调整！">区域经纬度 <i class="icon-question-sign"></i></small></li>
  <li class="row11">排序</li>
  <li class="row7 left">操 作</li>
</ul>

<form class="list mb50" id="list">
  <ul class="root"></ul>
  <div class="tr clearfix">
    <div class="row2"></div>
    <div class="row90 left"><a href="javascript:;" class="add-type" style="display:inline-block;" id="addNew">添加新区域</a></div>
  </div>
</form>
<div class="fix-btn"><button type="button" class="btn btn-success" id="saveBtn">保存</button></div>

<script id="customAreaNameObj" type="text/html">
    <form action="" class="quick-editForm clearfix" name="editForm" style="padding: 50px 0 50px 50px;">
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">一级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area0" type="text" class="input-small" placeholder="默认：省份" value="{#$areaName[0]#}" />          
        </dd>
      </dl>
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">二级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area1" type="text" class="input-small" placeholder="默认：城市" value="{#$areaName[1]#}" />          
        </dd>
      </dl>      
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">三级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area2" type="text" class="input-small" placeholder="默认：区县" value="{#$areaName[2]#}" />          
        </dd>
      </dl>        
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">四级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area3" type="text" class="input-small" placeholder="默认：乡镇" value="{#$areaName[3]#}" />          
        </dd>
      </dl>          
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">五级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area4" type="text" class="input-small" placeholder="默认：村庄" value="{#$areaName[4]#}" />          
        </dd>
      </dl>        
      <dl class="clearfix" style="float: left; width:130px;">
        <dt style="float: none; text-align: left; display: block;">六级：</dt>
        <dd style="margin-left: 0; padding-left: 0;">
            <input id="area5" type="text" class="input-small" placeholder="自定义" value="{#$areaName[5]#}" />          
        </dd>
      </dl>
    </form>
</script>

<script>
  var typeListArr = {#$typeListArr#}, adminPath = "{#$adminPath#}", token = '{#$token#}';
  var areaName = {#$areaName|json_encode#};
</script>
{#$jsFile#}
<script type="text/javascript">
$(function(){
  $('.lnglatTips').tooltip();
})
</script>
</body>
</html>
