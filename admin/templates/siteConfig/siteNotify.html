<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>消息通知配置</title>
{#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 90px 0 10px"><button type="button" class="close" data-dismiss="alert">×</button>短信通知模板配置教程：<a href="https://help.kumanyun.com/help-48-6.html" target="_blank">https://help.kumanyun.com/help-48-6.html</a><br />公众号模板消息配置教程：<a href="https://help.kumanyun.com/help-48-7.html" target="_blank">https://help.kumanyun.com/help-48-7.html</a></div>

<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <a href="siteNotifyAdd.php" class="btn btn-primary" id="addNew">新增消息通知</a>
    <a href="javascript:;" class="btn" id="importDefaultData" style="margin-left: 50px;">恢复系统默认通知</a>
    {#if $cfg_smsAlidayu == 1#}
    <a href="../inc/json.php?action=addSmsTemplate" target="_blank" class="btn" id="import" style="margin-left: 50px;">一键导入阿里云短信模板</a>
    {#/if#}
    {#*
    <div class="btn-group" id="pendBtn" style="margin-left: 50px;">
      <button class="btn dropdown-toggle" data-toggle="dropdown">一键导入微信公众号模板<span class="caret"></span></button>
      <ul class="dropdown-menu" style="max-height: 601px; overflow-y: auto;">
        <li><a href="javascript:;" data-id="0" id="setindustry">设置所属行业</a></li>
        <li><a href="../inc/json.php?action=addWxTemplate&addtype=1" id="importWxtemp" data-id="1" target="_blank" >导入模板消息</a></li>
      </ul>
    </div>
    *#}
  </div>
  <div class="f-right">
    <span class="help-inline" id="totalCount" style="margin-right:10px;"></span>
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row20 left">&nbsp;&nbsp;&nbsp;&nbsp;消息名称</li>
  <li class="row50">通知方式</li>
  <li class="row20">状态</li>
  <li class="row10 left">操 作</li>
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
</div>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
