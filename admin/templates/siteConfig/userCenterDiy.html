<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}"/>
<title>个人中心自定义</title>
<link rel="stylesheet" href="/static/css/core/base.css">
<link rel="stylesheet" href="/static/css/ui/element_ui_index.css">
<link rel="stylesheet" href="/static/css/ui/pickr.min.css">
<link rel="stylesheet" href="/static/css/admin/siteMemberPage.css?v={#$cfg_staticVersion#}">

<script language="javascript" type="text/javascript">
    var thumbSize = '{#$thumbSize#}', thumbType = "{#$thumbType#}", adminPath = "{#$adminPath#}", modelType = action = "{#$action#}", cfg_staticVersion = '{#$cfg_staticVersion#}';

    var masterDomain = '{#$cfg_basehost#}';
    var memberDomain = '{#$userDomain#}';
    var businessUrl = '{#$busiDomain#}';
    var branchUrl = '{#$userDomain#}/workPlatform.html'
    window.onbeforeunload = function(){
        if(optAction){
        return ("还没有完成,确认退出吗?");
        }
    }
    var cfg_attachment = '{#$cfg_attachment#}';
    var defaultPath = '{#$cfg_basehost#}/static/images/admin/siteMemberPage/defaultImg/';
    var cfg_pointName = '{#$cfg_pointName#}'
    var cfg_bonusName = '{#$cfg_bonusName#}'
    var hotLine = '{#$cfg_hotline#}'
    var sitePageData = '{#json_encode($sitePageData)#}'; //数据
    var platform = '{#$smarty.get.platform#}'  //当前终端
    var changePage = '{#$smarty.get.change#}'; //是从设置页面切换
    var config = '{#$config#}';  // 
    var cfg_business_state = {#$cfg_business_state|default:1#};
</script>
</head>

<body>
    <canvas id="canvas" style="display:none;"></canvas>
    <div class="page" id="page" v-cloak>
        <!-- <div class="alertBox" v-show="err.show">
            <div class="alertPop" :style="'left:'+ err.left +'; top:'+err.top+';right:'+err.right+';'">
                <div class="text_con">
                    <h4>{{err.title}}</h4>
                    <p>不可恢复，请谨慎操作</p>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="err.show = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure"><s></s><span>确定</span></a>
                </div>
            </div>
        </div> -->
        <!-- <div class="popMask"></div>
        <div class="popbox">
            <h2>重置发布页所有设置？</h2>
            <p>不可恢复，请谨慎操作</p>
            <a href="javascript:;" class="close_pop"></a>
            <div class="btn_group">
                <a href="javascript:;" class="cancel"><s></s><span>取消</span></a><a href="javascript:;"
                    class="sure"><s></s><span>确定</span></a>
            </div>
        </div> -->
        <div class="headBox">
            <div class="h3">
                <a href="javascript:;" target="_blank" class="backHome"></a> <s class="line1"></s><span>个人中心自定义设置</span>
                <div class="setConBox">
                    <label for="">正在编辑：</label>
                    <template v-if="sitePageData.default == '1'">

                        <div :class="['platformSetBox setBox',{'disabled':noSetPlatform}]" @click="showChangePop">
                            <div class="platform_curr" v-if="currPlatform && currPlatform.id">
                                <s><img :src="'/static/images/admin/siteConfigPage/'+currPlatform.id+'.png'" alt=""></s>
                                <span>{{currPlatform.name}}</span>
                            </div>
                            <div class="platform_curr" v-else>
                                <template  v-for="item in terminalList" >
                                    <s v-if="item.buy"><img :src="'/static/images/admin/siteConfigPage/'+item.id+'.png'" alt=""></s>
                                </template>
                            </div>
                            <i class="el-icon el-icon-arrow-down "></i>
                        </div>
                    </template>
                    <div class="platformSetBox setBox disabled" @click="showMessageTip" v-else>
                        <div class="platform_curr" v-if="terminalList.length > 0">
                            <template v-for="item in terminalList">
                                <s v-if="item.buy"><img :src="'/static/images/admin/siteConfigPage/'+item.id+'.png'" alt=""></s>
                            </template>
                        </div>
                        <div class="platform_curr" v-else>
                            <template v-for="item in terminalList" >
                                <template v-if="item.buy">
                                    <s><img :src="'/static/images/admin/siteConfigPage/'+item.id+'.png'" alt=""></s>
                                    <span>{{item.name}}</span>
                                </template>
                            </template>
                        </div>
                        <i class="el-icon el-icon-arrow-down "></i>
                    </div>
                </div>
            </div>
            <div class="btnsGroup">
                <a href="javascript:;" class="preview" @click="saveAllData(1)"><s></s>预览</a>
                <el-popover
                    width="320"
                    placement="bottom-end"
                    popper-class="blackConfirm"	
                    v-model="showFirstTooltip"
                    trigger="manual">
                    <h6>可批量设置同步此模板到其他终端</h6>
                    <div class="btns_box">
                        <span @click="showFirstTooltip = false">好的</span>
                    </div>
                    <el-button  slot="reference" class="advanceSetBtn" v-if="sitePageData.default == 1 && currPlatform && currPlatform.id " @click="advanceSetPopShow">{{advancsFormData.hasSet ? '已开启批量同步' : '发布高级选项'}}</el-button>
                </el-popover>
                <a href="javascript:;" class="save" @click="saveAllData()">保存</a>
                <div class="success_tip"><s></s>
                    <p>保存成功</p>
                </div>
                <div class="error_tip"><s></s>
                    <p>请补全未完善信息！</p>
                </div>
                
                <!-- <div class="previewBox" v-show="preveiw">
                    <div class="qrBox"><img :src=" '{#$cfg_basehost#}/include/qrcode.php?data='+ encodeURIComponent(preveiwSet ?  preveiwSet : '加载中')" alt=""></div>
                    <h5>扫描二维码预览</h5>
                    <p>已同步至最新</p>
                </div> -->
                <div class="mask_preview" v-show="preveiw " @click="preveiw = false"></div>
                <div :class="['previewBox',{'morePlatform':!currPlatform || !currPlatform.id}]" v-show="preveiw">
                    <div class="adQrBox" v-if="checkTerminal('app') && (currPlatform.id == 'app' || !currPlatform || !currPlatform.id) ">
                        <div class="qrBox"><img :src=" '{#$cfg_basehost#}/include/qrcode.php?data='+ encodeURIComponent('{#$userDomain#}?preview=1&appIndex=1&platform=app')" alt=""></div>
                        <h5>APP端</h5>
                        <p><a href="{#$masterDomain#}/mobile.html" target="_blank">{#$cfg_shortname#}APP端</a>扫码</p>
                    </div>
                    <div class="miniQrBox" v-if="checkTerminal('wxmini') && (currPlatform.id == 'wxmini' || !currPlatform || !currPlatform.id) ">
                        <div class="qrBox"><img :src=" '{#$cfg_basehost#}/include/qrcode.php?data='+ encodeURIComponent( '/pages/member/index/index?preview=1&appIndex=1&platform=wxmini')" alt=""></div>
                        <h5>微信小程序</h5>
                        <p>使用微信扫码</p>
                    </div>
                    <div class="h5QrBox" v-if="currPlatform.id == 'h5' || !currPlatform  || !currPlatform.id ">
                        <div class="qrBox"><img :src=" '{#$cfg_basehost#}/include/qrcode.php?data='+ encodeURIComponent('{#$userDomain#}?preview=1&appIndex=1&platform=h5')" alt=""></div>
                        <h5>h5端</h5>
                        <p>使用微信扫码</p>
                    </div>
                </div>
            </div>
        </div>
    
    
        <div class="containBox">
            <!-- 左侧数据 -->
            <div class="leftCon">
                <!-- 是否采用自定义 -->
                <dl class="modbox  fn-clear bujutype" data-type="bjtype">
                    <dt>更换模板</dt>
                    <dd data-val="1" :class="[{'on_chose':selfDefine == 1}]" @click="modelChosePop = true">选择模板</dd>
                </dl>
    
    
                <!-- 会员中心组件 -->
                <dl class="modbox  fn-clear " data-type="">
                    <div class="noClickMask" v-show="selfDefine == 0" @click.stop="openError"></div>
                    <dt>会员中心组件</dt>
                    <dd :class="['icon_item']" v-for="(item,ind) in moduleOptions.slice(0,6)" @click="addModule(item)">
                        <div class="icon"><img :src="defaultPath + 'mod_0' + (item.id) + '.png'" alt=""></div>
                        <p>{{item.text}}</p>
                    </dd>
                   
                </dl>
    
    
                <!-- 基础组件 -->
                <dl class="modbox  fn-clear " data-type="">
                    <div class="noClickMask" v-show="selfDefine == 0"  @click.stop="openError"></div>
                    <dt>基础组件</dt>
                    <dd :class="['icon_item']" v-for="(item,ind) in moduleOptions.slice(6,8)" @click="addModule(item)">
                        <div class="icon"><img :src="defaultPath + 'mod_0' + (item.id) + '.png'" alt=""></div>
                        <p>{{item.text}}</p>
                    </dd>
                </dl>
    
            </div>
    
    
    
    
            <!-- 中间内容 -->
            <div class="midContainer" @click="showPageSet" v-if="hasSetModel">
                <a href="javascript:;" class="hidebtn" @click="showLine = !showLine">
                    <span class="hideLine"  >{{showLine ? '隐藏辅助线' : '显示辅助线'}}</span>
                </a>
                <a href="javascript:;" class="hidebtn hideTitle" v-show="!currEditPart" @click="showTitle = !showTitle" >
                    <span class="hideLine" >{{showTitle ? '隐藏标题栏' : '显示标题栏'}}</span>
                </a>
                <div class="noClickMask" v-show="selfDefine == 0"  @click.stop="openError"></div>
                <div class="optBox">
                    <el-tooltip class="item" effect="dark" content="复制组件" placement="left" popper-class="fastbox" >
                        <div :class="['copyBtn',,{'disabled':[0,1,2,3,6].includes(currEditPart)}]" @click="copyPart"></div>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="页面设置" placement="left" popper-class="fastbox">
                        <div :class="['setAllBtn']"  @click="currEditPart = 0;showTitle=true" ></div>
                    </el-tooltip>
                </div>
                <div class="midConBox">
                    <div class="pageShow" >
                        <div class="pagebox">
                            
                            <div class="line_phone" v-show="showLine">
                                <span>首屏高度</span>
                            </div>
                            <div :class="['pageCon customCon',{'webPageCon':['dymini','h5'].includes(currPlatform.id)},{'wxPageCon':['wxmini'].includes(currPlatform.id)}] ">
                                <div id="toCanvas">
                                    <!-- 模板展示的 -->
                                    <div class="truePage" v-show="!selfDefine">
                                        <div class="fixedTop" id="pageTop" :style="'background:'+ defaultModel.pageSet.style.background +';'" v-show="!currEditPart||fadeOut">
                                            <div :class="['pagetop',{'lightStyle':defaultModel.memberComp.themeType == 'light'}]" ><img src="{#$cfg_basehost#}/static/images/admin/phone_head.png" alt=""></div>
                                            <div class="top_header" :style="getStyle('pageTop','',defaultModel.pageSet)">
                                                <div :class="['flex_l',{'flexCenter':defaultModel.pageSet.layout === 1}]">
                                                    <div class="infoBox" v-show="!defaultModel.pageSet.showType">
                                                        <div class="photo"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                        <h4 :style="'color:'+ defaultModel.pageSet.style.color +';'">星辰大海</h4>
                                                    </div>
                                                    <div class="infoBox" v-show="defaultModel.pageSet.showType">
                                                        <h4 :style="'color:'+ defaultModel.pageSet.style.color +';'">{{defaultModel.pageSet.title ? defaultModel.pageSet.title : '个人中心'}}</h4>
                                                    </div>
                                                </div>
                                                <div class="flex_r">
                                                    <ul class="btns"  v-show="defaultModel.pageSet.btns.showType !== 1" v-if="defaultModel.pageSet.btns.showType == 2"> 
                                                        <li v-for="btn in defaultModel.pageSet.btns.list">
                                                            <div class="icon"><img :style="getStyle('pageBtn','',defaultModel.pageSet)" :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                        </li>
                                                    </ul>
                                                    <ul class="btns"  v-show="defaultModel.pageSet.btns.showType !== 1" v-else> 
                                                        <li v-for="btn in memberCompFormData.setBtns.btns">
                                                            <div class="icon"><img  :src="btn.icon ? btn.icon : defaultIcon2 " ></div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>                                   
                                        </div>
                                        <div :class="['pagetop',{'lightStyle':defaultModel.memberComp.themeType == 'light'}]" ><img src="{#$cfg_basehost#}/static/images/admin/phone_head.png" alt=""></div>
                                        <!-- 会员信息组件 -->
                                        <div :class="['memberInfoBox ',{'lightStyle':defaultModel.memberComp.themeType == 'light'}]" data-part="memberHeader">
                                            <!-- 样式1 -->
                                            <div :class="['memberInfo1',{'currEditPart' : currEditPart === 1}] " @click="currEditPart = 1">
                                                <div class="memberBg" :style="getStyle('memberInfoBox','',defaultModel.memberComp)" ></div>
                                                <!-- 头部 -->
                                                <div class="flex_header" v-if="!memberCompFormData.hasOwnProperty('showHeader') || memberCompFormData.showHeader || currPlatform.id == 'wxmini'  ">
                                                    <div class="flexBox headerTop" v-if="cfg_business_state || defaultModel.memberComp.setBtns.btns.length " :style="'margin:0 ' + (defaultModel.memberComp.cardStyle.marginLeft / 2) +'px'">
                                                        <!-- 左上 -->
                                                        <div class="leftTop" v-if="cfg_business_state">
                                                            <el-carousel ref="carousel" height="30px" direction="vertical" :autoplay="false" indicator-position="none">
                                                                <el-carousel-item :class="['bitem',{'canWhite':defaultModel.memberComp.business.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="business">
                                                                    <s><img :src="defaultModel.memberComp.business.icon" alt=""></s>
                                                                    <span>{{defaultModel.memberComp.business.text}}</span>
                                                                </el-carousel-item>
                                                                <el-carousel-item :class="['bitem',{'canWhite':defaultModel.memberComp.branch.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="branch">
                                                                    <s><img :src="defaultModel.memberComp.branch.icon" alt=""></s>
                                                                    <span>{{defaultModel.memberComp.branch.text}}</span>
                                                                </el-carousel-item>
                                                            </el-carousel>
                                                        </div>
                                                        <div class="leftTop" v-else></div>
            
                                                        <!-- 右上 -->
                                                        <div class="rightTop">
                                                            <ul class="btns" > 
                                                                <li v-for="btn in defaultModel.memberComp.setBtns.btns" :class="{'floatItem fn-clear':defaultModel.memberComp.setBtns.btnType === 2}">
                                                                    <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                    <p v-if="btn.text">{{btn.text}}</p>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="wx_btn" v-if="currPlatform.id == 'wxmini'"><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
                                                </div>
                                                <div :class="['memberInfoCon']" :style="'margin:0 ' + (defaultModel.memberComp.cardStyle.marginLeft / 2) +'px'">
                                                    <div class="mleftCon fn-clear">
                                                        <div class="photoCon"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                        <div class="textCon">
                                                            <h4>星辰大海</h4>
                                                            <div class="vipIcon">
                                                                <s class="vip_icon"></s>
                                                                <span>普通会员</span>
                                                            </div>
                                                        </div>
                                                        <s class="arr"  v-show="!memberCompFormData.qiandaoBtn.show"><em :style="'filter: drop-shadow(15px 0 0 '+ memberCompFormData.qiandaoBtn.style.arr_color +');'"></em></s>
                                                    </div>
                                                    <div class="mrightCon" v-show="memberCompFormData.qiandaoBtn.show"  >
                                                        <div class="rbtn" 
                                                            :style="'background:'+ checkBgColor(defaultModel.memberComp.qiandaoBtn.style.background,defaultModel.memberComp.qiandaoBtn.style.opacity)+'; color:'+defaultModel.memberComp.qiandaoBtn.style.color+';'">
                                                            <s v-if="defaultModel.memberComp.qiandaoBtn.icon"><img :src=" defaultModel.memberComp.qiandaoBtn.icon" alt=""></s>
                                                            <span>{{defaultModel.memberComp.qiandaoBtn.text}}</span>
                                                        </div>
                                                    </div> 
                                                </div>
        
                                                <!-- 数字区域 -->
                                                <div class="dataCount" :style="getStyle('numberCountBox','',defaultModel.memberComp)" v-if=" defaultModel.memberComp.numberCount.showItems && defaultModel.memberComp.numberCount.showItems.length > 0">
                                                    <ul>
                                                        <template v-for="(item,ind) in checkArr(defaultModel.memberComp.numberCount.showItems,'numberCount')">
                                                            <li>
                                                                <h4 :style="getStyle('numberStyle','',defaultModel.memberComp)">{{item.num}}</h4>
                                                                <p :style="getStyle('titleStyle','',defaultModel.memberComp)">{{item.text}}</p>
                                                            </li>
                                                            <li class="line" v-if="defaultModel.memberComp.numberCount.splitLine && ind <= defaultModel.memberComp.numberCount.showItems.length - 2"></li>
                                                        </template>
                                                    </ul>
                                                </div>
        
                                                <!-- 会员 -->
                                                <div class="vipCardContainer" v-if="!defaultModel.memberComp.vipCard.cardHide">
                                                    <div :class="'vipCardCon vipCarCon' + defaultModel.memberComp.vipCard.theme" :style="getStyle('vipCardBox','margin',defaultModel.memberComp)">
                                                        <div :class="[{'vipCardStyle3':defaultModel.memberComp.vipCard.theme === 3}]"  :style="defaultModel.memberComp.vipCard.theme === 3 ? getStyle('vipCardBox','out',defaultModel.memberComp) : ''" v-if="defaultModel.memberComp.vipCard.theme !== 2">
                                                            <div class="vipCardBox vipCardStyle1" :style="getStyle('vipCardBox','inner',defaultModel.memberComp)" >
                                                                <!-- 会员图标 -->
                                                                <div :class="['vipIconBox',{'hasLine':defaultModel.memberComp.vipCard.subtitle && defaultModel.memberComp.vipCard.subtitle.length > 0}]">
                                                                    <div class="imgbox"><img :src="defaultModel.memberComp.vipCard.icon" alt=""></div>
                                                                    <span :style="'color:'+ defaultModel.memberComp.vipCard.titleStyle.color +';'">{{defaultModel.memberComp.vipCard.title}}</span>
                                                                </div>
                                                                <!-- 会员滚动文字 -->
                                                                <div class="vipScrollBox" v-if="defaultModel.memberComp.vipCard.subtitle && defaultModel.memberComp.vipCard.subtitle.length > 0">
                                                                    <el-carousel ref="carousel_vip" :height="(defaultModel.memberComp.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                        <template v-if="defaultModel.memberComp.vipCard.subtitle.length != 2 ">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in defaultModel.memberComp.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.text && item.type !== 1" :style="'color:'+ defaultModel.memberComp.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ defaultModel.memberComp.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                        <template v-else v-for=" i in 2">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in defaultModel.memberComp.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.text && item.type !== 1" :style="'color:'+ defaultModel.memberComp.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ defaultModel.memberComp.vipCard.subStyle.color +';'">xx会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                    </el-carousel>
                                                                </div>
                                                                <div :class="['vip_btn',{'arrow_btn':defaultModel.memberComp.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',defaultModel.memberComp)">
                                                                    <span>{{defaultModel.memberComp.vipCard.btnText}}</span>
                                                                    <s><em class="icon" :style="getStyle('vipCardBtnArr','',defaultModel.memberComp)"></em></s>
                                                                </div>
                                                            </div>
        
                                                            <!-- 样式3的财务信息 -->
                                                            <div class=" financeInfo" v-if="defaultModel.memberComp.vipCard.theme === 3 && defaultModel.memberComp.financeCount.showItems && defaultModel.memberComp.financeCount.showItems.length > 0">
                                                                <ul>
                                                                    <li v-for="item in checkArr(defaultModel.memberComp.financeCount.showItems,'financeCount')" :style="getStyle('financeInfo','',defaultModel.memberComp)"> {{item.text}}<b :style="getStyle('finance_number','',defaultModel.memberComp)">{{item.num}}</b> </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="vipCardStyle2 vipCardBox" :style="getStyle('vipCardBox','',defaultModel.memberComp)" v-else-if="defaultModel.memberComp.vipCard.theme === 2">
                                                            <!-- 会员图标 -->
                                                            <div :class="['vipIconBox',{'hasLine':defaultModel.memberComp.vipCard.subtitle && defaultModel.memberComp.vipCard.subtitle.length > 0}]">
                                                                <div class="imgbox"><img :src="defaultModel.memberComp.vipCard.icon" alt=""></div>
                                                                <span :style="'color:'+ defaultModel.memberComp.vipCard.titleStyle.color +';'">{{defaultModel.memberComp.vipCard.title}}</span>
                                                            </div>
                                                            <!-- 会员滚动文字 -->
                                                            <div class="vipScrollBox" v-if="defaultModel.memberComp.vipCard.subtitle && defaultModel.memberComp.vipCard.subtitle.length > 0">
                                                                <el-carousel ref="carousel_vip" :height="(defaultModel.memberComp.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                    <template v-if="defaultModel.memberComp.vipCard.subtitle.length != 2 ">
                                                                        <el-carousel-item class="bitem" v-for="(item,ind) in defaultModel.memberComp.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.text && item.type !== 1" :style="'color:'+ defaultModel.memberComp.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ defaultModel.memberComp.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                        </el-carousel-item>
                                                                    </template>
                                                                    <template v-else v-for=" i in 2">
                
                                                                        <el-carousel-item class="bitem" v-for="(item,ind) in defaultModel.memberComp.vipCard.subtitle" :key="ind">
                                                                            <p v-if="item.text && item.type !== 1" :style="'color:'+ defaultModel.memberComp.vipCard.subStyle +';'">{{item.text}}</p>
                                                                            <p v-else :style="'color:'+ defaultModel.memberComp.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                        </el-carousel-item>
                                                                    </template>
                                                                </el-carousel>
                                                            </div>
                                                            <div :class="['vip_btn',{'arrow_btn':defaultModel.memberComp.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',defaultModel.memberComp)">
                                                                <span>{{defaultModel.memberComp.vipCard.btnText}}</span>
                                                                <s><em class="icon" :style="getStyle('vipCardBtnArr','',defaultModel.memberComp)"></em></s>
                                                            </div>
                                                        </div>
                                                        <div class="btnsGroupBox " :style="getStyle('vipCardBox','radius',defaultModel.memberComp)"  v-if="defaultModel.memberComp.vipBtnsGroup.length > 0">
                                                            <ul >
                                                                <!-- :style="'width:'+(100 / defaultModel.memberComp.vipBtnsGroup.length)+'%; max-width:25%;'" -->
                                                                <li v-for="(btn,ind) in defaultModel.memberComp.vipBtnsGroup" >
                                                                    <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon1" alt=""></div>
                                                                    <p>{{btn.text ? btn.text : ('按钮' + (ind + 1)) }}</p>
                                                                </li> 
                                                            </ul>
                                                        </div>
                                                    </div>
        
                                                </div>
                                            </div>
                                        </div>
        
                                        
                                        
                                        <!-- 其他内容 -->
                                        <div class="otherSort" >
                                            <!-- JSON.stringify(obj) -->
                                            <template v-for="(obj,oInd) in defaultModel.compArrs">
                                                <div :data-id="oInd" :class="['partBox',{'currEditPart':defaultModel.compArrs.length && currEditPart === obj.id && currEditInd === oInd}]" @click="editCurrPart(obj,oInd)" v-if="currPlatform.id != 'dymini' || obj.id != 6">
                                                    <!-- 订单管理 -->
                                                    <div class="orderInfoBorderBox"  v-if="obj.id == 2">
                                                       
                                                        <div class="orderInfoBox" :style="getStyle('orderInfoBox','',obj.content)" >
                                                            <div class="orderHeader" v-show="obj.content.title.show">
                                                                <h4 :style="getStyle('orderInfoBox','title', obj.content)">{{obj.content.title.text}}</h4>
                                                                <span class="more" v-show="obj.content.more.showType == 0" :style="getStyle('orderInfoBox','more', obj.content)">{{obj.content.more.text}}<s v-if="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></span>
                                                            </div>
                                                            <ul :class='["orderUl",{"gridbox3":obj.content.showItems.length <= 3},{"gridbox4":obj.content.showItems.length == 4},{"gridbox5":obj.content.showItems.length >= 5}]'>
                                                                <li v-for="(btn,ind) in checkArr(obj.content.showItems,'orderFormData')">
                                                                    <div class="icon">
                                                                        <span class="tipNum" :style="getStyle('orderInfoBox','tipNum',obj.content)" v-if="obj.content.showNumItems.includes(btn.id)">{{btn.id}}</span>
                                                                        <img :src="btn.icon" alt="">
                                                                    </div>
                                                                    <p :style="getStyle('orderInfoBox','btnStyle',obj.content)">{{btn.btnText}}</p>
                                                                </li>
                                                            </ul>
                                                        </div> 
                                                    </div>
            
                                                    <!-- 财务卡片 -->
                                                    <div class="orderInfoBorderBox financeShow" v-else-if="obj.id == 3">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="orderInfoBox" :style="getStyle('financeInfoBox','', obj.content)">
                                                            <div class="orderHeader" v-show="obj.content.title.show">
                                                                <h4 :style="getStyle('financeInfoBox','title', obj.content)">{{obj.content.title.text}}</h4>
                                                                <span class="more" v-show="obj.content.more.show" :style="getStyle('financeInfoBox','more', obj.content)">{{obj.content.more.text}}<s v-if="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></span>
                                                            </div>
                                                            <ul :class="'financeInfo_ul gridbox' + obj.content.showItems.length">
                                                                <li v-for="(btn,ind) in checkArr(obj.content.showItems,'financeCount')" >
                                                                    <div class="numberShow" v-if="btn.id != 3 && btn.id != 1" :style="'color:'+ obj.content.numberStyle.color +';'">
                                                                        <span>￥<b>{{btn.num.split('.')[0]}}</b>{{btn.num.split('.').length > 1 ? '.' + btn.num.split('.')[1] : '.00' }}</span>
                                                                    </div>
                                                                    <div class="numberShow" v-else  :style="'color:'+ obj.content.numberStyle.color +';'">
                                                                        <span><b>{{btn.num}}</b></span>{{btn.id == 1 ? '' : '张'}}
                                                                    </div>
                                                                    <p :style="'color:'+ obj.content.textStyle.color +'; font-size:'+(bj.content.textStyle.fontSize / 2)+'px;'">{{btn.text}}</p>
                                                                </li>
                                                            </ul>
                                                        </div> 
                                                    </div>
            
            
                                                    <!-- 图标组 -->
                                                    <div class="orderInfoBorderBox " v-else-if="obj.id == 4">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="orderInfoBox iconsInfoBox" :style="getStyle('iconsInfoBox','', obj.content)">
                                                            <div class="orderHeader" v-show="obj.content.title.show">
                                                                <h4 :style="getStyle('iconsInfoBox','title', obj.content)">{{obj.content.title.text ? obj.content.title.text : '标题文字'}}</h4>
                                                                <span class="more" v-show="obj.content.more.show" :style="getStyle('iconsInfoBox','more', obj.content)">{{obj.content.more.text}}<s v-if="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></span>
                                                            </div>
                                                            <ul :class='"gridbox" + (obj.content.column ? obj.content.column : 5)'>
                                                                <li v-for="(btn,ind) in obj.content.btns.list" >
                                                                    <div class="icon">
                                                                        <img :src="btn.icon ? btn.icon : '{#$cfg_basehost#}/static/images/admin/siteMemberPage/default_icon1.png' " alt="">
                                                                    </div>
                                                                    <p v-if="btn.text" :style="'color:'+obj.content.btns.style.color+'; font-size:'+(obj.content.btns.style.fontSize /2) +'px;'">{{btn.text}}</p>
                                                                    <p v-else class="placeholder" :style="'color:'+obj.content.btns.style.color+'; font-size:'+(obj.content.btns.style.fontSize /2) +'px;'">按钮{{ind + 1}}</p>
                                                                </li>
                                                            </ul>
                                                            <div class="qiandao" v-show="obj.content.qiandao.show" :style="'background:'+ obj.content.qiandao.style.background +';color:'+ obj.content.qiandao.style.color +';'">
                                                                <s><img :src="obj.content.qiandao.icon" alt=""></s><span class="qtext">已签到5天，连签7天赠50积分</span>
                                                                <span class="qbtn" :style="'border-color:'+ obj.content.qiandao.style.color +';'" >去签到</span>
                                                            </div>
                                                        </div>
                                                    </div>
            
            
                                                    <!-- 列表导航 -->
                                                    <div class="orderInfoBorderBox " v-else-if="obj.id == 5">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="orderInfoBox listInfoBox" :style="getStyle('listInfoBox','', obj.content)">
                                                            <ul class="list" v-if="obj.content && obj.content.list">
                                                                <li class="flex_li" :style="'height:'+ (obj.content.style.lineHeight/2) +'px;'" v-for="(btn,ind) in obj.content.list">
                                                                    <div class="flex_l">
                                                                        <div class="icon"><img :src="btn.icon ? btn.icon : '{#$cfg_basehost#}/static/images/admin/siteMemberPage/default_icon2.png'" alt=""></div>
                                                                        <div class="text" :style="'color:' +obj.content.style.color+ ';'">{{btn.text ? btn.text : '文字' + (ind + 1)}}</div>
                                                                    </div>
                                                                    <div class="flex_r">
                                                                        <span class="tip" :style="'color:' +obj.content.tipStyle.color+ ';'">{{btn.tip.text}}</span>
                                                                        <s class="arr" ><em :style="' filter: drop-shadow(12px 0 0 ' +obj.content.tipStyle.color+ '); '"></em></s>
                                                                    </div>
                                                                    <s class="line"  v-show="obj.content.splitLine" v-if="ind != (obj.content.list.length - 1)"></s>
                                                                </li>
                                                            </ul>
                                                            <div class="qiandao" v-show="obj.content.qiandao.show" :style="'background:'+ obj.content.qiandao.style.background +';color:'+ obj.content.qiandao.style.color +';'">
                                                                <s><img :src="obj.content.qiandao.icon" alt=""></s><span class="qtext">已签到5天，连签7天赠50积分</span>
                                                                <span class="qbtn" :style="'border-color:'+ obj.content.qiandao.style.color +';'" >去签到</span>
                                                            </div>
                                                        </div>
                                                    </div>
            
            
                                                    <!-- 关注公众号 -->
                                                    <div :class="['orderInfoBorderBox',{'customWechat':obj.content.custom}]"  v-else-if="obj.id == 6">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="orderInfoBox wechatInfoBox" :style="getStyle('wechatInfo','', obj.content)">
                                                            <template v-if="!obj.content.custom">
                                                                <div class="flex_l">
                                                                    <div class="icon" v-show="obj.content.iconShow"><img :src="obj.content.icon" alt=""></div>
                                                                    <div class="text">
                                                                        <h4 :style="getStyle('wechatInfo','titleStyle', obj.content)">{{obj.content.title}}</h4>
                                                                        <p :style="getStyle('wechatInfo','subtitleStyle', obj.content)">{{obj.content.subtitle}}</p>
                                                                    </div>
                                                                </div> 
                                                                <div class="flex_r" >
                                                                    <span :style="'color:'+obj.content.btnStyle.color+';border-color:'+obj.content.btnStyle.color+';'">关注</span>
                                                                </div>
                                                            </template>
                                                            <img v-show="obj.content.image" :src="obj.content.image" v-else class="image" alt="" />
                                                        </div>
                                                    </div> 
            
            
                                                    <!-- 瓷片广告 -->
                                                    <div :class="['orderInfoBorderBox']"  v-else-if="obj.id == 7">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="orderInfoBox cipianInfoBox" :style="getStyle('advInfo','', obj.content)">
                                                            <div :class="'cipianList cipianList'+  obj.content.column"  :style="getStyle('advInfo','grid',obj.content)" v-if="obj.content.column !== 1">
                                                                <div class="cipian" v-for="adv in obj.content.list" :style="getStyle('advInfo','height', obj.content)" >
                                                                    <img :src="adv.image" alt="" v-show="adv.image">
                                                                </div>
                                                                
                                                            </div>
                                                            <!-- <el-carousel  :autoplay="false" v-else>
                                                                <el-carousel-item class="cipian">
                                                                    <img :src="defaultModel.memberComp.business.icon" alt="">
                                                                </el-carousel-item>
                                                            </el-carousel> -->
                                                        </div>
                                                    </div> 
            
                                                    <div class=" orderInfoBorderBox" v-else-if="obj.id == 8">
                                                        <div class="del_btn" @click="delModBox(oInd)"><s></s>删除</div>
                                                        <div class="titleInfoBox" :style="getStyle('titleInfo','', obj.content)">
                                                            <div class="place" v-show="obj.content.layout=== 1"></div>
                                                            <div :class="['titleInfo',{'alignCenter':obj.content.layout=== 1}]">
                                                                <div class="icon" v-show="obj.content.title.type == 2"><img :src="obj.content.title.icon ? obj.content.title.icon : ('/static/images/admin/siteMemberPage/default_icon2.png')"  alt=""></div>
                                                                <h4 :style="'font-weight:bold; font-size:'+(obj.content.title.style.fontSize / 2)+'px; color:'+obj.content.title.style.color+';'">
                                                                    <span>{{obj.content.title.text ? obj.content.title.text : '标题'}}</span>
                                                                    <div class="tit_line" :style="'background:'+obj.content.title.style.borderColor+';'" v-show="obj.content.title.type === 1"></div>
                                                                </h4>
                                                            </div>
                                                            <div class="moreInfo" v-if="obj.content.more.show" :style="'color:'+obj.content.more.style.color+';'">{{obj.content.more.text}}<s v-show="obj.content.more.arr"><em :style="'filter: drop-shadow(12px 0 0 '+ obj.content.more.style.color +')'"></em></s></div> 
                                                        </div>
                                                    </div>
            
                                                </div>
                                            </template>
                                        </div>
                                    </div>
    
                                    <!-- 编辑展示的 -->
                                    <div class="truePage"  v-show="selfDefine" :style="pageFormData.bgType == 'color' ? `background-color:${pageFormData.style.bgColor1};` : pageFormData.style.bgColor1 && pageFormData.style.bgColor2 ? `background:linear-gradient(180deg,${pageFormData.style.bgColor1} 0%, ${pageFormData.style.bgColor1} ${pageFormData.style.start || 0}%, ${pageFormData.style.bgColor2} calc(${pageFormData.style.start || 0}% + 70px),  ${pageFormData.style.bgColor2} 100%);` : ``">
                                        <!-- v-show="!currEditPart && showTitle" -->
                                        <div class="fixedTop"  id="pageTop1" v-if="currPlatform.id != 'h5' && currPlatform.id != 'dymini'" :style="'background:'+ pageFormData.style.background +';'" >
                                            <div :class="['pagetop']" ><img src="{#$cfg_basehost#}/static/images/admin/phone_head.png" alt=""></div>
                                            
                                            <div class="top_header" :style="getStyle('pageTop','',pageFormData)" >
                                                <div :class="['flex_l',{'flexCenter':pageFormData.layout === 1}]">
                                                    <div class="infoBox" v-show="!pageFormData.showType">
                                                        <div class="photo"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                        <h4 :style="'color:'+ pageFormData.style.color +';'">星辰大海</h4>
                                                    </div>
                                                    <div class="infoBox" v-show="pageFormData.showType">
                                                        <h4 :style="'color:'+ pageFormData.style.color +';'">{{pageFormData.title ? pageFormData.title : '个人中心'}}</h4>
                                                    </div>
                                                </div>
                                                <div class="flex_r" v-if="currPlatform.id != 'wxmini'">
                                                    <ul class="btns"  v-if="pageFormData.btns.showType == 2"> 
                                                        <li v-for="btn in pageFormData.btns.list">
                                                            <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                        </li>
                                                    </ul>
                                                    <ul class="btns"  v-else-if="pageFormData.btns.showType == 0"> 
                                                        <li v-for="btn in memberCompFormData.setBtns.btns">
                                                            <div class="icon"><img  :src="btn.icon ? btn.icon : defaultIcon2 " :style="getStyle('pageBtn','',pageFormData)"  alt=""></div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="wx_btn" v-else><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
                                            </div>                                   
                                        </div>
                                        <div :class="['webFixed '+ memberCompFormData.themeType + 'Style' ,{'show':currEditPart == ''}]" :style="`background-color:${pageFormData.style.bgImgColor};`" v-else v-show="pageFormData.h5FixedTop && memberCompFormData.showHeader && memberCompFormData.theme != 3">
                                            <div class="flex_header">
                                                <div class="flexBox headerTop" v-if="cfg_business_state || memberCompFormData.setBtns.btns.length " :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                    <!-- 左上 -->
                                                    <div class="leftTop" v-if="cfg_business_state">
                                                        <el-carousel ref="carousel" height="30px" direction="vertical" :autoplay="false" indicator-position="none">
                                                            <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.business.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="business">
                                                                <s><img :src="memberCompFormData.business.icon" alt=""></s>
                                                                <span>{{memberCompFormData.business.text}}</span>
                                                            </el-carousel-item>
                                                            <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.branch.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="branch">
                                                                <s><img :src="memberCompFormData.branch.icon" alt=""></s>
                                                                <span>{{memberCompFormData.branch.text}}</span>
                                                            </el-carousel-item>
                                                        </el-carousel>
                                                    </div>
                                                    <div class="leftTop" v-else></div>
        
                                                    <!-- 右上 -->
                                                    <div class="rightTop">
                                                        <ul class="btns" > 
                                                            <li v-for="btn in memberCompFormData.setBtns.btns" :class="{'floatItem fn-clear':memberCompFormData.setBtns.btnType === 2}">
                                                                <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                <p v-if="btn.text">{{btn.text}}</p>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- ,{'lightStyle':memberCompFormData.themeType == 'light'} -->
                                        <div :class="['pagetop']" ><img src="{#$cfg_basehost#}/static/images/admin/phone_head.png" alt=""></div>
                                        <div class="header">
                                            <h4 v-if="currPlatform.id == 'h5'">个人中心</h4>
                                            <div class="flex_header" v-else-if="currPlatform.id == 'dymini'">
                                                <div class="flex_l">
                                                    <div class="web_logo"><img src="{#$cfg_basehost#}/static/images/admin/siteConfigPage/dymini.png
                                                        " alt=""></div>
                                                    <h4>个人中心</h4>
                                                </div>
                                                <div class="flex_r"><img src="{#$cfg_basehost#}/static/images/admin/dy_btn.png" alt=""></div>
                                            </div>
                                        </div>
                                        <!-- 会员信息组件 -->
                                        <div :class="['memberInfoBox ',{'lightStyle':memberCompFormData.themeType == 'light'}]" data-part="memberHeader">
                                            <div :class="['memberInfo','memberInfo' + memberCompFormData.theme,{'currEditPart' : currEditPart === 1}] " @click="currEditPart = 1">
                                                <!-- 样式1 -->
                                                <template v-if="memberCompFormData.theme === 1" >
                                                    <div class="memberBg" :style="getStyle('memberInfoBox','',memberCompFormData)" ></div>
                                                    <!-- 头部 -->
                                                    <div class="flex_header" v-if="!memberCompFormData.hasOwnProperty('showHeader') || memberCompFormData.showHeader || currPlatform.id == 'wxmini'">

                                                        <div class="flexBox headerTop" v-if="cfg_business_state || memberCompFormData.setBtns.btns.length " :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                            <!-- 左上 -->
                                                            <div class="leftTop" v-if="cfg_business_state">
                                                                <el-carousel ref="carousel" height="30px" direction="vertical" :autoplay="false" indicator-position="none">
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.business.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="business">
                                                                        <s><img :src="memberCompFormData.business.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.business.text}}</span>
                                                                    </el-carousel-item>
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.branch.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="branch">
                                                                        <s><img :src="memberCompFormData.branch.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.branch.text}}</span>
                                                                    </el-carousel-item>
                                                                </el-carousel>
                                                            </div>
                                                            <div class="leftTop" v-else></div>
                
                                                            <!-- 右上 -->
                                                            <div class="rightTop">
                                                                <ul class="btns" > 
                                                                    <li v-for="btn in memberCompFormData.setBtns.btns" :class="{'floatItem fn-clear':memberCompFormData.setBtns.btnType === 2}">
                                                                        <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                        <p v-if="btn.text">{{btn.text}}</p>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="wx_btn" v-if="currPlatform.id == 'wxmini'"><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
                                                    </div>
                                                    <div :class="['memberInfoCon']" :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                        <div class="mleftCon fn-clear">
                                                            <div class="photoCon"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                            <div class="textCon">
                                                                <h4>星辰大海</h4>
                                                                <div class="vipIcon">
                                                                    <s class="vip_icon"></s>普通会员
                                                                    <!-- <span>普通会员</span> -->
                                                                </div>
                                                            </div>
                                                            <s class="arr"  v-show="!memberCompFormData.qiandaoBtn.show"><em :style="'filter: drop-shadow(15px 0 0 '+ memberCompFormData.qiandaoBtn.style.arr_color +');'"></em></s>
                                                        </div>
                                                        <div class="mrightCon" v-show="memberCompFormData.qiandaoBtn.show" >
                                                            <div class="rbtn" 
                                                                :style="'background:'+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+'; color:'+memberCompFormData.qiandaoBtn.style.color+';'">
                                                                <s v-if="memberCompFormData.qiandaoBtn.icon"><img :src=" memberCompFormData.qiandaoBtn.icon" alt=""></s>
                                                                <span>{{memberCompFormData.qiandaoBtn.text}}</span>
                                                            </div>
                                                        </div> 
                                                    </div>
            
                                                    <!-- 数字区域 -->
                                                    <div class="dataCount" :style="getStyle('numberCountBox','',memberCompFormData)" v-if="memberCompFormData.numberCount.showItems && memberCompFormData.numberCount.showItems.length">
                                                        <ul>
                                                            <template v-for="(item,ind) in checkArr(memberCompFormData.numberCount.showItems,'numberCount')">
                                                                <li>
                                                                    <h4 :style="getStyle('numberStyle','',memberCompFormData)">{{item.num}}</h4>
                                                                    <p :style="getStyle('titleStyle','',memberCompFormData)">{{item.text}}</p>
                                                                </li>
                                                                <li class="line" :style="' opacity:.1; background:'+ (memberCompFormData.themeType == 'dark' ? memberCompFormData.numberCount.titleStyle.color : '#fff') +';'" v-if="memberCompFormData.numberCount.splitLine && ind <= memberCompFormData.numberCount.showItems.length - 2"></li>
                                                            </template>
                                                        </ul>
                                                    </div>
            
                                                    <!-- 会员 -->
                                                    <div class="vipCardContainer" v-if="!memberCompFormData.vipCard.cardHide">
                                                        <div :class="'vipCardCon vipCarCon' + memberCompFormData.vipCard.theme" :style="getStyle('vipCardBox','margin',memberCompFormData)">
                                                            <div :class="[{'vipCardStyle3':memberCompFormData.vipCard.theme === 3}]"  :style="memberCompFormData.vipCard.theme === 3 ? getStyle('vipCardBox','out',memberCompFormData) : ''" v-if="memberCompFormData.vipCard.theme !== 2">
                                                                <div class="vipCardBox vipCardStyle1" :style="getStyle('vipCardBox','inner',memberCompFormData)" >
                                                                    <!-- 会员图标 -->
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]">
                                                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40px" height="15px" preserveAspectRatio="xMinYMin meet" viewBox="0 0 80 30">
                                                                                <path fill-rule="evenodd" :fill="memberCompFormData.vipCard.titleStyle.color"
                                                                                    d="M13.500,30.000 C12.273,30.000 11.045,28.824 10.432,26.471 L0.000,0.002 L7.978,0.002 L15.342,18.236 L27.001,0.002 L35.593,0.002 L17.797,26.471 C15.955,29.412 14.727,30.000 13.500,30.000 ZM33.040,30.009 L37.942,-0.006 L45.583,-0.006 L40.680,30.009 L33.040,30.009 ZM79.775,11.404 C79.159,14.405 77.926,17.406 74.845,19.806 C72.381,21.607 69.299,22.807 66.220,22.807 L53.278,22.807 L52.046,30.009 L44.651,30.009 L47.117,15.005 L68.067,15.005 C69.299,15.005 69.916,14.405 70.532,13.805 C71.148,13.205 71.765,12.005 71.765,11.404 C71.765,10.204 71.765,9.604 71.148,9.004 C70.532,8.404 69.299,7.803 68.683,7.803 L47.733,7.803 L53.895,0.002 L69.299,0.002 C72.381,0.002 75.461,1.203 77.310,3.003 C79.775,6.003 80.392,9.004 79.775,11.404 L79.775,11.404 Z" />
                                                                            </svg>
                                                                        </div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                    <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                        <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                        <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                    </div>
                                                                </div>
            
                                                                <!-- 样式3的财务信息 -->
                                                                <div class=" financeInfo" v-if="memberCompFormData.vipCard.theme === 3 && memberCompFormData.financeCount.showItems.length > 0">
                                                                    <ul>
                                                                        <li v-for="item in checkArr(memberCompFormData.financeCount.showItems,'financeCount')" :style="getStyle('financeInfo','',memberCompFormData)"> {{item.text}}<b :style="getStyle('finance_number','',memberCompFormData)">{{item.num}}</b> </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="vipCardStyle2 vipCardBox" :style="getStyle('vipCardBox','',memberCompFormData)" v-else-if="memberCompFormData.vipCard.theme === 2">
                                                                <!-- 会员图标 -->
                                                                <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                    <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]"><img :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''" :src="memberCompFormData.vipCard.icon" alt=""></div>
                                                                    <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                </div>
                                                                <!-- 会员滚动文字 -->
                                                                <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                    <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                        <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                        <template v-else v-for=" i in 2">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                    </el-carousel>
                                                                </div>
                                                                <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                    <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                    <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                </div>
                                                            </div>
                                                            <div class="btnsGroupBox " :style="getStyle('vipCardBox','radius',memberCompFormData)" v-show="memberCompFormData.theme !== 1" v-if="memberCompFormData.vipBtnsGroup.length > 0">
                                                                <ul >
                                                                    <li v-for="(btn,ind) in memberCompFormData.vipBtnsGroup" >
                                                                        <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon1" alt=""></div>
                                                                        <p>{{btn.text ? btn.text : ('按钮' + (ind + 1)) }}</p>
                                                                    </li> 
                                                                </ul>
                                                            </div>
                                                        </div>
            
                                                    </div>
                                                </template>
    
                                                <!-- 样式2 -->
                                                <template v-else-if="memberCompFormData.theme === 4" >
                                                    <div class="memberBg" :style="getStyle('memberInfoBox','',memberCompFormData)" ></div>
                                                    <!-- 头部 -->
                                                    <div class="flex_header" v-if="!memberCompFormData.hasOwnProperty('showHeader') || memberCompFormData.showHeader || currPlatform.id == 'wxmini'">

                                                        <div class="flexBox headerTop" v-if="cfg_business_state || memberCompFormData.setBtns.btns.length " :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                            <!-- 左上 -->
                                                            <div class="leftTop" v-if="cfg_business_state">
                                                                <el-carousel ref="carousel" height="30px" direction="vertical" :autoplay="false" indicator-position="none">
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.business.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="business">
                                                                        <s><img :src="memberCompFormData.business.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.business.text}}</span>
                                                                    </el-carousel-item>
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.branch.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="branch">
                                                                        <s><img :src="memberCompFormData.branch.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.branch.text}}</span>
                                                                    </el-carousel-item>
                                                                </el-carousel>
                                                            </div>
                                                            <div class="leftTop" v-else></div>
                
                                                            <!-- 右上 -->
                                                            <div class="rightTop">
                                                                <ul class="btns" > 
                                                                    <li v-for="btn in memberCompFormData.setBtns.btns" :class="{'floatItem fn-clear':memberCompFormData.setBtns.btnType === 2}">
                                                                        <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0},{'white':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/shop_icon01.png') > -1}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                        <p v-if="btn.text">{{btn.text}}</p>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="wx_btn" v-if="currPlatform.id == 'wxmini'"><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
                                                    </div>
                                                    <div :class="['memberInfoCon']" :style="'margin:0 0 0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                        <div class="mleftCon fn-clear">
                                                            <div class="photoCon"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                            <div class="textCon">
                                                                <h4>星辰大海</h4>
                                                                <ul class="dataCount">
                                                                    <li v-for="(item,ind) in circleData">
                                                                        <span>{{item.num}}</span>
                                                                        <em>{{item.text}}</em>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                            <s class="arr" :style="'right:' +(memberCompFormData.cardStyle.marginLeft / 2)+ 'px;'"  v-show="!memberCompFormData.qiandaoBtn.show"><em :style="'filter: drop-shadow(15px 0 0 '+ memberCompFormData.qiandaoBtn.style.arr_color +');'"></em></s>
                                                        </div>
                                                        <div class="mrightCon" v-show="memberCompFormData.qiandaoBtn.show" >
                                                            <div class="rbtn" 
                                                            :style="'background:linear-gradient(90deg, '+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+' 0%, '+ (memberCompFormData.qiandaoBtn.style.background + '00')+' 100%); color:'+memberCompFormData.qiandaoBtn.style.color+';'">
                                                                <s v-if="memberCompFormData.qiandaoBtn.icon"><img :src=" memberCompFormData.qiandaoBtn.icon" alt=""></s>
                                                                <span>{{memberCompFormData.qiandaoBtn.text}}</span>
                                                            </div>
                                                            
                                                        </div> 
                                                    </div>
            
            
                                                    <!-- 会员 -->
                                                    <div class="vipCardContainer" v-if="!memberCompFormData.vipCard.cardHide">
                                                        <div :class="'vipCardCon vipCarCon' + memberCompFormData.vipCard.theme" :style="getStyle('vipCardBox','margin',memberCompFormData)">
                                                            <div :class="[{'vipCardStyle3':memberCompFormData.vipCard.theme === 3}]"  :style="memberCompFormData.vipCard.theme === 3 ? getStyle('vipCardBox','out',memberCompFormData) : ''" v-if="memberCompFormData.vipCard.theme !== 2">
                                                                <div class="vipCardBox vipCardStyle1" :style="getStyle('vipCardBox','inner',memberCompFormData)" >
                                                                    <!-- 会员图标 -->
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]">
                                                                            <img :src="memberCompFormData.vipCard.icon" alt="" :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''">
                                                                        </div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                    <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                        <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                        <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                    </div>
                                                                </div>
            
                                                                <!-- 样式3的财务信息 -->
                                                                <div class=" financeInfo" v-if="memberCompFormData.vipCard.theme === 3 && memberCompFormData.financeCount.showItems.length > 0">
                                                                    <ul>
                                                                        <li v-for="item in checkArr(memberCompFormData.financeCount.showItems,'financeCount')" :style="getStyle('financeInfo','',memberCompFormData)"> {{item.text}}<b :style="getStyle('finance_number','',memberCompFormData)">{{item.num}}</b> </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="vipCardStyle2 vipCardBox" :style="getStyle('vipCardBox','',memberCompFormData)" v-else-if="memberCompFormData.vipCard.theme === 2">
                                                                <div class="flexLeft">
                                                                    <!-- 会员图标 -->
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]"><img :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''" :src="memberCompFormData.vipCard.icon" alt=""></div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                </div>
                                                                <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                    <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                    <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                </div>
                                                            </div>
                                                            <div class="btnsGroupBox " :style="getStyle('vipCardBox','radius',memberCompFormData)" v-show="memberCompFormData.theme !== 1" v-if="memberCompFormData.vipBtnsGroup.length > 0">
                                                                <ul >
                                                                    <li v-for="(btn,ind) in memberCompFormData.vipBtnsGroup" >
                                                                        <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon1" alt=""></div>
                                                                        <p>{{btn.text ? btn.text : ('按钮' + (ind + 1)) }}</p>
                                                                    </li> 
                                                                </ul>
                                                            </div>
                                                        </div>
            
                                                    </div>
                                                </template>
    
                                                <!-- 样式3 -->
                                                <template v-else-if="memberCompFormData.theme === 3" >
                                                    <div class="memberBg" :style="getStyle('memberInfoBox','',memberCompFormData)" ></div>
                                                    <!-- 头部 -->
                                                    <div :class="['memberInfoCon']" :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                        <div class="mleftCon fn-clear">
                                                            <div class="photoCon"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                            <div class="textCon">
                                                                <h4>星辰大海</h4>
                                                                <div class="vipIcon">
                                                                    <s class="vip_icon"></s>
                                                                    <span>普通会员</span>
                                                                </div>
                                                                <!-- <ul class="dataCount">
                                                                    <li v-for="(item,ind) in circleData">
                                                                        <span>{{item.num}}</span>
                                                                        <em>{{item.text}}</em>
                                                                    </li>
                                                                </ul> -->
                                                            </div>
                                                            <!-- <s class="arr"  v-show="!memberCompFormData.qiandaoBtn.show"><em :style="'filter: drop-shadow(15px 0 0 '+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity) +');'"></em></s> -->
                                                        </div>
                                                        <div class="mrightCon rightTop" >
                                                            <ul class="btns" > 
                                                                <li v-for="btn in memberCompFormData.setBtns.btns" :class="{'floatItem fn-clear':memberCompFormData.setBtns.btnType === 2}">
                                                                    <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                    <p v-if="btn.text">{{btn.text}}</p>
                                                                </li>
                                                            </ul>
                                                            
                                                        </div> 
                                                    </div>
                                                    
                                                    <div class="dataCountBox" :style="'margin-left:'+(memberCompFormData.cardStyle.marginLeft / 2)+'px;'">
                                                        <ul class="dataCount">
                                                            <li v-for="(item,ind) in circleData">
                                                                <span>{{item.num}}</span>
                                                                <em>{{item.text}}</em>
                                                            </li>
                                                        </ul>
                                                        <div class="mrightCon" v-show="cfg_business_state">
                                                            <el-carousel ref="carousel" height="32px" direction="vertical" :autoplay="false" indicator-position="none">
                                                                <el-carousel-item  :style="'background:linear-gradient(90deg, '+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+' 0%, '+ (memberCompFormData.qiandaoBtn.style.background + '00')+' 100%); color:'+memberCompFormData.qiandaoBtn.style.color+';'" :class="['rbtn']" name="business">
                                                                    <s><img :src="memberCompFormData.business.icon" alt=""></s>
                                                                    <span>{{memberCompFormData.business.text}}</span>
                                                                </el-carousel-item>
                                                                <el-carousel-item  :style="'background:linear-gradient(90deg, '+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+' 0%, '+ (memberCompFormData.qiandaoBtn.style.background + '00') +' 100%); color:'+memberCompFormData.qiandaoBtn.style.color+';'"  :class="['rbtn']" name="branch">
                                                                    <s><img :src="memberCompFormData.branch.icon" alt=""></s>
                                                                    <span>{{memberCompFormData.branch.text}}</span>
                                                                </el-carousel-item>
                                                            </el-carousel>
                                                            <!-- <div class="rbtn"  
                                                                :style="'background:'+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+'; color:'+memberCompFormData.qiandaoBtn.style.color+';'">
                                                                <s v-if="memberCompFormData.qiandaoBtn.icon"><img :src=" memberCompFormData.qiandaoBtn.icon" alt=""></s>
                                                                <span>{{memberCompFormData.qiandaoBtn.text}}</span>
                                                            </div> -->
                                                        </div> 
                                                    </div> 
            
                                                    <!-- 会员 -->
                                                    <div class="vipCardContainer" v-if="!memberCompFormData.vipCard.cardHide">
                                                        <div :class="'vipCardCon vipCarCon' + memberCompFormData.vipCard.theme" :style="getStyle('vipCardBox','margin',memberCompFormData)">
                                                            <div :class="[{'vipCardStyle3':memberCompFormData.vipCard.theme === 3}]"  :style="memberCompFormData.vipCard.theme === 3 ? getStyle('vipCardBox','out',memberCompFormData) : ''" v-if="memberCompFormData.vipCard.theme !== 2">
                                                                <div class="vipCardBox vipCardStyle1" :style="getStyle('vipCardBox','inner',memberCompFormData)" >
                                                                    <!-- 会员图标 -->
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]">
                                                                            <img :src="memberCompFormData.vipCard.icon" alt="" :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''">
                                                                        </div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                    <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                        <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                        <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                    </div>
                                                                </div>
            
                                                                <!-- 样式3的财务信息 -->
                                                                <div class=" financeInfo" v-if="memberCompFormData.vipCard.theme === 3 && memberCompFormData.financeCount.showItems.length > 0">
                                                                    <ul>
                                                                        <li v-for="item in checkArr(memberCompFormData.financeCount.showItems,'financeCount')" :style="getStyle('financeInfo','',memberCompFormData)"> {{item.text}}<b :style="getStyle('finance_number','',memberCompFormData)">{{item.num}}</b> </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="vipCardStyle2 vipCardBox" :style="getStyle('vipCardBox','',memberCompFormData)" v-else-if="memberCompFormData.vipCard.theme === 2">
                                                                <!-- 会员图标 -->
                                                                <div class="flexLeft">
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]"><img :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''" :src="memberCompFormData.vipCard.icon" alt=""></div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                </div>
                                                                <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                    <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                    <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                </div>
                                                            </div>
                                                            <div class="btnsGroupBox " :style="getStyle('vipCardBox','radius',memberCompFormData)" v-show="memberCompFormData.theme !== 1" v-if="memberCompFormData.vipBtnsGroup.length > 0">
                                                                <ul >
                                                                    <li v-for="(btn,ind) in memberCompFormData.vipBtnsGroup" >
                                                                        <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon1" alt=""></div>
                                                                        <p>{{btn.text ? btn.text : ('按钮' + (ind + 1)) }}</p>
                                                                    </li> 
                                                                </ul>
                                                            </div>
                                                        </div>
            
                                                    </div>
                                                </template>
    
                                                <!-- 样式4 -->
                                                <template v-else-if="memberCompFormData.theme === 2" >
                                                    <div class="memberBg" :style="getStyle('memberInfoBox','',memberCompFormData)" ></div>
                                                    <!-- 头部 -->
                                                    <div class="flex_header" v-if="!memberCompFormData.hasOwnProperty('showHeader') || memberCompFormData.showHeader || currPlatform.id == 'wxmini'">
                                                        <div class="flexBox headerTop" v-if="cfg_business_state || memberCompFormData.setBtns.btns.length " :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                            <!-- 左上 -->
                                                            <div class="leftTop" v-if="cfg_business_state">
                                                                <el-carousel ref="carousel" height="30px" direction="vertical" :autoplay="false" indicator-position="none">
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.business.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="business">
                                                                        <s><img :src="memberCompFormData.business.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.business.text}}</span>
                                                                    </el-carousel-item>
                                                                    <el-carousel-item :class="['bitem',{'canWhite':memberCompFormData.branch.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]" name="branch">
                                                                        <s><img :src="memberCompFormData.branch.icon" alt=""></s>
                                                                        <span>{{memberCompFormData.branch.text}}</span>
                                                                    </el-carousel-item>
                                                                </el-carousel>
                                                            </div>
                                                            <div class="leftTop" v-else></div>
                
                                                            <!-- 右上 -->
                                                            <div class="rightTop">
                                                                <ul class="btns" > 
                                                                    <li v-for="btn in memberCompFormData.setBtns.btns" :class="{'floatItem fn-clear':memberCompFormData.setBtns.btnType === 2}">
                                                                        <div :class="['icon',{'canWhite':btn.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') === 0}]"><img :src="btn.icon ? btn.icon : defaultIcon2 " alt=""></div>
                                                                        <p v-if="btn.text">{{btn.text}}</p>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="wx_btn" v-if="currPlatform.id == 'wxmini'"><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
                                                    </div> 
                                                    <div :class="['memberInfoCon']" :style="'margin:0 ' + (memberCompFormData.cardStyle.marginLeft / 2) +'px'">
                                                        <div class="mleftCon fn-clear">
                                                            <div class="photoCon"><img src="/static/images/noPhoto_100.jpg" alt=""></div>
                                                            <div class="textCon">
                                                                <h4>星辰大海</h4>
                                                                <div class="vipIcon">
                                                                    <s class="vip_icon"></s>
                                                                    <span>普通会员</span>
                                                                </div>
                                                            </div>
                                                            <s class="arr"  v-show="!memberCompFormData.qiandaoBtn.show"><em :style="'filter: drop-shadow(15px 0 0 '+ memberCompFormData.qiandaoBtn.style.arr_color +';'"></em></s>
                                                        </div>
                                                        <div class="mrightCon" v-show="memberCompFormData.qiandaoBtn.show" >
                                                            <div class="rbtn" 
                                                                :style="'background:'+ checkBgColor(memberCompFormData.qiandaoBtn.style.background,memberCompFormData.qiandaoBtn.style.opacity)+'; color:'+memberCompFormData.qiandaoBtn.style.color+';'">
                                                                <s v-if="memberCompFormData.qiandaoBtn.icon"><img :src=" memberCompFormData.qiandaoBtn.icon" alt=""></s>
                                                                <span>{{memberCompFormData.qiandaoBtn.text}}</span>
                                                            </div>
                                                        </div> 
                                                    </div>
            
                                                    <!-- 数字区域 -->
                                                    <div class="dataCount" :style="getStyle('numberCountBox','',memberCompFormData)" v-if="memberCompFormData.numberCount.showItems && memberCompFormData.numberCount.showItems.length">
                                                        <ul>
                                                            <template v-for="(item,ind) in checkArr(memberCompFormData.numberCount.showItems,'numberCount')">
                                                                <li>
                                                                    <h4 :style="getStyle('numberStyle','',memberCompFormData)">{{item.num}}</h4>
                                                                    <p :style="getStyle('titleStyle','',memberCompFormData)">{{item.text}}</p>
                                                                </li>
                                                                <li class="line" :style="' opacity:.1; background:'+ (memberCompFormData.themeType == 'dark' ? memberCompFormData.numberCount.titleStyle.color : '#fff') +';'" v-if="memberCompFormData.numberCount.splitLine && ind <= memberCompFormData.numberCount.showItems.length - 2"></li>
                                                            </template>
                                                        </ul>
                                                    </div>
            
                                                    <!-- 会员 -->
                                                    <div class="vipCardContainer" v-if="!memberCompFormData.vipCard.cardHide">
                                                        <div :class="'vipCardCon vipCarCon' + memberCompFormData.vipCard.theme" :style="getStyle('vipCardBox','margin',memberCompFormData)">
                                                            <div :class="[{'vipCardStyle3':memberCompFormData.vipCard.theme === 3}]"  :style="memberCompFormData.vipCard.theme === 3 ? getStyle('vipCardBox','out',memberCompFormData) : ''" v-if="memberCompFormData.vipCard.theme !== 2">
                                                                <div class="vipCardBox vipCardStyle1" :style="getStyle('vipCardBox','inner',memberCompFormData)" >
                                                                    <!-- 会员图标 -->
                                                                    <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                        <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]">
                                                                            <img :src="memberCompFormData.vipCard.icon" alt="" :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''">
                                                                        </div>
                                                                        <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                    </div>
                                                                    <!-- 会员滚动文字 -->
                                                                    <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                        <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                            <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                            <template v-else v-for=" i in 2">
                        
                                                                                <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                    <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                    <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                                </el-carousel-item>
                                                                            </template>
                                                                        </el-carousel>
                                                                    </div>
                                                                    <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                        <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                        <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                    </div>
                                                                </div>
            
                                                                <!-- 样式3的财务信息 -->
                                                                <div class=" financeInfo" v-if="memberCompFormData.vipCard.theme === 3 && memberCompFormData.financeCount.showItems.length > 0">
                                                                    <ul>
                                                                        <li v-for="item in checkArr(memberCompFormData.financeCount.showItems,'financeCount')" :style="getStyle('financeInfo','',memberCompFormData)"> {{item.text}}<b :style="getStyle('finance_number','',memberCompFormData)">{{item.num}}</b> </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="vipCardStyle2 vipCardBox" :style="getStyle('vipCardBox','',memberCompFormData)" v-else-if="memberCompFormData.vipCard.theme === 2">
                                                                <!-- 会员图标 -->
                                                                <div :class="['vipIconBox',{'hasLine':memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0}]">
                                                                    <div :class="['imgbox',{'canWhite':memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1}]"><img :style="memberCompFormData.vipCard.icon.indexOf('{#$cfg_basehost#}/static/images/admin/') > -1 ? 'filter: drop-shadow(41px 0 0 '+ memberCompFormData.vipCard.titleStyle.color +');' : ''" :src="memberCompFormData.vipCard.icon" alt=""></div>
                                                                    <span :style="'color:'+ memberCompFormData.vipCard.titleStyle.color +';'">{{memberCompFormData.vipCard.title}}</span>
                                                                </div>
                                                                <!-- 会员滚动文字 -->
                                                                <div class="vipScrollBox" v-if="memberCompFormData.vipCard.subtitle && memberCompFormData.vipCard.subtitle.length > 0">
                                                                    <el-carousel ref="carousel_vip" :height="(memberCompFormData.vipCard.theme === 3 ? 30 : 25 ) + 'px'" direction="vertical" :autoplay="true" :loop="true" indicator-position="none">
                                                                        <template v-if="memberCompFormData.vipCard.subtitle.length != 2 ">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                        <template v-else v-for=" i in 2">
                    
                                                                            <el-carousel-item class="bitem" v-for="(item,ind) in memberCompFormData.vipCard.subtitle" :key="ind">
                                                                                <p v-if="item.type !== 1 && item.text" :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">{{item.text}}</p>
                                                                                <p v-else :style="'color:'+ memberCompFormData.vipCard.subStyle.color +';'">会员已过期52天</p>
                                                                            </el-carousel-item>
                                                                        </template>
                                                                    </el-carousel>
                                                                </div>
                                                                <div :class="['vip_btn',{'arrow_btn':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}]" :style="getStyle('vipCardBtn','',memberCompFormData)">
                                                                    <span>{{memberCompFormData.vipCard.btnText}}</span>
                                                                    <s><em class="icon" :style="getStyle('vipCardBtnArr','',memberCompFormData)"></em></s>
                                                                </div>
                                                            </div>
                                                            <div class="btnsGroupBox " :style="getStyle('vipCardBox','radius',memberCompFormData)" v-show="memberCompFormData.theme !== 1" v-if="memberCompFormData.vipBtnsGroup.length > 0">
                                                                <ul >
                                                                    <li v-for="(btn,ind) in memberCompFormData.vipBtnsGroup" >
                                                                        <div class="icon"><img :src="btn.icon ? btn.icon : defaultIcon1" alt=""></div>
                                                                        <p>{{btn.text ? btn.text : ('按钮' + (ind + 1)) }}</p>
                                                                    </li> 
                                                                </ul>
                                                            </div>
                                                        </div>
            
                                                    </div>
                                                </template>
    
                                            </div>
                                        </div>
        
                                        
                                        <!-- 其他内容 -->
                                        <div class="otherSort otherBox">
                                            <!-- JSON.stringify(obj) -->
                                            <div   :data-id="oInd"  :class="['partBox',{'currEditPart':showContainerArr && showContainerArr.length && currEditPart === obj.id && currEditInd === oInd}]" @click="editCurrPart(obj,oInd)" v-for="(obj,oInd) in showContainerArr" >
                                                <!-- 订单管理 -->
                                                <div class="orderInfoBorderBox" v-if="obj.id === 2">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 2 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox" :style="getStyle('orderInfoBox','',obj.content)" >
                                                        <div class="orderHeader" v-show="obj.content.title.show">
                                                            <h4 :style="getStyle('orderInfoBox','title', obj.content)">{{obj.content.title.text}}</h4>
                                                            <span class="more" v-show="obj.content.more.showType == 0" :style="getStyle('orderInfoBox','more', obj.content)">{{obj.content.more.text}}
                                                                <s v-if="obj.content.more.arr">
                                                                    <!--  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg> -->
                                                                  
                                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg>
                                                                </s>
                                                            </span>
                                                        </div>
                                                        <ul :class='["orderUl",{"gridbox3":obj.content.showItems.length <= 3},{"gridbox4":obj.content.showItems.length == 4},{"gridbox5":obj.content.showItems.length >= 5}]'>
                                                            <li v-for="(btn,ind) in checkArr(obj.content.showItems,'orderFormData')">
                                                                <div class="icon">
                                                                    <span class="tipNum" :style="getStyle('orderInfoBox','tipNum',obj.content)" v-if="obj.content.showNumItems.includes(btn.id)">{{btn.id}}</span>
                                                                    <img :src="btn.icon" alt="">
                                                                </div>
                                                                <p :style="getStyle('orderInfoBox','btnStyle',obj.content)">{{btn.btnText}}</p>
                                                            </li>
                                                        </ul>
                                                    </div> 
                                                    
                                                </div>
        
                                                <!-- 财务卡片 -->
                                                <div class="orderInfoBorderBox financeShow" v-else-if="obj.id == 3">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 3 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox" :style="getStyle('financeInfoBox','', obj.content)">
                                                        <div class="orderHeader" v-show="obj.content.title.show">
                                                            <h4 :style="getStyle('financeInfoBox','title', obj.content)">{{obj.content.title.text}}</h4>
                                                            <span class="more" v-show="obj.content.more.show" :style="getStyle('financeInfoBox','more', obj.content)">{{obj.content.more.text}}<s v-if="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></span>
                                                        </div>
                                                        <ul :class="'financeInfo_ul gridbox' + obj.content.showItems.length">
                                                            <li v-for="(btn,ind) in checkArr(obj.content.showItems,'financeCount')">
                                                                <div class="numberShow" v-if="btn.id != 3 && btn.id != 1" :style="'color:'+ obj.content.numberStyle.color +';'">
                                                                    <span>￥<b>{{btn.num.split('.')[0]}}</b>.{{btn.num.split('.').length > 1 ? btn.num.split('.')[1] : '00'}}</span>
                                                                </div>
                                                                <div class="numberShow" v-else  :style="'color:'+ obj.content.numberStyle.color +';'">
                                                                    <span><b>{{btn.num}}</b></span>{{btn.id === 3 ? "张" : ""}}
                                                                </div>
                                                                <p :style="'color:'+ obj.content.textStyle.color +'; font-size:'+(obj.content.textStyle.fontSize / 2)+'px;'">{{btn.text}}</p>
                                                            </li>
                                                        </ul>
                                                    </div> 
                                                </div>
        
        
                                                <!-- 图标组 -->
                                                <div class="orderInfoBorderBox " v-else-if="obj.id == 4">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 4 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox iconsInfoBox" :style="getStyle('iconsInfoBox','', obj.content)">
                                                        <div class="orderHeader" v-show="obj.content.title.show">
                                                            <h4 :style="getStyle('iconsInfoBox','title', obj.content)">{{obj.content.title.text ? obj.content.title.text : '标题文字'}}</h4>
                                                            <span class="more" v-show="obj.content.more.show" :style="getStyle('iconsInfoBox','more', obj.content)">{{obj.content.more.text}}<s v-if="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></span>
                                                        </div>
                                                        <ul :class='"gridbox" + (obj.content.column ? obj.content.column : 5)'>
                                                            <li v-for="(btn,ind) in obj.content.btns.list" >
                                                                <div class="icon">
                                                                    <img :src="btn.icon ? btn.icon : '{#$cfg_basehost#}/static/images/admin/siteMemberPage/default_icon1.png' " alt="">
                                                                </div>
                                                                <p v-if="btn.text" :style="'color:'+obj.content.btns.style.color+'; font-size:'+(obj.content.btns.style.fontSize / 2)+'px;'">{{btn.text}}</p>
                                                                <p v-else class="placeholder" :style="' font-size:'+(obj.content.btns.style.fontSize / 2)+'px;'">按钮{{ind + 1}}</p>
                                                            </li>
                                                        </ul>
                                                        <div class="qiandao" v-show="obj.content.qiandao.show" :style="'background:'+ obj.content.qiandao.style.background +';color:'+ obj.content.qiandao.style.color +';'">
                                                            <s><img :src="obj.content.qiandao.icon" alt=""></s><span class="qtext">已签到5天，连签7天赠50积分</span>
                                                            <span class="qbtn" :style="'border-color:'+ obj.content.qiandao.style.color +';'" >去签到</span>
                                                        </div>
                                                    </div>
                                                </div>
        
        
                                                <!-- 列表导航 -->
                                                <div class="orderInfoBorderBox " v-else-if="obj.id == 5">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 5 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox listInfoBox" :style="getStyle('listInfoBox','', obj.content)">
                                                        <ul class="list">
                                                            <li class="flex_li" :style="'height:'+ (obj.content.style.lineHeight / 2) +'px;'" v-for="(btn,ind) in obj.content.list">
                                                                <div class="flex_l">
                                                                    <div class="icon" v-show="obj.content.iconShow"><img :src="btn.icon ? btn.icon : '{#$cfg_basehost#}/static/images/admin/siteMemberPage/default_icon2.png'" alt=""></div>
                                                                    <div class="text" :style="'color:' +obj.content.style.color+ ';'">{{btn.text ? btn.text : '文字' + (ind + 1)}}</div>
                                                                </div>
                                                                <div class="flex_r">
                                                                    <span class="tip" :style="'color:' +obj.content.tipStyle.color+ ';'">{{btn.tip.text}}</span>
                                                                    <s class="arr" ><em :style="' filter: drop-shadow(12px 0 0 ' +obj.content.tipStyle.color+ '); '"></em></s>
                                                                </div>
                                                                <s class="line" v-show="obj.content.splitLine" v-if="obj.content.list && obj.content.list.length && ind != (obj.content.list.length - 1)"></s>
                                                            </li>
                                                        </ul>
                                                        <div class="qiandao" v-show="obj.content.qiandao.show" :style="'background:'+ obj.content.qiandao.style.background +';color:'+ obj.content.qiandao.style.color +';'">
                                                            <s><img :src="obj.content.qiandao.icon" alt=""></s><span class="qtext">已签到5天，连签7天赠50积分</span>
                                                            <span class="qbtn" :style="'border-color:'+ obj.content.qiandao.style.color +';'" >去签到</span>
                                                        </div>
                                                    </div>
                                                </div>
        
        
                                                <!-- 关注公众号 -->
                                                <div :class="['orderInfoBorderBox',{'customWechat':obj.content.custom}]"  v-else-if="obj.id == 6">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 6 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox wechatInfoBox" :style="getStyle('wechatInfo','', obj.content)">
                                                        <template v-if="!obj.content.custom">
                                                            <div class="flex_l">
                                                                <div class="icon" v-show="obj.content.iconShow"><img :src="obj.content.icon" alt=""></div>
                                                                <div class="text">
                                                                    <h4 :style="getStyle('wechatInfo','titleStyle', obj.content)">{{obj.content.title}}</h4>
                                                                    <p :style="getStyle('wechatInfo','subtitleStyle', obj.content)">{{obj.content.subtitle}}</p>
                                                                </div>
                                                            </div> 
                                                            <div class="flex_r" >
                                                                <span :style="'color:'+obj.content.btnStyle.color+';border-color:'+obj.content.btnStyle.color+';'">关注</span>
                                                            </div>
                                                        </template>
                                                        <img v-show="obj.content.image" :src="obj.content.image" v-else class="image" alt="" />
                                                    </div>
                                                </div> 
        
        
                                                <!-- 瓷片广告 -->
                                                <div :class="['orderInfoBorderBox']"  v-else-if="obj.id == 7">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 7 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="orderInfoBox cipianInfoBox" :style="getStyle('advInfo','', obj.content)" v-if="obj.content.list && obj.content.list.length">
                                                        <div :class="'cipianList cipianList'+  obj.content.column" :style="getStyle('advInfo','grid',obj.content)" v-if="obj.content.column !== 1">
                                                            <div class="cipian" v-for="adv in obj.content.list" :style="'border-radius:'+(obj.content.style.borderRadius / 2) +'px;' + getStyle('advInfo','height', obj.content) ">
                                                                <img :src="adv.image" alt="" v-show="adv.image" :style="'border-radius:'+(obj.content.style.borderRadius / 2) +'px;'">
                                                            </div>
                                                        </div>
                                                        <el-carousel :height="(obj.content.style.height / 2) + 'px'"  arrow="never" :loop="true" class="cipianList" :autoplay="true" v-else>
                                                            <el-carousel-item class="cipian" v-for="adv in obj.content.list" :style="getStyle('advInfo','height', obj.content)">
                                                                <img v-show="adv.image" :src="adv.image"alt="" >
                                                            </el-carousel-item>
                                                        </el-carousel>
                                                    </div>
                                                </div> 
        
                                                <!-- 分隔标题 -->
                                                <div class=" orderInfoBorderBox" v-else-if="obj.id == 8">
                                                    <el-popover popper-class="alertPop" :visible-arrow="false" :value="obj.id == 8 && currEditInd == oInd && showPop" @show="showPop = true">
                                                        <div class="text_con">
                                                            <h4>确认删除该组件？</h4>
                                                        </div>
                                                        <div class="alertBtn">
                                                            <a href="javascript:;" class="cancel" @click="showPop= false"><s></s><span>取消</span></a>
                                                            <a href="javascript:;" class="sure" @click="delModBox(oInd)"><s></s><span>确定</span></a>
                                                        </div>
                                                        <div :class="['del_btn',{'show':currEditInd == oInd && showPop && currEditPart == obj.id}]" slot="reference" ><s></s>删除</div>
                                                    </el-popover>
                                                    <div class="titleInfoBox" :style="getStyle('titleInfo','', obj.content)">
                                                        <div class="place" v-show="obj.content.layout=== 1"></div>
                                                        <div :class="['titleInfo',{'alignCenter':obj.content.layout=== 1}]">
                                                            <div class="icon" v-show="obj.content.title.type == 2"><img :src="obj.content.title.icon ? obj.content.title.icon : ('/static/images/admin/siteMemberPage/default_icon2.png')"  alt=""></div>
                                                            <h4 :style="'font-weight:bold; font-size:'+(obj.content.title.style.fontSize/2)+'px; color:'+obj.content.title.style.color+';'">
                                                                <span>{{obj.content.title.text ? obj.content.title.text : '标题'}}</span>
                                                                <div class="tit_line" :style="'background:'+obj.content.title.style.borderColor+';'" v-show="obj.content.title.type === 1"></div>
                                                            </h4>
                                                        </div>
                                                        <div class="moreInfo" v-if="obj.content.more.show" :style="'color:'+obj.content.more.style.color+';'">{{obj.content.more.text}}<s v-show="obj.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 4 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="obj.content.more.style.color ? obj.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></s></div> 
                                                    </div>
                                                </div>
        
                                            </div>
                                        </div>
    
    
                                    
                                        <div class="public_bottom" @click="showConfirmModel">
                                            <ul :class="'gridbox' + bottomNavs.length" :style="'grid-template-columns:repeat('+bottomNavs.length+',1fr);' "> 
                                                <li v-for="(item,ind) in bottomNavs" :class="[{'fabu':item.fabu}]">
                                                    <div class="icon_box"><img :src="(ind == bottomNavs.length - 1) ? item.icon_h : item.icon" alt=""></div>
                                                    <p v-if="!item.fabu">{{item.name}}</p>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <p class="tip_page" v-show="showLine">一屏高度参考机型为iPhoneX~14</p>
                            </div>
                        </div>
        
        
                    </div>
                </div>
            </div>
    
    
    
    
    
            <!-- 右侧内容 -->
            


            <div class="allRightBoxContainer" v-if="hasSetModel">
                <div class="noClickMask" v-show="selfDefine == 0"  @click.stop="openError"></div>
                <!-- 会员组件 -->
                <div :class="['rightCon',{'show':currEditPart === 1}] " v-cloak>
                    <!-- 会员信息组件 -->
                    <div class="memberInfoStyleContainer modBox" >
                        <div class="title">会员信息 
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 1 && showResetPop" @show="showResetPop = true" v-if="!config">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('memberComp',0)"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 1 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('memberComp',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference" @click="">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 1 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end" >
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('memberComp',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>
                            
                        </div>
                        <div class="memberInfoStyle">
                            <div class=" modstyleBox" v-if=" currPlatform.id != 'wxmini' && memberCompFormData.theme != 3">
                                <h2 :class="['stitle']" >顶部操作区</h2>
                                <div class="sContent" >
                                    <div class="style_item noBg" style="padding-top: 0 !important;">
                                        <div class="style_options" style="padding-left: 6px; margin-bottom: 0;">
                                            <label for=""> 是否显示：</label>
                                            <div class="split_options radioBox">
                                                <span v-for="(item,key) in 2" :class="{'onChose':memberCompFormData.showHeader == (!key ? 1 : 0)}"  @click="memberCompFormData.showHeader = (!key ? 1 : 0)"><s></s>{{key == 0 ? '显示' : '不显示'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 组件样式 -->
                            <div class="modstyleBox">
                                <h2 class="stitle">组件样式</h2>
                                <div class="changeStyle">
                                    <a href="javascript:;" class="btn_change" @click="showMemberTopStylePop = true">修改样式</a>
                                    <span>当前样式：样式{{numberText[currMemberStyle]}}</span>
                                </div>
                                <div class="titleSetBox linkSetBox"> 
                                    <el-input :class="['bgColorInp linkChose',{'hasVal':memberCompFormData.headLink},{'telLink':memberCompFormData.headLinkInfo && memberCompFormData.headLinkInfo.selfSetTel }]"   style="margin-bottom: 10px; margin-top: 0; "  placeholder="请选择会员头像的跳转链接" readonly :value="solveShowtext(memberCompFormData.headLinkInfo,memberCompFormData.headLink)" @focus="changeLink('memberCompFormData.headLink')"></el-input>
                                </div> 
                                <div class="style fn-clear" style="z-index: 999; position:relative;">
                                    <div class="column_chose">
                                        <s :class="memberCompFormData.themeType"></s>
                                        <span data-col="dark" @click="changeThemeStyle('dark')">深色</span>
                                        <span data-col="light" @click=" changeThemeStyle('light')">反白</span>
                                    </div>
                                    <div class="colorPickerBox">
                                        <div class="color_picker"  data-type="1">
                                            <div class="color bgimg" v-show="memberCompFormData.bgType == 'image' || memberCompFormData.style.bg_color.indexOf('linear-gradient') > -1">
                                                <div class="imgShow" v-if="memberCompFormData.bgType == 'image'"><img :src="memberCompFormData.style.bg_image" alt=""></div>
                                                <div class="imgShow" v-else :style="'background:'+ memberCompFormData.style.bg_color +';'"></div>
                                            </div>
                                            <div class="color" id="colorPicker1"></div>
                                        </div>
                                        <!-- 显示官方提供的图 -->
                                        <a href="javascript:;" class="btn_showPop" @click="showMemberCardPop = true;memberCompFormData.bgType = 'image' "></a>
                                    </div> 
                                </div>
                            </div>

                            <!-- 内容设置 -->
                            <div class="contentSetBox ">
                                <h2 :class="['stitle hasArr',{'slideup':showContentSet}]" @click="showContentSet = !showContentSet">内容设置</h2>
                                <el-collapse-transition >
                                    <div class="sContent" v-show="showContentSet">
                                        <!-- 左上 -->
                                        <dl class="setbox" v-show="memberCompFormData.theme !== 3 && cfg_business_state">
                                            <dt><h5>左上区域</h5><span class="tip">（按用户实况显示切换商家版、或员工工作台）</span></dt>
                                            <dd class="btn_item " v-if="memberCompFormData.theme !== 3">
                                                <div class="item">
                                                    <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                    <div class="item_con">
                                                        <div class="img_up fn-clear">
                                                            <div class="upbtn hasup smShowImg">
                                                                <input type="file" name="Filedata" id="business_filedata" data-type="business" accept="png,jpg,jpeg" class="fileUp">
                                                                <img :src="memberCompFormData.business.icon" alt="">
                                                                <span>更换图片</span>
                                                            </div>
                                                            <div class="imgText">
                                                                <h4>切换商家版</h4>
                                                                <p>建议图标尺寸70*70px</p>
                                                            </div>
                                                        </div>
                                                        <div class="inpbox">
                                                            <el-input v-model="memberCompFormData.business.text" @focus="currFocus = 'business'" maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                        </div>
                                                        <div class="inpbox linkbox linkChose">
                                                            <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(memberCompFormData.business.linkInfo && memberCompFormData.business.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                            <input type="text" placeholder="请选择按钮链接" class="iconLink disabled" readonly :value="memberCompFormData.business.linkInfo && memberCompFormData.business.linkInfo.linkText ? memberCompFormData.business.linkInfo.linkText :memberCompFormData.business.link" >
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="item" v-show="memberCompFormData.branchShow">
                                                    <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                    <div class="item_con">
                                                        <s class="del_item" @click="memberCompFormData.branchShow = false"></s>
                                                        <div class="img_up fn-clear ">
                                                            <div class="upbtn hasup smShowImg">
                                                                <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="branch_filedata" data-type="branch">
                                                                <img :src="memberCompFormData.branch.icon" alt="">
                                                                <span>更换图片</span>
                                                            </div>
                                                            <div class="imgText">
                                                                <h4>员工工作台</h4>
                                                                <p>建议图标尺寸70*70px</p>
                                                            </div>
                                                        </div>
                                                        <div class="inpbox">
                                                            <el-input v-model="memberCompFormData.branch.text" @focus="currFocus = 'branch'" maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                        </div>
                                                        <div class="inpbox linkbox linkChose">
                                                            <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(memberCompFormData.branch.linkInfo && memberCompFormData.branch.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                            <input type="text" placeholder="请选择按钮链接"  :value="memberCompFormData.branch.linkInfo && memberCompFormData.branch.linkInfo.linkText ? memberCompFormData.branch.linkInfo.linkText : memberCompFormData.branch.link" readonly class="iconLink disabled">
                                                        </div>
                                                    </div>
                                                </div>
                                                <a href="javascript:;" class="add_more" @click="memberCompFormData.branchShow = true" v-show="!memberCompFormData.branchShow"><s></s>添加员工工作台</a>
                                            </dd>
                                        </dl>

                                        <!-- 右上 -->
                                        <dl class="setbox">
                                            <dt><h5>右上区域</h5>
                                                <div class="choseItemBox">
                                                    <el-checkbox label="添加文本(小)"  :value="memberCompFormData.setBtns.btnType === 1" @change="changeBtnType(1)"></el-checkbox>
                                                    <el-checkbox label="添加文本(大)"  @change="changeBtnType(2)" :value="memberCompFormData.setBtns.btnType === 2" ></el-checkbox>
                                                </div>
                                            </dt>
                                            <dd class="btn_item sortBox" >
                                                    <div class="item" v-for="(btn,ind) in memberCompFormData.setBtns.btns" :data-id="JSON.stringify(btn)" :key="ind" >
                                                        <s class="del_item" @click="delBtns('setBtns',ind)"></s>
                                                        <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                        <div class="item_con">
                                                            <div class="img_up fn-clear">
                                                                <div :class="['upbtn smShowImg',{'hasup':btn.icon}]">
                                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" :id="'rbtn_'+ind+'_filedata'" :data-type="'rbtn_' + ind">
                                                                    <img :src="btn.icon" alt="" v-if="btn.icon">
                                                                    <span>更换图片</span>
                                                                </div>
                                                                <div class="imgText">
                                                                    <h4>按钮图标</h4>
                                                                    <p>建议图标尺寸50*50px</p>
                                                                </div>
                                                            </div>
                                                            <template v-if="memberCompFormData.setBtns.btnType === 1">
                                                                <div class="inpbox">
                                                                    <el-input v-model="btn.text"  maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                                </div>
                                                                <div class="inpbox linkbox linkChose">
                                                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                                    <input type="text" placeholder="请选择按钮链接" readonly :value="solveShowtext(btn.linkInfo,btn.link)" class="iconLink" @click="changeLink('memberCompFormData.setBtns.btns.' + ind + '.link')">
                                                                </div>
                                                            </template>
                                                            <template v-else>

                                                                <div class="inpbox canDelInp" v-if="(btn.edit || btn.text) && memberCompFormData.setBtns.btnType == 2">
                                                                    <el-input maxlength="5"  v-model="btn.text" show-word-limit  placeholder="选填" class="iconName" ></el-input>
                                                                    <s class="del" @click="btn.text = '',btn.edit = 0"></s>
                                                                </div>
                                                                <div class="inpbox linkbox flexBox linkChose">
                                                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                                    <input type="text" placeholder="请选择链接" readonly :value="solveShowtext(btn.linkInfo,btn.link)" class="iconLink" @click="changeLink('memberCompFormData.setBtns.btns.' + ind + '.link')">
                                                                    <a href="javascript:;" class="edit" v-if="!btn.text && !btn.edit && memberCompFormData.setBtns.btnType" @click="btn.edit = 1"><s></s>按钮文本</a>
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                    <a href="javascript:;" class="add_more" @click="addMoreBtn()" v-show="memberCompFormData.setBtns.btns.length < 3"><s></s>添加按钮</a>
                                                
                                            </dd>
                                        </dl>


                                        <!-- 右侧 -->
                                        <dl class="setbox" v-show="cfg_business_state">
                                            <dt class="style_options ">
                                                <h5>右侧按钮</h5>
                                                <div class="split_options radioBox" v-show="memberCompFormData.theme != 3">
                                                    <span :class="{'onChose':!memberCompFormData.qiandaoBtn.show}" @click="memberCompFormData.qiandaoBtn.show = false"><s></s>不显示</span>
                                                    <span :class="{'onChose':memberCompFormData.qiandaoBtn.show}" @click="memberCompFormData.qiandaoBtn.show = true"><s></s>显示</span>
                                                </div>
                                                <span class="tip" v-show="memberCompFormData.theme == 3" >（按用户实况显示切换商家版、或员工工作台）</span>
                                            </dt>
                                            <dd class="btn_item ">
                                                <div class="option_item item" v-show="memberCompFormData.qiandaoBtn.show"  v-if="memberCompFormData.theme !== 3">
                                                    <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                    <div class="item_con">
                                                        <div class="img_up fn-clear">
                                                            <div :class="['upbtn smShowImg',{'hasup':memberCompFormData.qiandaoBtn.icon}]">
                                                                <s class="clearIcon" @click="memberCompFormData.qiandaoBtn.icon = ''"></s>
                                                                <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="qiandao_filedata" data-type="qiandao">
                                                                <img :src="memberCompFormData.qiandaoBtn.icon" alt="">
                                                                <span v-if="memberCompFormData.qiandaoBtn.icon">更换图片</span>
                                                            </div>
                                                            <div class="imgText">
                                                                <h4>按钮图标</h4>
                                                                <p>建议图标尺寸40*40px</p>
                                                            </div>
                                                        </div>
                                                        <div class="inpbox">
                                                            <el-input maxlength="5" v-model="memberCompFormData.qiandaoBtn.text" show-word-limit  placeholder="请输入按钮名称" class="iconName" ></el-input>
                                                        </div>
                                                        <div class="inpbox linkbox linkChose">
                                                            <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(memberCompFormData.qiandaoBtn.linkInfo && memberCompFormData.qiandaoBtn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                            <input type="text" placeholder="请选择链接" class="iconLink" :value="solveShowtext(memberCompFormData.qiandaoBtn.linkInfo,memberCompFormData.qiandaoBtn.link)" @click="changeLink('memberCompFormData.qiandaoBtn.link')" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                                <template v-else>
                                                    <div class="item option_item" >
                                                        <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                        <div class="item_con">
                                                            <div class="img_up fn-clear">
                                                                <div class="upbtn hasup smShowImg">
                                                                    <input type="file" name="Filedata" id="business_filedata" data-type="business" accept="png,jpg,jpeg" class="fileUp">
                                                                    <img :src="memberCompFormData.business.icon" alt="">
                                                                    <span>更换图片</span>
                                                                </div>
                                                                <div class="imgText">
                                                                    <h4>切换商家版</h4>
                                                                    <p>建议图标尺寸70*70px</p>
                                                                </div>
                                                            </div>
                                                            <div class="inpbox">
                                                                <el-input v-model="memberCompFormData.business.text" @focus="currFocus = 'business'" maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                            </div>
                                                            <div class="inpbox linkbox linkChose">
                                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(memberCompFormData.business.linkInfo && memberCompFormData.business.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                                <input type="text" placeholder="请选择按钮链接" class="iconLink disabled" readonly :value="solveShowtext(memberCompFormData.business.linkInfo,memberCompFormData.business.link)" >
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="item" v-show="memberCompFormData.branchShow">
                                                        <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                        <div class="item_con">
                                                            <s class="del_item" @click="memberCompFormData.branchShow = false"></s>
                                                            <div class="img_up fn-clear ">
                                                                <div class="upbtn hasup smShowImg">
                                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="branch_filedata" data-type="branch">
                                                                    <img :src="memberCompFormData.branch.icon" alt="">
                                                                    <span>更换图片</span>
                                                                </div>
                                                                <div class="imgText">
                                                                    <h4>员工工作台</h4>
                                                                    <p>建议图标尺寸70*70px</p>
                                                                </div>
                                                            </div>
                                                            <div class="inpbox">
                                                                <el-input v-model="memberCompFormData.branch.text" @focus="currFocus = 'branch'" maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                            </div>
                                                            <div class="inpbox linkbox linkChose">
                                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(memberCompFormData.branch.linkInfo && memberCompFormData.branch.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                                <input type="text" placeholder="请选择按钮链接"  :value="solveShowtext(memberCompFormData.branch.linkInfo,memberCompFormData.branch.link)" readonly class="iconLink disabled">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <a href="javascript:;" class="add_more" style="margin-bottom:20px;" @click="memberCompFormData.branchShow = true" v-show="!memberCompFormData.branchShow"><s></s>添加员工工作台</a>
                                                </template>


                                                <!-- 样式调整 -->
                                                <div class="style_item item">
                                                    <div class="style_options" v-show="!memberCompFormData.qiandaoBtn.show">
                                                        <label for="">箭头颜色：</label>
                                                        <div class="colorpickerBox">
                                                            <el-color-picker @active-change="(value) => activeChangeColor(value,'arr_color','qiandaoBtn')"  v-model="memberCompFormData.qiandaoBtn.style.arr_color" class="colorpicker"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false" :value="memberCompFormData.qiandaoBtn.style.arr_color ? (memberCompFormData.qiandaoBtn.style.arr_color).replace('#','') : ''"  @change="changeColor('arr_color','qiandaoBtn')" maxlength="6">
                                                            <button @click="resetColor('arr_color','qiandaoBtn')">重置</button>
                                                        </div>
                                                        <!-- <div class="opciatyBox">
                                                            <input type="text" class="opciatySet" v-model="memberCompFormData.qiandaoBtn.style.opacity">
                                                            <span  class="single">%</span>
                                                        </div> -->
                                                    </div>
                                                    <div class="style_options" v-show="memberCompFormData.qiandaoBtn.show">
                                                        <label for="">按钮颜色：</label>
                                                        <div class="colorpickerBox">
                                                            <el-color-picker @active-change="(value) => activeChangeColor(value,'background','qiandaoBtn')"  v-model="memberCompFormData.qiandaoBtn.style.background" class="colorpicker"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false" :value="(memberCompFormData.qiandaoBtn.style.background).replace('#','')"  @change="changeColor('background','qiandaoBtn')" maxlength="6">
                                                            <button @click="resetColor('background','qiandaoBtn')">重置</button>
                                                        </div>
                                                        <div class="opciatyBox">
                                                            <input type="text" class="opciatySet" v-model="memberCompFormData.qiandaoBtn.style.opacity">
                                                            <span  class="single">%</span>
                                                        </div>
                                                    </div>
                                                    <div class="style_options" data-type="text" v-show="memberCompFormData.qiandaoBtn.show">
                                                        <label for="">文本颜色：</label>
                                                        <div class="colorpickerBox">
                                                            <el-color-picker @active-change="(value) => activeChangeColor(value,'color','qiandaoBtn')"  v-model="memberCompFormData.qiandaoBtn.style.color" class="colorpicker"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false"  @change="changeColor('color','qiandaoBtn')" maxlength="6" :value="(memberCompFormData.qiandaoBtn.style.color).replace('#','')">
                                                            <button @click="resetColor('color','qiandaoBtn')">重置</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </dd>
                                        </dl>
                                        
                                    </div>
                                </el-collapse-transition>
                            </div>


                            <!-- 数字区域 -->
                            <div class="numberSetBox " v-show="memberCompFormData.theme === 1 || memberCompFormData.theme === 2">
                                <h2 :class="['stitle hasArr',{'slideup':showNumberSet}]" @click="showNumberSet = !showNumberSet">数字区域</h2>
                                <el-collapse-transition >
                                    <div class="sContent" v-show="showNumberSet">
                                        <!-- 数字选项 -->
                                        <el-checkbox-group :max="4" v-model="memberCompFormData.numberCount.showItems" class="checkbox">
                                            <el-checkbox v-for="item in numberOption" :value="item.id" :label="item.id" :key="item.id">{{item.text}}</el-checkbox>
                                        </el-checkbox-group>

                                        <!-- 样式调整 -->
                                        <div class="style_item item">
                                            <div class="style_options">
                                                <label for="">数字颜色：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker  @active-change="(value) => activeChangeColor(value,'color','numberStyle')" v-model="memberCompFormData.numberCount.numberStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('color','numberStyle')" maxlength="6"  :value="memberCompFormData.numberCount.numberStyle.color.replace('#','')">
                                                    <button @click="resetColor('color','numberStyle')">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">标题颜色：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker @active-change="(value) => activeChangeColor(value,'color','titleStyle')" v-model="memberCompFormData.numberCount.titleStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('color','titleStyle')" maxlength="6" :value="memberCompFormData.numberCount.titleStyle.color.replace('#','')">
                                                    <button  @click="resetColor('color','titleStyle')">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">背景：</label>
                                                <div :class="['colorpickerBox',{'noColor':!memberCompFormData.numberCount.style.background}]">
                                                    <el-color-picker popper-class="borderColorPicker" v-model="memberCompFormData.numberCount.style.background"  @active-change="(value) => activeChangeColor(value,'background','numberCount')"  class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('background','numberCount')" maxlength="6" :value="memberCompFormData.numberCount.style.background ? memberCompFormData.numberCount.style.background.replace('#','') : ''">
                                                    <button  @click="resetColor('background','numberCount')">重置</button>
                                                </div>
                                                <div class="opciatyBox">
                                                    <input type="text" class="opciatySet" v-model="memberCompFormData.numberCount.style.opacity">
                                                    <span  class="single">%</span>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">边框：</label>
                                                <div :class="['colorpickerBox',{'noColor':!memberCompFormData.numberCount.style.borderColor}]">
                                                    <el-color-picker @active-change="(value) => activeChangeColor(value,'borderColor','numberCount')" popper-class="borderColorPicker" v-model="memberCompFormData.numberCount.style.borderColor" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false"  @change="changeColor('borderColor','numberCount')" maxlength="6" :value="memberCompFormData.numberCount.style.borderColor ? memberCompFormData.numberCount.style.borderColor.replace('#','') : ''">
                                                    <button  @click="resetColor('borderColor','numberCount')">重置</button>
                                                </div>
                                                <div class="opciatyBox">
                                                    <input type="text" class="opciatySet" v-model="memberCompFormData.numberCount.style.borderSize">
                                                    <span  class="single">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">分隔线：</label>
                                                <div class="split_options radioBox">
                                                    <span :class="{'onChose':!memberCompFormData.numberCount.splitLine}" @click="memberCompFormData.numberCount.splitLine = false"><s></s>不显示</span>
                                                    <span :class="{'onChose':memberCompFormData.numberCount.splitLine}" @click="memberCompFormData.numberCount.splitLine = true"><s></s>显示</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>


                            <!-- 会员卡片 -->
                            <div class="vipCardSetBox">
                                <div class="title_flex"><h2 :class="['stitle hasArr',{'slideup':showVipSet}]" @click="showVipSet = !showVipSet">会员卡片</h2><el-checkbox label="隐藏卡片"  v-model="memberCompFormData.vipCard.cardHide"></el-checkbox></div>
                                
                                <el-collapse-transition v-if="!memberCompFormData.vipCard.cardHide">
                                    <div class="sContent" v-show="showVipSet">
                                        
                                        <div class="style  fn-clear">
                                            <div class="column_chose">
                                                <s :style="'left:'+offsetLeft+'px;'"></s>
                                                <span @click="changeVipStyle(1)" >样式1</span>
                                                <span @click="changeVipStyle(2)">样式2</span>
                                                <span @click="changeVipStyle(3)">样式3</span>
                                            </div>
                                            <div class="color_picker" data-type="2">
                                                <div class="color bgimg" v-show="memberCompFormData.vipCard.style.bgType == 'image' || memberCompFormData.vipCard.style.background.indexOf('linear-gradient') > -1">
                                                    <div class="imgShow" v-if="memberCompFormData.vipCard.style.bgType == 'image'"><img :src="memberCompFormData.vipCard.style.backimg" alt=""></div>
                                                    <div class="imgShow" v-else :style="'background:'+ memberCompFormData.vipCard.style.background +';'"></div>
                                                </div>
                                                <div class="color" id="colorPicker2" ></div>
                                            </div>
                                        </div>
                                        <div class="style_item setbox">
                                            <div class="style_options" >
                                                <label for="">卡片标题：</label>
                                                <el-input type="text" placeholder="请输入内容" v-model="memberCompFormData.vipCard.title" maxlength="8" show-word-limit>
                                            </div>
                                            <div class="style_options subtitle" >
                                                <label for="" style="align-self: flex-start; padding-top:10px;">副标题：</label>
                                                <div class="flexBox">
                                                    <ul class="textCon" v-show="memberCompFormData.vipCard.subtitle.length">
                                                        <li :class="[{'vipInfo':item.type === 1},{'textInfo':item.type === 2}]" v-for="(item,ind) in memberCompFormData.vipCard.subtitle">
                                                            <div class="vipInfoBox" v-if="item.type === 1">
                                                                
                                                                <el-popover
                                                                    popper-class="popbox"
                                                                    width="408"
                                                                    trigger="hover">
                                                                    <div class="popCon">
                                                                        <ul>
                                                                            <li style="color:#A2A6AB;"><span>未开通会员：</span>不显示</li>
                                                                            <li><span>已开通会员：</span>权益到期：20xx/xx/xx
                                                                            </li>
                                                                            <li><span>会员已过期：</span>x类型会员已过期xx天 </li>
                                                                        </ul>
                                                                    </div>
                                                                    <span slot="reference">{{item.text}}<s ></s></span>
                                                                </el-popover>
                                                            </div>
                                                            <div :class="['inpbox', {'canDelInp': memberCompFormData.vipCard.subtitle.length > 1}] "  v-else>
                                                                <el-input type="text" placeholder="请输入内容" v-model="item.text" maxlength="20" show-word-limit>
                                                            </div>
                                                            <a href="javascript:;" class="delBtn" @click="delVipObj(ind)" v-show="item.type === 1 || memberCompFormData.vipCard.subtitle.length > 1" ></a>
                                                        </li>
                                                    </ul>
                                                    <a href="javascript:;" class="btn_add" v-if=" memberCompFormData.vipCard.subtitle.length <= 1" @click="addVipSub">
                                                        <span>滚动文本</span>
                                                        <div class="smPop" v-if="!checkHasChose()">
                                                            <ul class="subtit_options">
                                                                <li v-show="!checkHasChose()" @click.stop="addSubtitle(1)"><s></s>会员有效期</li>
                                                                <li class="text"  @click.stop="addSubtitle(2)"><s></s>普通文本</li>
                                                            </ul>
                                                        </div>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">标题：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker @active-change="(value) => activeChangeColor(value,'color','viptitleStyle')" v-model="memberCompFormData.vipCard.titleStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false"  @change="changeColor('color','viptitleStyle')" maxlength="6"  :value="memberCompFormData.vipCard.titleStyle.color.replace('#','')">
                                                    <button @click="memberCompFormData.vipCard.titleStyle.color = memberCompFormData.vipCard.titleStyle.initColor">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">副标题：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker @active-change="(value) => activeChangeColor(value,'color','vipsubStyle')" v-model="memberCompFormData.vipCard.subStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('color','vipsubStyle')" maxlength="6"  :value="memberCompFormData.vipCard.subStyle.color.replace('#','')">
                                                    <button @click="memberCompFormData.vipCard.subStyle.color = memberCompFormData.vipCard.subStyle.initColor">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for=""
                                                    style="align-self:flex-start; padding-top:6px;">会员图标：</label>
                                                <div class="img_up fn-clear">
                                                    <div class="upbtn hasup  vip_icon">
                                                        <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="vipIcon_filedata" data-type="vipIcon">
                                                        <img :src="memberCompFormData.vipCard.icon" alt="" v-if="memberCompFormData.vipCard.icon">
                                                        <span>更换图片</span>
                                                    </div>
                                                    <!-- <div class="imgText">
                                                        <p>官方图标库</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                            <el-divider></el-divider>
                                            <div class="style_options" >
                                                <label for="">按钮文本：</label>
                                                <el-input type="text" placeholder="请输入内容" v-model="memberCompFormData.vipCard.btnText" maxlength="5" show-word-limit>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">按钮样式：</label>
                                                <div class="split_options radioBox">
                                                    <span  @click="memberCompFormData.vipCard.btnStyle.styleType = 'radius'" :class="{'onChose':memberCompFormData.vipCard.btnStyle.styleType == 'radius'}"><s></s>圆角按钮</span>
                                                    <span @click="memberCompFormData.vipCard.btnStyle.styleType = 'arrow'" :class="{'onChose':memberCompFormData.vipCard.btnStyle.styleType == 'arrow'}"><s></s>箭头指引</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">按钮颜色：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker @active-change="(value) => activeChangeColor(value,'background','vipbtnStyle')" v-model="memberCompFormData.vipCard.btnStyle.style.background" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false"  @change="changeColor('background','vipbtnStyle')" maxlength="6"  :value="memberCompFormData.vipCard.btnStyle.style.background.replace('#','')">
                                                    <button @click="memberCompFormData.vipCard.btnStyle.style.background = memberCompFormData.vipCard.btnStyle.style.initBackground">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" v-show="memberCompFormData.vipCard.btnStyle.styleType == 'radius'">
                                                <label for="">文本颜色：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker  @active-change="(value) => activeChangeColor(value,'color','vipbtnStyle')" v-model="memberCompFormData.vipCard.btnStyle.style.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('color','vipbtnStyle')" maxlength="6"  :value="memberCompFormData.vipCard.btnStyle.style.color.replace('#','')">
                                                    <button @click="memberCompFormData.vipCard.btnStyle.style.color = memberCompFormData.vipCard.btnStyle.style.initColor">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" v-show="memberCompFormData.vipCard.btnStyle.styleType == 'radius'">
                                                <label for="">按钮圆角：</label>
                                                <div class="colorShowBox">
                                                    <input type="text" class="colorShow" spellcheck="false" v-model="memberCompFormData.vipCard.btnStyle.style.borderRadius">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>

                            <!-- 财务区域 -->
                            <div class="financeInfoSetBox" v-show="memberCompFormData.vipCard.theme === 3">
                                <h2 :class="['stitle hasArr',{'slideup':financeInfoSet}]" @click="financeInfoSet = !financeInfoSet">财务区域</h2>
                                <el-collapse-transition>
                                    <div class="sContent" v-show="financeInfoSet">
                                        <!-- 数字选项 -->
                                        <el-checkbox-group :max="3" v-model="memberCompFormData.financeCount.showItems" class="checkbox">
                                            <el-checkbox v-for="item in financeOption" :value="item.id" :label="item.id" :key="item.id">{{item.text}}</el-checkbox>
                                        </el-checkbox-group>

                                        <!-- 样式调整 -->
                                        <div class="style_item item">
                                            <div class="style_options">
                                                <label for="">数字颜色：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker  @active-change="(value) => activeChangeColor(value,'color','finance_number')" v-model="memberCompFormData.financeCount.numberStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" :value="memberCompFormData.financeCount.numberStyle.color.replace('#','')">
                                                    <button @click="resetColor('color','finance_number')">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">数字类型：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker  @active-change="(value) => activeChangeColor(value,'color','finance_title')" v-model="memberCompFormData.financeCount.titleStyle.color" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false"  @change="changeColor('color','finance_title')" maxlength="6"   :value="memberCompFormData.financeCount.titleStyle.color.replace('#','')">
                                                    <button  @click="resetColor('color','finance_title')">重置</button>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">背景：</label>
                                                <div class="colorpickerBox">
                                                    <el-color-picker  @active-change="(value) => activeChangeColor(value,'background','finance_bg')" v-model="memberCompFormData.financeCount.style.background" class="colorpicker"></el-color-picker>
                                                </div>
                                                <div class="colorShowBox">
                                                    <span class="single">#</span>
                                                    <input type="text" class="colorShow" spellcheck="false" @change="changeColor('background','finance_bg')" maxlength="6"    :value="memberCompFormData.financeCount.style.background.replace('#','')">
                                                    <button  @click="resetColor('background','finance_bg')">重置</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>


                            <!-- 按钮组 -->
                            <div class="btnsGroupSetBox contentSetBox " v-show="memberCompFormData.theme !== 1 && memberCompFormData.theme !== 2">
                                <h2 :class="['stitle hasArr',{'slideup':btnsGroupSet}]" @click="btnsGroupSet = !btnsGroupSet">按钮组</h2>
                                <el-collapse-transition>

                                    <div class="scontent " v-show="btnsGroupSet">
                                        <div class="btn_item sortBox1">
                                            <div class="item" v-for="(btn,ind) in memberCompFormData.vipBtnsGroup"  :data-id="JSON.stringify(btn)">
                                                <s class="del_item" @click="delBtns('vipBtnsGroup',ind)"></s>
                                                <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                <div class="item_con" >
                                                    <div class="img_up fn-clear">
                                                        <div :class="['upbtn ',{'hasup':btn.icon}]">
                                                            <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" :id="'btns_'+ind+'_filedata'" :data-type="'btns_' + ind">
                                                            <img :src="btn.icon" alt="" v-if="btn.icon">
                                                            <span>更换图片</span>
                                                        </div>
                                                        <div class="imgText">
                                                            <h4>按钮图标</h4>
                                                            <p>建议图标尺寸50*50px</p>
                                                        </div>
                                                    </div>
                                                    <div class="inpbox">
                                                        <el-input v-model="btn.text"  maxlength="5" show-word-limit  placeholder="请输入按钮名称" class="iconName"></el-input>
                                                    </div>
                                                    <div class="inpbox linkbox linkChose">
                                                        <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                        <input type="text" placeholder="请选择按钮链接"  :value="solveShowtext(btn.linkInfo,btn.link)" readonly @click="changeLink('memberCompFormData.vipBtnsGroup.' +ind+'.link' )" class="iconLink">
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="javascript:;" class="add_more" @click="addMoreBtn('vipBtnsGroup')" v-show="memberCompFormData.vipBtnsGroup.length < 5"><s></s>添加按钮</a>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>


                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="memberCompFormData.cardStyle.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="memberCompFormData.cardStyle.borderRadius = (memberCompFormData.cardStyle.borderRadius ? memberCompFormData.cardStyle.borderRadius : 0)" v-model="memberCompFormData.cardStyle.borderRadius" @input="checkMax(50,'borderRadius')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">卡片间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="memberCompFormData.cardStyle.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number"  class="colorShow" @change="memberCompFormData.cardStyle.marginTop = (memberCompFormData.cardStyle.marginTop ? memberCompFormData.cardStyle.marginTop : 0)"  v-model="memberCompFormData.cardStyle.marginTop" @input="checkMax(40,'marginTop')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="memberCompFormData.cardStyle.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="memberCompFormData.cardStyle.marginLeft = (memberCompFormData.cardStyle.marginLeft ? memberCompFormData.cardStyle.marginLeft : 0)"  v-model="memberCompFormData.cardStyle.marginLeft"  @input="checkMax(40,'marginLeft')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单管理 -->
                <div :class="['rightCon',{'show':currEditPart === 2}] " v-cloak>
                    <div class="orderStyleContainer modBox">
                        <div class="title">订单管理 
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 2 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('order',2)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('order')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 2 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('order',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 2 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('order',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="orderStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                                <div class="titleSetBox">
                                    <div class="titleSet">
                                        <el-checkbox label="显示标题" v-model="orderFormData.title.show" ></el-checkbox>
                                        <div class="moreBtnSet">
                                            <span>查看更多：</span>
                                            <div class="radioBox">
                                                <span :class="[{'on_chose':orderFormData.more.showType == ind},{'no_chose':!orderFormData.title.show && ind == 0}]" @click="choseShowType(ind)" v-for="(item,ind) in showTypeOpt"><s></s>{{item}}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <el-input class="bgColorInp" v-show="orderFormData.title.show" show-word-limit maxlength="10" placeholder="请输入标题" v-model="orderFormData.title.text"></el-input>
                                </div>
                                
                            </div>
                            <div class="modstyleBox contentSetBox" >
                                <h2 :class="['stitle hasArr',{'slideup':showContentSet}]"  @click="showContentSet = !showContentSet">内容设置</h2>
                                <el-collapse-transition>
                                    <div class="sContent" v-show="showContentSet">
                                        <!-- 数字选项 -->
                                        <el-checkbox-group :max="5" v-model="orderFormData.showItems" class="checkbox">
                                            <el-checkbox v-for="item in orderOption" :value="item.id" v-show="item.id !== 8" :label="item.id" :key="item.id">{{item.text}}</el-checkbox>
                                        </el-checkbox-group>
                                        <div  class="btnsBox btn_item orderSort">
                                            <div class="option_item item" :data-id="btn.id"  v-for="(btn,ind) in checkArr(orderFormData.showItems,'orderFormData')">
                                                <s class="del_item" @click="delBtns('orderFormData',btn.id)"></s>
                                                <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                <div class="item_con">
                                                    <div class="img_up fn-clear">
                                                        <div class="upbtn hasup">
                                                            <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" :id="'order_'+ind+'_filedata'" :data-type="'order_'+ind">
                                                            <img :src="btn.icon" alt="">
                                                            <span>更换图片</span>
                                                        </div>
                                                        <div class="imgText">
                                                            <h4>{{btn.text}}</h4>
                                                            <p>建议图标尺寸40*40px</p>
                                                        </div>
                                                    </div>
                                                    <div class="inpbox">
                                                        <el-input maxlength="5" v-model="btn.btnText" show-word-limit  placeholder="请输入按钮名称" class="iconName" ></el-input>
                                                    </div>
                                                    <div class="inpbox linkbox linkChose">
                                                        <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                        <input type="text" placeholder="请选择链接" class="iconLink" :value="solveShowtext(btn.linkInfo,btn.link)" readonly @click="changeLink('memberCompFormData.orderFormData.' + checkOrderOptionInd(btn.id) +'.link' )" >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                            <div class="modstyleBox contentSetBox" v-show="orderFormData.more.showType === 0">
                                <h2 class="stitle">查看更多</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">显示文本：</label>
                                            <div class="textSetBox">
                                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="orderFormData.more.text"></el-input>
                                            </div>
                                            <span :class="['showArr',{'noColor':!orderFormData.more.arr}] " @click="orderFormData.more.arr = !orderFormData.more.arr">

                                            </span>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">链接：</label>
                                            <div class="inpbox linkbox linkChose">
                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(orderFormData.more.linkInfo && orderFormData.more.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                <input type="text" placeholder="请选择链接" class="iconLink" :value="solveShowtext(orderFormData.more.linkInfo,orderFormData.more.link)" readonly @click="changeLink('orderFormData.more.link')">
                                            </div>
                                        </div>

                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'more','orderFormData')" class="colorpicker" v-model="orderFormData.more.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(orderFormData.more.style.color).replace('#','')" @change="changeColor('more','orderFormData')" maxlength="6">
                                                <button @click="resetColor('more','orderFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">组件样式</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" v-show="orderFormData.title.show" >
                                            <label for="">标题颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  class="colorpicker"  @active-change="(value) => activeChangeColor(value,'title','orderFormData')" v-model="orderFormData.title.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(orderFormData.title.style.color).replace('#','')"  @change="changeColor('title','orderFormData')" maxlength="6">
                                                <button @click="resetColor('title','orderFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">按钮文本：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker   @active-change="(value) => activeChangeColor(value,'btnStyle','orderFormData')" class="colorpicker" v-model="orderFormData.btnStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(orderFormData.btnStyle.color).replace('#','')"  @change="changeColor('btnStyle','orderFormData')" maxlength="6">
                                                <button @click="resetColor('btnStyle','orderFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">按钮字号：</label>
                                            <div class="split_options radioBox">
                                                <span :class="{'onChose':orderFormData.btnStyle.fontSize !== 26}" @click="orderFormData.btnStyle.fontSize = 24"><s></s>默认</span>
                                                <span :class="{'onChose':orderFormData.btnStyle.fontSize == 26}" @click="orderFormData.btnStyle.fontSize = 26"><s></s>加大</span>
                                            </div>
                                        </div>
                                        <div class="style_options" v-show="checkArr(orderFormData.showItems,'orderFormData').length > 0">
                                            <label for="" style="align-self:flex-start; margin-top:10px;">数量角标：</label>
                                            <!-- 数字选项 -->
                                            <el-checkbox-group :max="5" v-model="orderFormData.showNumItems" class="checkbox">
                                                <el-checkbox v-for="(item,ind) in checkArr(orderFormData.showItems,'orderFormData')" v-show="item.id != 8"  :value="item.id" :label="item.id" :key="item.id">{{item.text}}</el-checkbox>
                                            </el-checkbox-group>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">角标颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'tipNumStyle','orderFormData')" class="colorpicker" v-model="orderFormData.tipNumStyle.background"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(orderFormData.tipNumStyle.background).replace('#','')"  @change="changeColor('tipNumStyle','orderFormData')" maxlength="6">
                                                <button @click="resetColor('tipNumStyle','orderFormData')">重置</button>
                                            </div>
                                        </div>


                                        
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="orderFormData.style.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="orderFormData.style.borderRadius = (orderFormData.style.borderRadius ? orderFormData.style.borderRadius : 0)" v-model="orderFormData.style.borderRadius" @input="checkMax(50,'borderRadius','orderFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="orderFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="orderFormData.style.marginTop = (orderFormData.style.marginTop ? orderFormData.style.marginTop : 0)" v-model="orderFormData.style.marginTop" @input="checkMax(40,'marginTop','orderFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="orderFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="orderFormData.style.marginLeft = (orderFormData.style.marginLeft ? orderFormData.style.marginLeft : 0)" v-model="orderFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','orderFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务卡片 -->
                <div :class="['rightCon',{'show':currEditPart === 3}] " v-cloak>
                    <div class="financeContainer modBox">
                        <div class="title">财务卡片
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 3 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('finance',3)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('finance')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 3 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end" >
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('finance',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 3 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('finance',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="financeStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                                <div class="titleSetBox">
                                    <div class="titleSet">
                                        <el-checkbox label="显示标题" @change="financeFormData.more.show = (!financeFormData.title.show ? false : financeFormData.more.show)"  v-model="financeFormData.title.show"></el-checkbox>
                                        <el-checkbox label="显示查看更多" v-model="financeFormData.more.show" :disabled="!financeFormData.title.show"></el-checkbox>
                                        <div class="color_picker"  data-type="3">
                                            <div class="color bgimg" v-show="financeFormData.bgType == 'image' || financeFormData.style.bg_color.indexOf('linear-gradient') > -1">
                                                <div class="imgShow" v-if="financeFormData.bgType == 'image'"><img :src="financeFormData.style.bg_image" alt=""></div>
                                                <div class="imgShow" v-else :style="'background:'+ financeFormData.style.bg_color +';'"></div>
                                            </div>
                                            <div class="color" id="colorPicker3"></div>
                                        </div>
                                    </div>
                                    <el-input class="bgColorInp" v-show="financeFormData.title.show"  show-word-limit maxlength="10" placeholder="请输入标题" v-model="financeFormData.title.text"></el-input>
                                </div>
                                
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">内容设置</h2>
                                <div class="sContent">
                                    <!-- 数字选项 -->
                                    <el-checkbox-group :max="5" v-model="financeFormData.showItems" class="checkbox">
                                        <el-checkbox v-for="item in financeOption" :value="item.id" :label="item.id" :key="item.id">{{item.text}}</el-checkbox>
                                    </el-checkbox-group>
                                    <div class="item style_item ">
                                        <div class="style_options" v-show="financeFormData.title.show" >
                                            <label for="">标题：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'title','financeFormData')" class="colorpicker" v-model="financeFormData.title.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(financeFormData.title.style.color).replace('#','')"  @change="changeColor('tipNumStyle','orderFormData')" maxlength="6">
                                                <button @click="resetColor('title','financeFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">数字：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'numberStyle','financeFormData')" class="colorpicker" v-model="financeFormData.numberStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="financeFormData.numberStyle.color.replace('#','')"  @change="changeColor('numberStyle','financeFormData')" maxlength="6">
                                                <button @click="resetColor('numberStyle','financeFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">财务类型：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'textStyle','financeFormData')" class="colorpicker" v-model="financeFormData.textStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(financeFormData.textStyle.color).replace('#','')"  @change="changeColor('textStyle','orderFormData')" maxlength="6">
                                                <button @click="resetColor('textStyle','financeFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">类型字号：</label>
                                            <div class="split_options radioBox">
                                                <span :class="{'onChose':financeFormData.textStyle.fontSize !== 26}" @click="financeFormData.textStyle.fontSize = 24"><s></s>默认</span>
                                                <span :class="{'onChose':financeFormData.textStyle.fontSize == 26}" @click="financeFormData.textStyle.fontSize = 26"><s></s>加大</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modstyleBox contentSetBox" v-show="financeFormData.more.show">
                                <h2 class="stitle">查看更多</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">显示文本：</label>
                                            <div class="textSetBox">
                                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="financeFormData.more.text"></el-input>
                                            </div>
                                            <span :class="['showArr',{'noColor':!financeFormData.more.arr}] " @click="financeFormData.more.arr = !financeFormData.more.arr">

                                            </span>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">链接：</label>
                                            <div class="inpbox linkbox linkChose">
                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(financeFormData.more.linkInfo && financeFormData.more.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="solveShowtext(financeFormData.more.linkInfo,financeFormData.more.link)" @click="changeLink('financeFormData.more.link')">
                                            </div>
                                        </div>

                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'more','financeFormData')"  class="colorpicker" v-model="financeFormData.more.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(financeFormData.more.style.color).replace('#','')" @change="changeColor('more','financeFormData')" maxlength="6">
                                                <button @click="resetColor('more','financeFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="financeFormData.style.borderRadius" :min="0" :max="50" ></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow"  @change="financeFormData.style.borderRadius = (financeFormData.style.borderRadius ? financeFormData.style.borderRadius : 0)" v-model="financeFormData.style.borderRadius" @input="checkMax(50,'borderRadius','financeFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="financeFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="financeFormData.style.marginTop = (financeFormData.style.marginTop ? financeFormData.style.marginTop : 0)" v-model="financeFormData.style.marginTop" @input="checkMax(40,'marginTop','financeFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="financeFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow"  @change="financeFormData.style.marginLeft = (financeFormData.style.marginLeft ? financeFormData.style.marginLeft : 0)" v-model="financeFormData.style.marginLeft"  @input="checkMax(40,'marginLeft',financeFormData)">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 图标组 -->
                <div :class="['rightCon',{'show':currEditPart === 4}] " v-cloak>
                    <div class="financeContainer iconsContainer modBox">
                        <div class="title">图标组
                            
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 4 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('icons',4)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('icons')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 4 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('icons',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 4 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('icons',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="financeStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                               
                                <div class="titleSetBox">
                                    <div class="titleSet">
                                        <el-checkbox label="显示标题" v-model="iconsFormData.title.show" @change="iconsFormData.more.show = (!iconsFormData.title.show ? false : iconsFormData.more.show);"></el-checkbox >
                                        <el-checkbox label="显示查看更多" v-model="iconsFormData.more.show" :disabled="!iconsFormData.title.show"></el-checkbox>
                                        <!-- <el-checkbox label="签到组件" @change="setQiandao" v-show="!qiandaoSet || iconsFormData.qiandao.show " v-model="iconsFormData.qiandao.show"></el-checkbox> -->
                                        <div class="qdbox" >
                                            <el-checkbox label="签到组件" @change="setQiandao"  v-model="iconsFormData.qiandao.show" ></el-checkbox>
                                            <!-- :disabled="qiandaoSet && !iconsFormData.qiandao.show" -->
                                        </div>
                                        
                                    </div>
                                    <el-input class="bgColorInp" v-show="iconsFormData.title.show" show-word-limit maxlength="10" placeholder="请输入标题" v-model="iconsFormData.title.text"></el-input>
                                </div>
                                <div class="column_chose" style="margin-top: 16px;">
                                    <s :style="'left:'+ offsetLeft +'px;'"></s>
                                    <span data-col="3" :class="{'on_chose':iconsFormData.column == 3}" @click="choseColumn(3,0)">每行3个</span>
                                    <span data-col="4" :class="{'on_chose':iconsFormData.column == 4}" @click="choseColumn(4,1)">每行4个</span>
                                    <span data-col="5" :class="{'on_chose':iconsFormData.column == 5}" @click="choseColumn(5,2)">每行5个</span>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 :class="['stitle hasArr',{'slideup':showContentSet}]" @click="showContentSet = !showContentSet">内容设置</h2>
                                <el-collapse-transition>
                                    <div class="sContent" v-show="showContentSet">
                                        <div  class="btnsBox btn_item btnsSort" v-if="iconsFormData.btns && iconsFormData.btns.list">
                                            <div class="option_item item" :data-id="JSON.stringify(btn)" v-for="(btn,ind) in iconsFormData.btns.list">
                                                <s class="del_item" @click="iconsFormData.btns.list.splice(ind,1)"></s>
                                                <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                <div class="item_con">
                                                    <div class="img_up fn-clear">
                                                        <div :class="['upbtn',{'hasup':btn.icon}]">
                                                            <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp"  :id="'iconsBtn_'+ind+'_filedata'" :data-type="'iconsBtn_'+ind" >
                                                            <img  :src="btn.icon" alt="">
                                                            <span>更换图片</span>
                                                        </div>
                                                        <div class="imgText">
                                                            <h4>按钮图标</h4>
                                                            <p>建议图标尺寸100*100px</p>
                                                        </div>
                                                    </div>
                                                    <div class="inpbox">
                                                        <el-input maxlength="5" v-model="btn.text"  show-word-limit  placeholder="请输入按钮名称" class="iconName" ></el-input>
                                                    </div>
                                                    <div class="inpbox linkbox linkChose">
                                                        <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                        <input type="text" placeholder="请选择链接" class="iconLink"  :value="solveShowtext(btn.linkInfo,btn.link)" readonly @click="changeLink('iconsFormData.btns.list.' + ind +'.link' )" >
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="javascript:;" class="add_more" @click="addMoreBtn('icons')" v-show="memberCompFormData.setBtns.btns.length < 3"><s></s>添加按钮</a>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">组件样式</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" v-show="iconsFormData.title.show">
                                            <label for="">标题颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'title','iconsFormData')" class="colorpicker" v-model="iconsFormData.title.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(iconsFormData.title.style.color).replace('#','')"  @change="changeColor('title','iconsFormData')" maxlength="6">
                                                <button @click="resetColor('title','iconsFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">按钮文本：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'btns','iconsFormData')" class="colorpicker" v-model="iconsFormData.btns.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="iconsFormData.btns.style.color.replace('#','')"  @change="changeColor('btns','iconsFormData')" maxlength="6">
                                                <button @click="resetColor('btns','iconsFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">按钮字号：</label>
                                            <div class="split_options radioBox">
                                                <span :class="{'onChose':iconsFormData.btns.style.fontSize !== 26}" @click="iconsFormData.btns.style.fontSize = 24"><s></s>默认</span>
                                                <span :class="{'onChose':iconsFormData.btns.style.fontSize == 26}" @click="iconsFormData.btns.style.fontSize = 26"><s></s>加大</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">签到设置</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="" style="align-self:flex-start; padding-top:6px;">签到图标：</label>
                                            <div class="img_up fn-clear">
                                                <div :class="['upbtn vip_icon',{'hasup':iconsFormData.qiandao.icon}]">
                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="qiandao_iconsFormData_filedata" data-type="qiandao_iconsFormData">
                                                    <img :src="iconsFormData.qiandao.icon" alt="" v-if="iconsFormData.qiandao.icon">
                                                    <span v-show="iconsFormData.qiandao.icon">更换图片</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'qiandao','iconsFormData')"  class="colorpicker" v-model="iconsFormData.qiandao.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(iconsFormData.qiandao.style.color).replace('#','')"  @change="changeColor('qiandao','iconsFormData')" maxlength="6">
                                                <button @click="resetColor('qiandao','iconsFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">背景颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'qiandao_bg','iconsFormData')"  class="colorpicker" v-model="iconsFormData.qiandao.style.background"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="iconsFormData.qiandao.style.background.replace('#','')"  @change="changeColor('qiandao_bg','iconsFormData')" maxlength="6">
                                                <button @click="resetColor('qiandao_bg','iconsFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modstyleBox contentSetBox" v-show="iconsFormData.more.show">
                                <h2 class="stitle">查看更多</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">显示文本：</label>
                                            <div class="textSetBox">
                                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="iconsFormData.more.text"></el-input>
                                            </div>
                                            <span :class="['showArr',{'noColor':!iconsFormData.more.arr}] " @click="iconsFormData.more.arr = !iconsFormData.more.arr">

                                            </span>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">链接：</label>
                                            <div class="inpbox linkbox linkChose">
                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(iconsFormData.more.linkInfo && iconsFormData.more.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="solveShowtext(iconsFormData.more.linkInfo,iconsFormData.more.link)" @click="changeLink('iconsFormData.more.link')">
                                            </div>
                                        </div>

                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  class="colorpicker"  @active-change="(value) => activeChangeColor(value,'more','iconsFormData')" v-model="iconsFormData.more.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(iconsFormData.more.style.color).replace('#','')" @change="changeColor('more','iconsFormData')" maxlength="6">
                                                <button @click="resetColor('more','iconsFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="iconsFormData.style.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow"  @change="iconsFormData.style.borderRadius = (iconsFormData.style.borderRadius ? iconsFormData.style.borderRadius : 0)" v-model="iconsFormData.style.borderRadius" @input="checkMax(50,'borderRadius','iconsFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="iconsFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="iconsFormData.style.marginTop = (iconsFormData.style.marginTop ? iconsFormData.style.marginTop : 0)" v-model="iconsFormData.style.marginTop" @input="checkMax(40,'marginTop','iconsFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="iconsFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" v-model="iconsFormData.style.marginLeft" @change="iconsFormData.style.marginLeft = (iconsFormData.style.marginLeft ? iconsFormData.style.marginLeft : 0)"  @input="checkMax(40,'marginLeft','iconsFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 列表导航 -->
                <div :class="['rightCon',{'show':currEditPart === 5}] " v-cloak>
                    <div class="financeContainer iconsContainer modBox">
                        <div class="title">列表导航
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 5 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('list',5)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('list')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 5 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end" >
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('list',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 5 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('list',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="financeStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                                <div class="titleSetBox">
                                    <div class="titleSet">
                                        <el-checkbox label="显示图标" v-model="listFormData.iconShow"></el-checkbox>
                                        <el-checkbox label="显示分隔线" v-model="listFormData.splitLine"></el-checkbox>
                                        <!-- <el-checkbox label="签到组件" @change="setQiandao" v-show="!qiandaoSet || listFormData.qiandao.show " v-model="listFormData.qiandao.show"></el-checkbox> -->
                                        <div class="qdbox">
                                            <el-checkbox label="签到组件" @change="setQiandao" v-model="listFormData.qiandao.show"></el-checkbox>
                                            <!-- :disabled="qiandaoSet && !listFormData.qiandao.show "  -->
                                        </div> 
                                    </div>
                                </div>
                                
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 :class="['stitle hasArr',{'slideup':showContentSet}]" @click="showContentSet = !showContentSet">内容设置</h2>
                                <el-collapse-transition>

                                    <div class="sContent" v-show="showContentSet">
                                        <div  class="btnsBox btn_item listSort" v-if="listFormData && listFormData.list">
                                            <div class="option_item item" :data-id="JSON.stringify(btn)" v-for="(btn,ind) in listFormData.list">
                                                <s class="del_item" @click="listFormData.list.splice(ind,1)"></s>
                                                <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                <div class="item_con">
                                                    <div class="img_up fn-clear" v-show="listFormData.iconShow">
                                                        <div :class="['upbtn',{'hasup':btn.icon}]">
                                                            <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp"  :id="'list_'+ind+'_filedata'" :data-type="'list_'+ind" >
                                                            <img v-show="btn.icon" :src="btn.icon" alt="">
                                                            <span>更换图片</span>
                                                        </div>
                                                        <div class="imgText">
                                                            <h4>列表图标</h4>
                                                            <p>建议图标尺寸70*70px</p>
                                                        </div>
                                                    </div>
                                                    <div class="flexItem">
                                                        <div class="inpbox">
                                                            <el-input maxlength="30" v-model="btn.text"   placeholder="请输入列表文字" class="iconName" ></el-input>
                                                        </div>
                                                        <a href="javascript:;" v-if="!btn.tip.show" class="btn_add" @click="btn.tip.show = true">说明文本</a>
                                                    </div>
                                                    <div class="inpbox canDelInp" v-if="btn.tip.show || btn.tip.text">
                                                        <el-input maxlength="10" v-model="btn.tip.text"  show-word-limit  placeholder="说明（选填）" class="iconName" ></el-input>
                                                        <s class="del" @click="btn.text = ''; btn.tip.show = false"></s>
                                                    </div>
                                                    <div class="inpbox linkbox linkChose">
                                                        <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                        <input type="text" placeholder="请选择链接" readonly class="iconLink"  :value="solveShowtext(btn.linkInfo,btn.link)" @click="changeLink('listFormData.list.' + ind + '.link')">
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="javascript:;" class="add_more" @click="addMoreBtn('list')"><s></s>添加按钮</a>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">组件样式</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">标题颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'title','listFormData')" class="colorpicker" v-model="listFormData.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(listFormData.style.color).replace('#','')"  @change="changeColor('title','listFormData')" maxlength="6">
                                                <button @click="resetColor('title','listFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">说明文本：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'tipStyle','listFormData')" class="colorpicker" v-model="listFormData.tipStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="listFormData.tipStyle.color.replace('#','')"  @change="changeColor('tipStyle','listFormData')" maxlength="6">
                                                <button @click="resetColor('tipStyle','listFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox" v-show="listFormData.qiandao.show">
                                <h2 class="stitle">签到设置</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="" style="align-self:flex-start; padding-top:6px;">签到图标：</label>
                                            <div class="img_up fn-clear">
                                                <div :class="['upbtn vip_icon',{'hasup':listFormData.qiandao.icon}]">
                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="qiandao_listFormData_filedata" data-type="qiandao_listFormData">
                                                    <img :src="listFormData.qiandao.icon" alt="" v-if="listFormData.qiandao.icon">
                                                    <span v-show="listFormData.qiandao.icon">更换图片</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'qiandao','listFormData')" class="colorpicker" v-model="listFormData.qiandao.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(listFormData.qiandao.style.color).replace('#','')"  @change="changeColor('qiandao','listFormData')" maxlength="6">
                                                <button @click="resetColor('qiandao','listFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">背景颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'qiandao_bg','listFormData')" class="colorpicker" v-model="listFormData.qiandao.style.background"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="listFormData.qiandao.style.background.replace('#','')"  @change="changeColor('qiandao_bg','listFormData')" maxlength="6">
                                                <button @click="resetColor('qiandao_bg','listFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="listFormData.style.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="listFormData.style.borderRadius = (listFormData.style.borderRadius ? listFormData.style.borderRadius : 0)" v-model="listFormData.style.borderRadius" @input="checkMax(50,'borderRadius','listFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">单行行高：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="listFormData.style.lineHeight" :min="80" :max="120"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow"  @change="listFormData.style.lineHeight = (listFormData.style.lineHeight ? listFormData.style.lineHeight : 0)" v-model="listFormData.style.lineHeight" @input="checkMax(120,'lineHeight','listFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="listFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="listFormData.style.marginTop = (listFormData.style.marginTop ? listFormData.style.marginTop : 0)" v-model="listFormData.style.marginTop" @input="checkMax(40,'marginTop','listFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="listFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="listFormData.style.marginLeft = (listFormData.style.marginLeft ? listFormData.style.marginLeft : 0)" v-model="listFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','listFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关注公众号 -->
                <div :class="['rightCon',{'show':currEditPart === 6}] " v-cloak>
                    <div class="financeContainer  modBox">
                        <div class="title">关注公众号
                            
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('wechat',6)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('wechat')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('wechat',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('wechat',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="financeStyle" v-if="wechatFormData && wechatFormData.titleStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                                <div class="column_chose">
                                    <s :class="{'custom':wechatFormData.custom}"></s>
                                    <span :class="{'on_chose':!wechatFormData.custom}" @click="wechatFormData.custom = 0">默认样式</span>
                                    <span :class="{'on_chose':wechatFormData.custom}"  @click="wechatFormData.custom = 1">自定义</span>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">内容设置</h2>
                                <div class="sContent">
                                    <div  class="style_item item"  v-show="!wechatFormData.custom">
                                        <div class="style_options">
                                            <label for="">公众号图标：</label>
                                            <div class="radioBox">
                                                <span :class="{'on_chose':wechatFormData.iconShow}" @click="wechatFormData.iconShow = 1"><s></s>显示</span>
                                                <span :class="{'on_chose':!wechatFormData.iconShow}" @click="wechatFormData.iconShow = 0"><s></s>不显示</span>
                                            </div>
                                        </div>
                                        <div class="style_options" v-show="wechatFormData.iconShow">
                                            <label for="" style="align-self:flex-start; padding-top:6px;">公众号图标：</label>
                                            <div class="img_up fn-clear">
                                                <div :class="['upbtn vip_icon',{'hasup':wechatFormData.icon}]">
                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="wechatFormData_icon_filedata" data-type="wechatFormData_icon">
                                                    <img :src="wechatFormData.icon" alt="" v-if="wechatFormData.icon">
                                                    <span v-show="wechatFormData.icon">更换图片</span>
                                                </div>
                                            </div>
                                            <div class="imgText">
                                                <p class="tip">建议尺寸100x100px</p>
                                            </div>
                                        </div>
                                        <div class="style_options">
                                            <label for="">标题：</label>
                                            <div class="inpbox">
                                                <el-input v-model="wechatFormData.title" maxlength="20" show-word-limit  placeholder="请输入标题" ></el-input>
                                            </div>
                                        </div>
                                        <div class="style_options">
                                            <label for="">副标题：</label>
                                            <div class="inpbox">
                                                <el-input v-model="wechatFormData.subtitle" maxlength="20" show-word-limit  placeholder="请输入标题" ></el-input>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">标题：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker   @active-change="(value) => activeChangeColor(value,'titleStyle','wechatFormData')" class="colorpicker" v-model="wechatFormData.titleStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(wechatFormData.titleStyle.color).replace('#','')"  @change="changeColor('titleStyle','wechatFormData')" maxlength="6">
                                                <button @click="resetColor('titleStyle','wechatFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">副标题：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'subtitleStyle','wechatFormData')"   class="colorpicker" v-model="wechatFormData.subtitleStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(wechatFormData.subtitleStyle.color).replace('#','')"  @change="changeColor('subtitleStyle','wechatFormData')" maxlength="6">
                                                <button @click="resetColor('subtitleStyle','wechatFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">按钮颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'btnStyle','wechatFormData')"  class="colorpicker" v-model="wechatFormData.btnStyle.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(wechatFormData.btnStyle.color).replace('#','')"  @change="changeColor('btnStyle','wechatFormData')" maxlength="6">
                                                <button @click="resetColor('btnStyle','wechatFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="style_item item" v-show="wechatFormData.custom">
                                        <div class="style_options">
                                            <label for="" style="align-self:flex-start; padding-top:6px;">设置图片：</label>
                                            <div class="img_up fn-clear">
                                                <div :class="['upbtn fullW ',{'hasup':wechatFormData.image}]">
                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="wechatFormData_image_filedata" data-type="wechatFormData_image">
                                                    <img :src="wechatFormData.image" alt="" v-if="wechatFormData.image" />
                                                    <span v-show="wechatFormData.image">更换图片</span>
                                                </div>
                                            </div>
                                            <div class="imgText">
                                                <p class="tip">建议宽度1080px</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="wechatFormData.style.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="wechatFormData.style.borderRadius = (wechatFormData.style.borderRadius ? wechatFormData.style.borderRadius : 0)" v-model="wechatFormData.style.borderRadius" @input="checkMax(50,'borderRadius','wechatFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options" v-show="wechatFormData.custom">
                                                <label for="">组件高度：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="wechatFormData.style.height" :min="100" :max="300"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="wechatFormData.style.height = (wechatFormData.style.height ? wechatFormData.style.height : 0)" v-model="wechatFormData.style.height" @input="checkMax(120,'height','wechatFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="wechatFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="wechatFormData.style.marginTop = (wechatFormData.style.marginTop ? wechatFormData.style.marginTop : 0)" v-model="wechatFormData.style.marginTop" @input="checkMax(40,'marginTop','wechatFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" v-model="wechatFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="wechatFormData.style.marginLeft = (wechatFormData.style.marginLeft ? wechatFormData.style.marginLeft : 0)" v-model="wechatFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','wechatFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 瓷片广告 -->
                <div :class="['rightCon',{'show':currEditPart === 7}] " v-cloak>
                    <div class="financeContainer cipianBox modBox" >
                        <div class="title">瓷片广告
                            
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('adv',7)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('adv')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('adv',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('adv',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="financeStyle">
                            <div class="modstyleBox">
                                <h2 class="stitle">组件设置</h2>
                                <div class="column_chose">
                                    <s :class="{'custom':wechatFormData.custom}" :style="'left:'+ offsetLeft +'px;'"></s>
                                    <span v-for="item in 3" :class="['item_' + item]" @click="choseAdvType(item)"><em v-for="i in item"></em></span>

                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">内容设置</h2>
                                <div class="sContent">
                                    <div  class="btnsBox btn_item cipianSort" >
                                        <div class="option_item item" :data-id="JSON.stringify(btn)" v-for="(btn,ind) in advFormData.list">
                                            <s class="del_item"  v-if="advFormData.column === 1 && advFormData.list.length != 1" @click="advFormData.list.splice(ind,1)"></s>
                                            <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                            <div class="item_con">
                                                <div class="img_up fn-clear">
                                                    <div :class="['upbtn fullW',{'hasup':btn.image}]">
                                                        <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp"  :id="'adv_'+ind+'_filedata'" :data-type="'adv_'+ind" >
                                                        <img v-show="btn.image" :src="btn.image" alt="">
                                                        <span>更换图片</span>
                                                    </div>
                                                    <div class="imgText">
                                                        <h4>广告图片</h4>
                                                        <p>建议宽度1080px</p>
                                                    </div>
                                                </div>
                                                <div class="inpbox linkbox linkChose">
                                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                    <input type="text" placeholder="请选择链接" class="iconLink"  :value="solveShowtext(btn.linkInfo,btn.link)" readonly @click="changeLink('advFormData.list.' + ind +'.link' )">
                                                </div>
                                            </div>
                                        </div>
                                        <a href="javascript:;" class="add_more" @click="addMoreBtn('adv')" v-show="advFormData.column === 1"><s></s>添加广告</a>
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="advFormData.style.borderRadius"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" @change="advFormData.style.borderRadius = (advFormData.style.borderRadius ? advFormData.style.borderRadius : 0)" class="colorShow"  v-model="advFormData.style.borderRadius" @input="checkMax(50,'borderRadius','advFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options" >
                                                <label for="">广告高度：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :step="4" :min="100" :max="300" v-model="advFormData.style.height"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change=" checkAdvheight()" v-model="advFormData.style.height"  >
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="40" v-model="advFormData.style.marginTop"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="advFormData.style.marginTop = (advFormData.style.marginTop ? advFormData.style.marginTop : 0)"  v-model="advFormData.style.marginTop" @input="checkMax(40,'marginTop','advFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip"  :min="0" :max="40"  v-model="advFormData.style.marginLeft"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow"  @change="advFormData.style.marginLeft = (advFormData.style.marginLeft ? advFormData.style.marginLeft : 0)"  v-model="advFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','advFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 分隔标题 -->
                <div :class="['rightCon',{'show':currEditPart === 8}] " v-cloak>
                    <div class="financeContainer titleBox modBox">
                        <div class="title">分隔标题
                            
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 8 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('title',8)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('title')"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 8 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('title',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 8 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('title',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="financeStyle">

                            <!-- 组件样式 -->
                            <div class="modstyleBox">
                                <h2 class="stitle">组件样式</h2>
                                <div class="titleSetBox">
                                    <div class="style fn-clear">
                                        <div class="column_chose" style="width:200px;">
                                            <s :class="!titleFormData.layout ? 'dark' : 'light'"></s>
                                            <span data-col="dark" @click="titleFormData.layout = 0">居左</span>
                                            <span data-col="light" @click="titleFormData.layout = 1">居中</span>
                                        </div>
                                        <el-checkbox label="显示查看更多" class="showMore" v-model="titleFormData.more.show"></el-checkbox>
                                    </div>
                                    <el-input class="bgColorInp" show-word-limit maxlength="10" placeholder="请输入标题" v-model="titleFormData.title.text"></el-input>
                                </div> 
                            </div>
    
                            <!-- 样式设置 -->
                            <div class="modstyleBox contentSetBox">
                                <h2 class="stitle">样式设置</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">标题样式：</label>
                                            <div class="radioBox">
                                                <span :class="[{'on_chose':titleFormData.title.type == ind}]" @click="titleFormData.title.type = ind" v-for="(item,ind) in showTypeOpt1"><s></s>{{item}}</span>
                                            </div>
                                            
                                        </div>
                                        
    
                                        <div class="style_options" v-show="titleFormData.title.type === 1" >
                                            <label for="">划线颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'border','titleFormData')" class="colorpicker" v-model="titleFormData.title.style.borderColor"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(titleFormData.title.style.borderColor).replace('#','')" @change="changeColor('border','titleFormData')" maxlength="6">
                                                <button @click="resetColor('border','titleFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" v-show="titleFormData.title.type === 2" >
                                            <label for="" style="align-self:flex-start; padding-top:6px;">添加图标：</label>
                                            <div class="img_up fn-clear">
                                                <div :class="['upbtn vip_icon',{'hasup':titleFormData.title.icon}]">
                                                    <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp" id="titleFormData_filedata" data-type="titleFormData">
                                                    <img :src="titleFormData.title.icon" alt="" v-if="titleFormData.title.icon">
                                                    <span v-show="titleFormData.title.icon">更换图片</span>
                                                </div>
                                            </div>
                                            <div class="imgText">
                                                <p class="tip">建议高度60px</p>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">标题颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker @active-change="(value) => activeChangeColor(value,'title','titleFormData')" class="colorpicker" v-model="titleFormData.title.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(titleFormData.title.style.color).replace('#','')" @change="changeColor('title','titleFormData')" maxlength="6">
                                                <button @click="resetColor('title','titleFormData')">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">标题大小：</label>
                                            <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="24" :max="48" v-model="titleFormData.title.style.fontSize"></el-slider>
                                            <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                <input type="number" class="colorShow" @change="titleFormData.title.style.fontSize = (titleFormData.title.style.fontSize ? titleFormData.title.style.fontSize : 0)" v-model="titleFormData.title.style.fontSize"  @input="checkMax(48,'fontSize','titleFormData')">
                                                <span class="single back">px</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modstyleBox contentSetBox" v-show="titleFormData.more.show">
                                <h2 class="stitle">查看更多</h2>
                                <div class="sContent">
                                    <div class="item style_item ">
                                        <div class="style_options" >
                                            <label for="">显示文本：</label>
                                            <div class="textSetBox">
                                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="titleFormData.more.text"></el-input>
                                            </div>
                                            <span :class="['showArr',{'noColor':!titleFormData.more.arr}] " @click="titleFormData.more.arr = !titleFormData.more.arr"></span>
                                        </div>
                                        <div class="style_options" >
                                            <label for="">链接：</label>
                                            <div class="inpbox linkbox linkChose">
                                                <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(titleFormData.more.linkInfo && titleFormData.more.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                                <input type="text" placeholder="请选择链接" class="iconLink" :value="solveShowtext(titleFormData.more.linkInfo,titleFormData.more.link)" readonly @click="changeLink('titleFormData.more.link')">
                                            </div>
                                        </div>
    
                                        <div class="style_options" >
                                            <label for="">文本颜色：</label>
                                            <div class="colorpickerBox">
                                                <el-color-picker  @active-change="(value) => activeChangeColor(value,'more','titleFormData')"  class="colorpicker" v-model="titleFormData.more.style.color"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" spellcheck="false" :value="(titleFormData.more.style.color).replace('#','')" @change="changeColor('more','titleFormData')" maxlength="6">
                                                <button @click="resetColor('more','titleFormData')">重置</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">间距设置</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options" >
                                                <label for="">高度：</label>
                                                <el-slider tooltip-class="tooltip_show" :step="2" :show-tooltip="showTooltip" :min="80" :max="180" v-model="titleFormData.style.height"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="checktitHeighr()" v-model="titleFormData.style.height"  >
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip"  :min="0" :max="40"  v-model="titleFormData.style.marginLeft"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="titleFormData.style.marginLeft = (titleFormData.style.marginLeft ? titleFormData.style.marginLeft : 0)" v-model="titleFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','titleFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                
                </div>


                <!-- 页面设置 -->
                <div :class="['rightCon',{'show':currEditPart === 0}] " v-cloak>
                    <div class="financeContainer modBox">
                        <div class="title">页面设置
                            
                            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 0 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('page',0)">
                                <div class="text_con">
                                    <h4>确认重置该组件为默认样式？</h4>
                                </div>
                                <div class="alertBtn">
                                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                    <a href="javascript:;" class="sure" @click="resetItem('page',0)"><s></s><span>确定</span></a>
                                </div>
                                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
                            </el-popover>
                            <div class="reset_btn" v-else>
                                <span class="btn"><s></s>重置此项</span>
                                <div class="smPop">
                                    <ul class="subtit_options">
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 0 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认恢复上次保存样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('page',1)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">恢复上次保存</li> 
                                        </el-popover>
                                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 0 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                                            <div class="text_con">
                                                <h4>确认将该组件恢复默认样式？</h4>
                                            </div>
                                            <div class="alertBtn">
                                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                                <a href="javascript:;" class="sure" @click="resetItem('page',0)"><s></s><span>确定</span></a>
                                            </div>
                                            <li class="text" slot="reference">重置默认样式</li> 
                                        </el-popover>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="financeStyle" >
                            <el-collapse-transition  >

                                <div class="contbox" @click="showTitle = true" >
                                    <template v-if="currPlatform.id != 'h5' && currPlatform.id != 'dymini'   ">
                                        <div class="modstyleBox titleSetBox">
                                            <h2 class="stitle">页面标题<span>（用户上划页面后，浮起并固定在顶部的内容）</span></h2>
                                            <div class="column_chose">
                                                <s :class="{'custom':pageFormData.showType}"></s>
                                                <span :class="{'on_chose':!pageFormData.showType}" @click="pageFormData.showType = 0">显示会员信息</span>
                                                <span :class="{'on_chose':pageFormData.showType}" @click="pageFormData.showType = 1">固定标题</span>
                                            </div>
                                            <el-input class="bgColorInp" v-show="pageFormData.showType" show-word-limit maxlength="10" placeholder="请输入标题"
                                                v-model="pageFormData.title"></el-input>
                                    
                                        </div>
                                        <!-- 样式设置 -->
                                        <div class="modstyleBox contentSetBox">
                                            <h2 class="stitle">内容样式</h2>
                                            <div class="sContent">
                                                <div class="style_item noBg">
                                                    <div class="style_options">
                                                        <label for="">标题位置：</label>
                                                        <div class="radioBox">
                                                            <span :class="{'on_chose':pageFormData.layout == 0}"
                                                                @click="pageFormData.layout = 0"><s></s>居左</span>
                                                            <span :class="{'on_chose':pageFormData.layout == 1}"
                                                                @click="pageFormData.layout = 1"><s></s>居中</span>
                                                        </div>
                                                    </div>
                                    
                                    
                                                    <div class="style_options">
                                                        <label for="">标题颜色：</label>
                                                        <div class="colorpickerBox">
                                                            <el-color-picker @active-change="(value) => activeChangeColor(value,'color','pageFormData')"
                                                                class="colorpicker" v-model="pageFormData.style.color"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false"
                                                                :value="(pageFormData.style.color).replace('#','')"
                                                                @change="changeColor('color','pageFormData')" maxlength="6">
                                                            <button @click="resetColor('color','pageFormData')">重置</button>
                                                        </div>
                                                    </div>
                                    
                                                    <div class="style_options">
                                                        <label for="">顶部背景：</label>
                                                        <div class="colorpickerBox">
                                                            <el-color-picker
                                                                @active-change="(value) => activeChangeColor(value,'background','pageFormData')"
                                                                class="colorpicker" v-model="pageFormData.style.background"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false"
                                                                :value="(pageFormData.style.background).replace('#','')"
                                                                @change="changeColor('background','pageFormData')" maxlength="6">
                                                            <button @click="resetColor('background','pageFormData')">重置</button>
                                                        </div>
                                                    </div>
                                                    <div class="style_options">
                                                        <label for="">右侧按钮：</label>
                                                        <div class="radioBox">
                                                            <span :class="[{'on_chose':pageFormData.btns.showType == ind}]"
                                                                @click="pageFormData.btns.showType= ind"
                                                                v-for="(item,ind) in showTypeOpt2"><s></s>{{item}}</span>
                                                        </div>
                                    
                                                    </div>
                                                    <div class="btnsBox btn_item" v-show="pageFormData.btns.showType === 2">
                                                        <div class="option_item item" v-for="(btn,ind) in pageFormData.btns.list">
                                                            <s class="del_item" @click="pageFormData.btns.list.splice(ind,1); "></s>
                                                            <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                                            <div class="item_con">
                                                                <div class="img_up fn-clear">
                                                                    <div :class="['upbtn', {'hasup':btn.icon}]">
                                                                        <input type="file" name="Filedata" accept="png,jpg,jpeg" class="fileUp"
                                                                            :id="'page_'+ind+'_filedata'" :data-type="'page_'+ind">
                                                                        <img :src="btn.icon" alt="">
                                                                        <span>更换图片</span>
                                                                    </div>
                                                                    <div class="imgText">
                                                                        <h4>{{btn.text}}</h4>
                                                                        <p>建议图标尺寸40*40px</p>
                                                                    </div>
                                                                </div>
                                                                <div class="inpbox linkbox linkChose">
                                                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'"
                                                                            alt=""></s>
                                                                    <input type="text" placeholder="请选择链接" class="iconLink"
                                                                        :value="solveShowtext(btn.linkInfo,btn.link)" readonly
                                                                        @click="changeLink('pageFormData.btns.list.' + ind +'.link' )">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <a href="javascript:;" class="add_more" @click="addMoreBtn('pageBtns')"
                                                            v-show="pageFormData.btns.list.length < 3 "><s></s>添加按钮</a>
                                                    </div>
                                                    <div class="style_options" v-show="!pageFormData.btns.showType">
                                                        <label for="">按钮颜色：</label>
                                                        <div :class="['colorpickerBox',{'noColor':!pageFormData.btns.style.color}]">
                                                            <el-color-picker @active-change="(value) => activeChangeColor(value,'btnsColor','pageFormData')"
                                                                popper-class="borderColorPicker" class="colorpicker"
                                                                v-model="pageFormData.btns.style.color"></el-color-picker>
                                                        </div>
                                                        <div class="colorShowBox">
                                                            <span class="single">#</span>
                                                            <input type="text" class="colorShow" spellcheck="false"
                                                                :value="(pageFormData.btns.style.color ? (pageFormData.btns.style.color).replace('#','') : '')"
                                                                @change="changeColor('btnsColor','pageFormData')" maxlength="6">
                                                            <button @click="resetColor('btnsColor','pageFormData')">重置</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    
                                    </template>
                                    <div class="modstyleBox  marginTopBox" v-else-if="memberCompFormData.showHeader != 0 && memberCompFormData.theme != 3">
                                        <h2 class="stitle">顶部操作区<span> 用户上划页面后，是否浮起并固定在顶部</span></h2>
                                        <div class="sContent" >
                                            <div class="style_item noBg" >
                        
                                                <div class="style_options" style="padding-left: 6px;">
                                                    <label for=""> 固定在顶部：</label>
                                                    <div class="split_options radioBox">
                                                        <span v-for="(item,key) in 2" :class="{'onChose':pageFormData.h5FixedTop == (!key ? 1 : 0)}"  @click="pageFormData.h5FixedTop = (!key ? 1 : 0)"><s></s>{{key == 0 ? '固定' : '不固定'}}</span>
                                                    </div>
                                                </div>
                                                <div class="style_options">
                                                    <label for="">背景颜色：</label>
                                                    <div class="colorpickerBox">
                                                        <el-color-picker class="colorpicker" v-model="pageFormData.style.bgImgColor" @active-change="value => activeChangeColor_new(value,'pageFormData.style.bgImgColor')"></el-color-picker>
                                                    </div>
                                                    <div class="colorShowBox" v-if="pageFormData.style.hasOwnProperty('bgImgColor')">
                                                        <span class="single">#</span>
                                                        <input type="text" class="colorShow" :value="pageFormData.style.bgImgColor.replace('#','')" @change="changeColor_new('pageFormData.style.bgImgColor')" spellcheck="false" maxlength="6">
                                                        <button @click="pageFormData.style.bgImgColor = '#ffffff'">重置</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> 
                            </el-collapse-transition>
                            <div class="modstyleBox  marginTopBox marginSetBox" v-if="pageFormData.bgType ">
                                <h2 class="stitle">页面背景</h2>
                                <div class="sContent" >
                                    <div class="style_item noBg" >
                                        <div class="style_options">
                                            <label for="">页面背景：</label>
                                            <div class="split_options radioBox">
                                                <span v-for="(item,key) in 2" :class="{'onChose':pageFormData.bgType == (!key ? 'color' : 'image')}"  @click="pageFormData.bgType = (!key ? 'color' : 'image')"><s></s>{{key == 0 ? '纯色' : '渐变'}}</span>
                                            </div>
                                        </div>
                                        <div class="style_options">
                                            <label for="">背景色{{pageFormData.bgType == 'image' ? 1 : ''}}：</label>
                                            <div :class="['colorpickerBox',{'noColor':!pageFormData.style.bgColor1}]">
                                                <el-color-picker class="colorpicker" v-model="pageFormData.style.bgColor1" @active-change="value => activeChangeColor_new(value,'pageFormData.style.bgColor1')"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" :value="pageFormData.style.bgColor1.replace('#','')" @change="changeColor_new('pageFormData.style.bgColor1')" spellcheck="false" maxlength="6">
                                                <button @click="pageFormData.style.bgColor1 = '#f5f7fa'">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" v-if="pageFormData.bgType == 'image'">
                                            <label for="">背景色2：</label>
                                            <div :class="['colorpickerBox',{'noColor':!pageFormData.style.bgColor2}]">
                                                <el-color-picker class="colorpicker" v-model="pageFormData.style.bgColor2" @active-change="value => activeChangeColor_new(value,'pageFormData.style.bgColor2')"></el-color-picker>
                                            </div>
                                            <div class="colorShowBox">
                                                <span class="single">#</span>
                                                <input type="text" class="colorShow" :value="pageFormData.style.bgColor2.replace('#','')" @change="changeColor_new('pageFormData.style.bgColor2')" spellcheck="false" maxlength="6">
                                                <button @click="pageFormData.style.bgColor1 = ''">重置</button>
                                            </div>
                                        </div>
                                        <div class="style_options" v-if="pageFormData.bgType == 'image'">
                                            <label for="">渐变位置：</label>
                                            <el-slider step="2" tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="100" v-model="pageFormData.style.start"></el-slider>
                                            <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                <input type="number"  class="colorShow"  :value="pageFormData.style.start"  @input="checkMax(0,100,'pageFormData.style.start')">
                                                <span class="single back">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 间距设置 -->
                            <div class="marginSetBox" >
                                <h2 class="stitle">全局样式</h2>
                                <el-collapse-transition>
                                    <div class="style_item" v-show="showMarginSet">
                                        <div class="sContent">
                                            <div class="style_options">
                                                <label for="">圆角值：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" @change="(value) => changeOverAll('borderRadius',value)" v-model="pageFormData.style.borderRadius" :min="0" :max="50"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="pageFormData.style.borderRadius = (pageFormData.style.borderRadius ? pageFormData.style.borderRadius : 0);  changeOverAll('borderRadius',pageFormData.style.borderRadius)"  v-model="pageFormData.style.borderRadius" @input="checkMax(50,'borderRadius','pageFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">上间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" @change="(value) => changeOverAll('marginTop',value)" v-model="pageFormData.style.marginTop"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="pageFormData.style.marginTop = (pageFormData.style.marginTop ? pageFormData.style.marginTop : 0);  changeOverAll('marginTop',pageFormData.style.marginTop)" v-model="pageFormData.style.marginTop" @input="checkMax(40,'marginTop','pageFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                            <div class="style_options">
                                                <label for="">左右间距：</label>
                                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" @change="(value) => changeOverAll('marginLeft',value)" v-model="pageFormData.style.marginLeft"  :min="0" :max="40"></el-slider>
                                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                                    <input type="number" class="colorShow" @change="pageFormData.style.marginLeft = (pageFormData.style.marginLeft ? pageFormData.style.marginLeft : 0); changeOverAll('marginLeft',pageFormData.style.marginLeft) " v-model="pageFormData.style.marginLeft"  @input="checkMax(40,'marginLeft','pageFormData')">
                                                    <span class="single back">px</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-transition>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
    
        </div>

        <!-- 弹窗 -->
        <div class="largePopBox">
            <div class="mask" v-show="showMemberCardPop || showMemberTopStylePop || showholeModelStylePop || linkSetPop || modelChosePop || platformChosePop || advanceSetPop"></div>
            <template v-if="showMemberCardPop">
                <!-- 会员头部图片选择 -->
                <div class="largePop" v-show="showMemberCardPop">
                    <div class="pop_header">
                        <a href="javascript:;" class="close_pop" @click="showMemberCardPop = false;"></a>
                        <h4>背景图</h4>
                        <div class="upload">
                            <div class="upload_btn">自定义上传 <input type="file" name="Filedata" id="memberBg_filedata" data-type="memberBg" accept="png,jpg,jpeg" class="fileUp"></div>
                            <span>建议尺寸750*500px，不超过1M</span>
                        </div>
                    </div>
                    <div class="pop_con">
                        <div class="mh_style_Box">
                            <ul class="fn-clear">
                                <li v-if="memberBg" @click="memberCompFormData.bgType = 'image';memberCompFormData.style.bgID = ''" :class="{'on_chose':memberCompFormData.style.bgID === '' && memberCompFormData.bgType == 'image'}">
                                    <div class="mimg"><img :src="memberBg" alt=""></div>
                                    <div class="tip_show">
                                        <span>{{memberCompFormData.style.bgID === '' && memberCompFormData.bgType == 'image' ? '已选择' : '选择图片'}}</span>
                                    </div>
                                </li>
                                <li v-for="item in 9" :class="{'on_chose':item === memberCompFormData.style.bgID && memberCompFormData.bgType == 'image'}" @click="memberCompFormData.style.bgID = item;memberCompFormData.bgType = 'image';">
                                    <div class="mimg"><img :src="defaultPath + 'bg_' + (item > 9 ? item : ('0' + item)) + '.png'" alt=""></div>
                                    <div class="tip_show">
                                        <span>{{item === memberCompFormData.style.bgID && memberCompFormData.bgType == 'image'? '已选择' : '选择图片'}}</span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pop_footer">
                        <p>更多官方素材更新中…</p>
                        <div class="btns_group">
                            <a href="javascript:;" class="cancel_btn"  @click="showMemberCardPop = false;">取消</a>
                            <a href="javascript:;" class="sure_btn" @click="sureChoseMemberBg">确定</a>
                        </div>
                    </div>
                </div>
            </template>
            <template v-if="showMemberTopStylePop">
                <!-- 会员头部组件模板 -->
                <div class="largePop topModelBox" v-show="showMemberTopStylePop">
                    <div class="pop_header">
                        <a href="javascript:;" class="close_pop" @click="showMemberTopStylePop = false;"></a>
                        <h4>官方素材</h4>
                        <div class="pop_tip">
                            <em class="el-icon-info"></em>更换头部组件样式后，您之前对其所做的更改将不被保存
                        </div>
                    </div>
                    <div class="pop_con">
                        <div class="styleListBox">
    
                            <ul class="styleList fn-clear">
                                <li v-for="(item,ind) in memberDefaultStyle" :class="{'on_chose':currMemberStyle == ind}" @click="choseMemberStyle(item,ind)">
                                    <div class="styleBox">
                                        <div class="styleShow"><img :src="'/static/images/admin/siteMemberPage/member_0'+item.theme+'.png'" alt=""></div>
                                        <p>样式{{numberText[ind]}}</p>
                                    </div>
                                </li>
                               
                            </ul>
                        </div>
                    </div>
                    <div class="pop_footer">
                        <p>更多自定义页面、模板更新中…</p>
                        <div class="btns_group">
                            <a href="javascript:;" class="cancel_btn"  @click="showMemberTopStylePop = false;">取消</a>
                            <a href="javascript:;" class="sure_btn" @click="sureChoseMemberStyle">确定</a>
                        </div>
                    </div>
                </div>
            </template>
            <!-- 官方模板 -->
            <template  v-if="showholeModelStylePop">
                <div class="largePop holeModelBox" v-show="showholeModelStylePop">
                    <div class="pop_header">
                        <a href="javascript:;" class="close_pop" @click="showholeModelStylePop = false;" v-show="hasSetModel"></a>
                        <h4>{{hasSetModel ? '官方模板' : '请选择模板'}}</h4>
                        <div class="pop_tip">
                            <em class="el-icon-info"></em>{{hasSetModel ? '更换模板后，您之前对个人中心所做的更改将不被保存' : '进入编辑后仍可切换模板，可选择较适合您的模板在此基础上修改'}}
                        </div>
                    </div>
                    <div class="pop_con">
                        <div class="styleListBox">
    
                            <ul class="styleList">
                                <li v-for="(item,ind) in modelsArr" :class="{'on_chose':currMemberModel == ind}" >
                                    <div class="styleBox">
                                        <div class="styleShow">
                                            <img :src="'/static/images/admin/siteMemberPage/style_0'+(ind + 1)+'.png'" alt="">
                                            <div class="ueBox">
                                                <span @click="choseModel(item,ind)">使用模板</span>
                                            </div>
                                        </div>
                                        <div class="styleInfo">
                                            <p>模板{{numberText[ind]}}</p>
                                            <h3>免费</h3>
                                        </div> 
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="pop_footer">
                        <p>更多自定义页面、模板更新中…</p>
                        <div class="btns_group">
                            <a href="javascript:;" class="cancel_btn"  @click="showholeModelStylePop = false;">取消</a>
                            <a href="javascript:;" class="sure_btn" @click="sureChoseMemberStyle">确定</a>
                        </div>
                    </div> -->
                </div>
            </template> 
            <template  v-if="linkSetPop">

                <!-- 选择链接 -->
                <div class="largePop linkSetBox" v-show="linkSetPop">
                    <div class="pop_header">
                        <a href="javascript:;" class="close_pop" @click="linkSetPop = false;"></a>
                        <h4>链接选择器</h4>
                    </div>
                    <div class="pop_con">
                        <div class="left_tab">
                            <ul>
                                <li :class="{'on_chose': linkSetForm.slefSet}" @click="linkSetForm.slefSet = 1">自定义链接</li>
                                <li :class="{'on_chose': !linkSetForm.slefSet}" @click="linkSetForm.slefSet = 0">常用链接</li>
                            </ul>
                        </div>
                        <div class="right_con">
                            <div class="slefSetBox setBox" v-show="linkSetForm.slefSet">
                                <div class="setCon" >
                                    <div class="linkType radioBox">
                                       <span :class="{'on_chose':linkSetForm.linkType === 0}" @click="linkSetForm.linkType = 0; "><s></s>跳转链接</span>
                                       <span :class="{'on_chose':linkSetForm.linkType === 1}" @click="linkSetForm.linkType = 1;" ><s></s>拨打电话</span>
                                    </div>
                                    <div class="linkBox">
                                        <div class="linkInp" v-show="!linkSetForm.linkType">
                                            <div class="label">链接地址:</div>
                                            <el-input placeholder="请输入链接" v-model="linkSetForm.selfLink"><i slot="prefix" class="el-input__icon el-icon-link"></i></el-input>
                                        </div>
                                        <div class="linkInp" v-show="linkSetForm.linkType">
                                            <div class="label">拨打电话:</div>
                                            <el-input placeholder="请输入电话" v-model="linkSetForm.selfTel"><i slot="prefix" class="el-input__icon el-icon-tel"></i></el-input>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="defaultSetBox setBox" v-show="!linkSetForm.slefSet">
                                <div class="setCon">
                                    <div class="searchBox">
                                        <el-input placeholder="搜索链接" v-model="keywords" @keyup.enter.native="toSearch" >
                                            <i class="el-icon-clear el-input__icon" v-show="keywords" slot="suffix" @click="keywords = ''; toSearch()">
                                          </i></el-input><span :class="['search_btn',{'onSearch':keywords}]" @click="toSearch">搜索</span>
                                    </div>
                                    <div class="linkList listGroup" >
                                        <dl class="group" v-for="group in searchList" v-show="searchList && searchList.length">
                                            <dt class="group_name" v-if="group.listName">{{group.listName}}</dt>
                                            <dd class="group_list">
                                                <ul class="fn-clear">
                                                    <li v-for="link in group.list" @click="choseLink(link)">{{link.linkText}}</li>
                                                </ul>
                                            </dd>
                                        </dl>
                                        <div class="noData" v-show="!searchList || searchList.length == 0">
                                            <p>暂无数据</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> 
                    </div> 
                    <div class="pop_foot">
                        <div class="btnsGroup">
                            <a href="javascript:;" class="cancel"  @click="linkSetPop = false;">取消</a>
                            <a href="javascript:;" class="sure" @click="sureChangeLink">确定</a>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 终端选择 -->
            <template v-if="platformChosePop">
                <div class="largePop platformChosePop" v-show="platformChosePop">
                    <div class="pop_header">
                        <h4>选择终端</h4>
                        <a href="javascript:;" class="close_pop" @click="platformChosePop = false; currChosePlatform = currPlatform"></a>
                    </div>
                    <div class="pop_con">
                        <div class="platformBox">
                            <h6>切换您想要编辑的终端</h6>
                            <ul>
                                <li :class="[{'on_chose':terminal.id == currChosePlatform.id},terminal.id]"  @click="chosePlatform(terminal)" v-for="terminal in terminalList" v-show="terminal.buy"><i class="icon"><img :src="'/static/images/admin/siteConfigPage/'+(terminal.id)+'.png'" alt=""></i><span>{{terminal.name}}</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="pop_foot">
                        <a href="javascript:;" class="sure" @click="sureChangePlatform">确定</a>
                    </div>
                </div>

            </template>

            <template v-if="modelChosePop">

                <!-- 选择模板 -->
                <div class="largePop modelChosePop" v-show="modelChosePop">
                    <div class="pop_header">
                        <h4>选择模板</h4><span>可选择现有页面，在此基础上编辑</span>
                        <a href="javascript:;" class="close_pop" @click="modelChosePop = false"></a>
                    </div>
                    <div class="pop_con">
                        <div class="left_tab">
                            <ul>
                                <li :class="{'on_chose': showModelType == 1}" @click="showModelType = 1">我的模板</li>
                                <li :class="{'on_chose': showModelType == 0}" @click="showModelType = 0">官方模板</li>
                            </ul>
                        </div>
                        <div class="right_tabCon">
                            <div class="myModelList" v-show="showModelType">
                                <h6>我的模板</h6>
                                <ul>
                                    <li class="newModel" v-if="sitePageData['default'] != 1">
                                        <div class="icon el-icon-plus"></div>
                                        <p>新建空白页面</p>
                                    </li>
                                    <template v-else>
                                        <template v-for="(item,ind) in platsArr">
                                            <li class="model_li" v-if="sitePageData[item] == 1">
                                                <div class="model">
                                                    <img :src="'{#$cfg_attachment#}' + sitePageData[item + '_cover']" alt="">
                                                    <div class="ueBox">
                                                        <span @click="userModel(item,ind)">使用模板</span>
                                                    </div>
                                                </div>
                                                <h5 v-if="item == 'app'">APP</h5>
                                                <h5 v-else-if="item == 'wxmini'">微信小程序</h5>
                                                <h5 v-else-if="item == 'dymini'">抖音小程序</h5>
                                                <h5 v-else>H5</h5>
                                            </li>
                                        </template>
                                    </template>
                                </ul>
                            </div>
                            <div class="modelList" v-show="!showModelType">
                                <h6>官方模板</h6>
                                <ul>
                                    <li class="model_li" v-for="(item,ind) in modelsArr">
                                        <div class="model">
                                            <img :src="'/static/images/admin/siteMemberPage/style_0'+(ind + 1)+'.png'" alt="">
                                            <div class="ueBox">
                                                <span @click="choseModel(item,ind)">使用模板</span>
                                            </div>
                                        </div>
                                        <div class="modelInfo">
                                            <p>模板{{numberText[ind]}}</p>
                                            <h3>免费</h3>
                                        </div> 
                                    </li>
                                </ul>
                                <p class="moreTip">更多自定义页面、模板更新中…</p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
    
    
                <!-- 高级选项 -->
    <template v-if="advanceSetPop">
        <div class="largePop advanceSetPop" v-show="advanceSetPop">
            <div class="pop_header">
                <h4>发布高级选项</h4>
                <p><i class="el-icon el-icon-info"></i><span>批量设置同步此模板到其他终端</span></p>
                <a href="javascript:;" class="close_pop" @click="closeAdvancePop"></a>
            </div>
            <div class="pop_con">
                <div class="coverShowBox">
                    <div class="coverShow"></div>
                </div>
                <div class="setConList">
                    <h6>批量同步<span>{{currPlatform.name ? currPlatform.name : ''}}</span>到：</h6>

                    <!-- 终端选择 -->
                    <div class="platformChoseBox setCon">
                        <h5>选择终端</h5>
                        <div class="setShow">
                            <ul>
                                <li v-for="item in terminalList" v-show="currPlatform.id != item.id" :class="{'on_chose':advancsFormData.plats.find(obj => {return obj.id == item.id})}" @click="chosePlatformAdv(item)" v-show="item.buy">{{item.name}}</li>
                            </ul>
                        </div>
                    </div>

                    
                    <div class="btns_group">
                        <a href="javascript:;" class="fb_btn" v-if="advancsFormData.plats && advancsFormData.plats.length " @click="directSave()">直接同步并发布</a>
                        <a href="javascript:;" class="fb_btn cancel_btn" v-else @click="closeAdvancePop">取消</a>
                        <a href="javascript:;" :class="['cn_btn',{'disabeld':(!advancsFormData.plats || !advancsFormData.plats.length )}]" @click="confirmAdvance">确定</a>
                    </div>
                </div>
            </div>
        </div>
    </template>
     
        </div> 
    </div>

</body>
<script src="/static/js/vue/vue.min.js"></script>
<script src="/static/js/ui/Sortable.js"></script>
<script src="/static/js/image-main-color.js"></script>
<script src="/static/js/ui/element_ui_index.js"></script>
<script src="/static/js/core/jquery-2.1.1.min.js"></script>
<script src="/static/js/ui/pickr.min.js"></script>
<script src="/static/js/ui/jquery.ajaxFileUpload.js"></script>
<script src="/static/js/admin/html2canvas.js"></script>
<script src="/static/js/admin/siteMemberPage.js?v={#$cfg_staticVersion#}"></script>
</html>