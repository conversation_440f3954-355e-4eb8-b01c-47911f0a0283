<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>ElasticSearch配置管理</title>
    {#$cssFile#}
    <script>
        var adminPath = "{#$adminPath#}";
    </script>
    <style>
        table{
            border: 1px solid #e6e6e6;
            border-collapse: collapse;
            border-spacing: 1px;
        }
        th,td{
            border: 1px solid #e6e6e6;
            padding: 8px 16px;
            text-align: left;
        }
        th{
            background-color: #a9a9a9;
        }
        table tr td{
            word-break: break-all;
        }
    </style>
</head>
<body>

<div class="alert alert-success" style="margin:10px;">
    <button type="button" class="close" data-dismiss="alert">×</button>
    linux系统配置教程：<a href="https://help.kumanyun.com/help-68-782.html" target="_blank">https://help.kumanyun.com/help-68-782.html</a>
    <br>
    windows系统配置教程：<a href="https://help.kumanyun.com/help-68-781.html" target="_blank">https://help.kumanyun.com/help-68-781.html</a>
</div>
<div class="container-fluid">
    <form action="" method="post" name="editform" id="editform" class="editform">
        <dl class="clearfix">
            <dt><label>启用状态：</label></dt>
            <dd class="radio">
                {#html_radios id="esState" name="open" values=$open checked=$esStateChecked output=$esStateNames
                separator="&nbsp;&nbsp;"#}
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="port">服务器地址：</label></dt>
            <dd>
                <input class="input-large" type="text" name="host" id="server" data-regex=".{2,6}" maxlength="15"
                       value="{#$esConfig['host']#}" placeholder="127.0.0.1"/>
                <span class="input-tips"><s></s>请输入ES服务器地址。</span>
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="port">服务器端口：</label></dt>
            <dd>
                <input class="input-large" type="text" name="port" id="port" data-regex=".{2,6}" maxlength="10"
                       value="{#$esConfig['port']#}" placeholder="9200"/>
                <span class="input-tips"><s></s>请输入ES服务器端口。</span>
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="requirepass">用户名：</label></dt>
            <dd>
                <input class="input-large" type="text" name="username" id="uname" data-regex=".{5,30}" maxlength="30"
                       value="{#$esConfig['username']#}"/>
                <span class="input-tips"><s></s>请输入用户名。</span>
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="requirepass">用户密码：</label></dt>
            <dd>
                <input class="input-large" type="text" name="password" id="requirepass" data-regex=".{5,30}"
                       maxlength="30" value="{#$esConfig['password']#}"/>
                <span class="input-tips"><s></s>请输入用户密码。</span>
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="index_name">索引名称：</label></dt>
            <dd>
                <input class="input-large" type="text" name="index" id="index_name" data-regex=".{0,30}"
                       maxlength="30" value="{#$esConfig['index']#}" placeholder="huoniao"/>
                <span class="input-tips" style="display:inline-block;"><s></s>多个火鸟系统连接同一台服务器时请设置不同的索引名</span>
            </dd>
        </dl>
        <dl class="clearfix">
            <dt>状态：</dt>
            <dd class="singel-line">
                <a href="javascript:;" id="checkES">点击检测是否可用</a>
            </dd>
        </dl>
        <dl class="clearfix formbtn">
            <dt>&nbsp;</dt>
            <dd>
                <button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button>

                {#if $esStateChecked#}
                <button style="margin-left: 20px;" class="btn-link" type="button" id="asyncAllData">同步历史数据</button>
                {#/if#}
            </dd>
        </dl>
    </form>
</div>
<div class="container hide">
    <h2 class="text-center">分模块数据同步</h2>
    <br>
    <center>
        <table cellspacing="1">
            <tbody id="syncTable">
            <tr>
                <th>模块</th>
                <th>上次同步时间</th>
                <th>操作</th>
            </tr>
            {#if $build#}
            {#foreach from=$modules key=k item=v#}
            <tr module="{#$v['module']#}" second="{#$v['second']#}">
                <td>{#$v['description']#}</td>
                <td>{#$esConfig["{#$v['time']#}"]#}</td>
                <td>
                    <button class="btn btn-info">同步</button>
                </td>
            </tr>
            {#/foreach#}
            {#/if#}
        </table>
    </center>
    <br>
    <br>
</div>

{#$jsFile#}
</body>
</html>