<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var thumbSize = {#$thumbSize#}, thumbType = "{#$thumbType#}",
	typeid = {#$typeid#}, action = "{#$action#}",
	adminPath = "{#$adminPath#}", modelType = 'siteConfig', cityid = {#$cityid#}, cityList = {#$cityList#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="module">城市：</label></dt>
    <dd style="overflow: visible; padding-left: 140px;">
      <div class="choseCity"><input type="hidden" id="cityid" name="cityid" placeholder="请选择城市分站" value="{#$cityid#}"></div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="typeid">链接分类：</label></dt>
		<dd style="overflow: visible; padding-left: 140px;">
      <select class="chosen-select" id="typeid" name="typeid" style="width: auto; min-width: 150px;">
				<option value="">选择分类</option>
		    {#foreach from=$typeListArr item=type#}
		    <option value="{#$type.id#}"{#if $typeid == $type.id#} selected{#/if#}>{#$type.typename#}</option>
		    {#/foreach#}
      </select>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="sitename">网站名称：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="sitename" id="sitename" data-regex=".{1,60}" maxlength="60" value="{#$sitename#}" />
      <span class="input-tips"><s></s>请输入网站名称</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="sitelink">网站地址：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="sitelink" id="sitelink" data-regex=".{1,100}" maxlength="100" value="{#$sitelink#}" />
      <span class="input-tips"><s></s>请输入网站名称</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="sitelogo">网站LOGO：</label></dt>
		<dd class="thumb clearfix listImgBox">
			<div class="uploadinp filePicker thumbtn{#if $litpic != ""#} hide{#/if#}" id="filePicker1" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
			{#if $litpic != ""#}
			<ul id="listSection1" class="listSection thumblist clearfix" style="display:inline-block;"><li id="WU_FILE_0_1"><a href='{#$cfg_attachment#}{#$litpic#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$litpic#}" data-val="{#$litpic#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
			{#else#}
			<ul id="listSection1" class="listSection thumblist clearfix"></ul>
			{#/if#}
			<input type="hidden" name="litpic" value="{#$litpic#}" class="imglist-hidden" id="litpic">
		</dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="weight">排序：</label></dt>
    <dd>
      <input class="input-mini" type="number" name="weight" id="weight" min="0" data-regex="[1-9]\d*" value="{#$weight#}" />
      <span class="input-tips"><s></s>必填，排序越大，越排在前面</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>状态：</dt>
    <dd class="radio">
      {#html_radios name="arcrank" values=$arcrankList checked=$arcrank output=$arcrankName separator="&nbsp;&nbsp;"#}
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
