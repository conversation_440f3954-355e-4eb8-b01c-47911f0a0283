<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>管理广告</title>
{#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 100px 0 10px;"><button type="button" class="close" data-dismiss="alert">×</button>初始广告位操作教程：<a href="https://help.kumanyun.com/help-5-607.html" target="_blank">https://help.kumanyun.com/help-5-607.html</a><br />节日广告教程及素材下载：<a href="https://help.kumanyun.com/help-5-711.html" target="_blank">https://help.kumanyun.com/help-5-711.html</a><br />弹窗公告设置教程：<a href="https://help.kumanyun.com/help-5-738.html" target="_blank">https://help.kumanyun.com/help-5-738.html</a>{#if $action == 'article' || $action == 'info' || $action == 'tieba'#}<br />信息流广告设置教程：<a href="https://help.kumanyun.com/help-222-1116.html" target="_blank">https://help.kumanyun.com/help-222-1116.html</a>{#/if#}</div>

<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <div class="btn-group" id="typeBtn" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">全部分类<span class="caret"></span></button>
  </div>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
  {#if !$type && $userType != 3#}<a href="advType.php?action={#$action#}" class="btn btn-info ml30" id="typeManage">分类管理</a>{#/if#}
</div>

<div class="filter clearfix">
  <div class="f-left">
    {#if $userType != 3#}
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <a href="advAdd.php?action={#$action#}&type={#$type#}" class="btn btn-primary" id="addNew">新增广告</a>
    <button class="btn" id="importDefaultData" style="margin-left: 50px;">导入默认广告</button>
    <button class="btn btn-danger " id="deleteRepeat" style="margin-left: 50px;">一键删除重复广告</button>
    {#/if#}
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row30">标题</li>
  <li class="{#if $userType != 3#}row10{#else#}row14{#/if#}">{#if !$type#}分类{#else#}模板{#/if#}</li>
  <li class="{#if $userType != 3#}row10{#else#}row14{#/if#}">类型</li>
  <li class="{#if $userType != 3#}row13{#else#}row14{#/if#}">开始/结束时间</li>
  <li class="{#if $userType != 3#}row13{#else#}row25{#/if#}">分站广告</li>
  {#if $userType != 3#}
  <li class="row9">状态</li>
  <li class="row12">操作</li>
  {#/if#}
</ul>

<div class="list mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="sType"></span>
</div>

<script>
  var typeListArr = {#$typeListArr#}, action = "{#$action#}", adminPath = "{#$adminPath#}", atype = {#$type#}, userType = {#$userType#};
</script>
{#$jsFile#}
</body>
</html>
