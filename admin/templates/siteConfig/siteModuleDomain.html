<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>模块域名管理</title>
{#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px 100px 0 10px;"><button type="button" class="close" data-dismiss="alert">×</button>注意：系统开通多个分站城市后，模块将不支持绑定独立域名或者二级域名，只有一个分站城市，模块可以绑定独立域名或者二级域名！</div>

<div class="search">
  <div class="btn-group">
    <button class="btn dropdown-toggle" data-toggle="dropdown">批量操作<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <!-- <li><a href="javascript:;" data-id="0">主域名</a></li> -->
      <li><a href="javascript:;" data-id="1">子域名</a></li>
      <li><a href="javascript:;" data-id="2">子目录</a></li>
    </ul>
  </div>
  <button type="button" class="btn btn-success">保存全部</button>
</div>
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px 10px 0;">
  <li class="row3">&nbsp;</li>
  <li class="row12 left">模块名称</li>
  <li class="row10 left" style="overflow:visible;">类型</li>
  <li class="row35 left">域名</li>
  <li class="row40 left">操作</li>
</ul>
<div class="list mt124" id="list"><table><tbody><tr><td style="height:200px;" align="center">加载中...</td></tr></tbody></table></div>
<div class="search">
  <div class="btn-group dropup">
    <button class="btn dropdown-toggle" data-toggle="dropdown">批量操作<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <!-- <li><a href="javascript:;" data-id="0">主域名</a></li> -->
      <li><a href="javascript:;" data-id="1">子域名</a></li>
      <li><a href="javascript:;" data-id="2">子目录</a></li>
    </ul>
  </div>
  <button type="button" class="btn btn-success">保存全部</button>
</div>
<script>var adminPath = "{#$adminPath#}", cfg_basehost = '{#$cfg_basehost_#}', token = '{#$token#}', moduleArr = {#$moduleArr#};</script>
{#$jsFile#}
</body>
</html>
