<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>管理第三方登录接口</title>
{#$cssFile#}
</head>

<body>
<div class="alert alert-success" style="margin:10px;"><button type="button" class="close" data-dismiss="alert">×</button>网站整合登录配置教程：<a href="https://help.kumanyun.com/help-52.html" target="_blank">https://help.kumanyun.com/help-52.html</a></div>

{#if $installArr|@count neq 0#}
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px;">
  <li class="row50 left">&nbsp;&nbsp;已安装</li>
  <li class="row10 left">作者</li>
  <li class="row20">排序</li>
  <li class="row20 left">操作</li>
</ul>
<div class="list mb50" id="list" style="margin-top:-20px;">
  <ul class="root">
    {#foreach from=$installArr item=install#}
    <li class="li0">
      <div class="tr clearfix" data-id="{#$install.id#}">
        <div class="row50 left">&nbsp;&nbsp;&nbsp;<strong>{#$install.name#}</strong><sup>{#$install.version#}</sup>&nbsp;&nbsp;<a href="javascript:;" class="explain">说明</a></div>
        <div class="row10 left"><a href="{#$install.website#}" target="_blank">{#$install.author#}</a></div>
        <div class="row20"><a href="javascript:;" class="up">向上</a><a href="javascript:;" class="down">向下</a></div>
        <div class="row20 left"><a href="loginConnect.php?action=edit&id={#$install.id#}" class="modify" data-title="{#$install.name#}" data-id="{#$install.id#}">配置</a>&nbsp;|&nbsp;<a href="loginConnect.php?action=uninstall&id={#$install.id#}" class="uninstall">卸载</a>{#if $install.state == 2#}&nbsp;&nbsp;&nbsp;&nbsp;<font color="#f00">未启用</font>{#/if#}</div>
        <div class="hide">{#$install.desc#}</div>
      </div>
    </li>
    {#/foreach#}
  </ul>
</div>
{#/if#}
{#if $uninstallArr|@count neq 0#}
<ul class="thead clearfix" style="position:relative; top:0; left:0; right:0; margin:10px;">
  <li class="row60 left">&nbsp;&nbsp;未安装</li>
  <li class="row20 left">作者</li>
  <li class="row20 left">操作</li>
</ul>
<div class="list mb50" style="margin-top:-20px;">
  <ul>
    {#foreach from=$uninstallArr item=install#}
    <li>
      <div class="tr clearfix">
        <div class="row60 left">&nbsp;&nbsp;&nbsp;<strong>{#$install.name#}</strong><sup>{#$install.version#}</sup>&nbsp;&nbsp;<a href="javascript:;" class="explain">说明</a></div>
        <div class="row20 left"><a href="{#$install.website#}" target="_blank">{#$install.author#}</a></div>
        <div class="row20 left"><a href="loginConnect.php?action=install&code={#$install.code#}" class="modify" data-title="{#$install.name#}" data-id="{#$install.code#}">安装</a></div>
        <div class="hide">{#$install.desc#}</div>
      </div>
    </li>
    {#/foreach#}
  </ul>
</div>
{#/if#}
<script>var adminPath = "{#$adminPath#}";</script>
{#$jsFile#}
</body>
</html>
