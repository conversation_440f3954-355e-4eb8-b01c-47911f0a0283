<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>隐私保护通话号码管理</title>
{#$cssFile#}
<style>
    #import {overflow: hidden; position: relative;}
    #Filedata {position: absolute; left: 0; top: 0; right: 0; bottom: 0; opacity: 0; filter: alpha(opacity=0); cursor: pointer;}
</style>
</head>

<body>
<div class="search">
  <label>搜索：
  <div class="btn-group" id="ctype" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">归属地<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部</a></li>
      {#foreach from=$cityNameArr item=city#}
      <li><a href="javascript:;" data-id="{#$city.cityCode#}">{#$city.cityName#} - <code>{#$city.cityCode#}</code></a></li>
      {#/foreach#}
    </ul>
  </div>
  </label>
  <input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的号码">&nbsp;&nbsp;
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <div class="btn-group hide" id="batchAudit">
        <button class="btn dropdown-toggle" data-toggle="dropdown">批量操作<span class="caret"></span></button>
        <ul class="dropdown-menu">
          <li><a href="javascript:;" data-id="正常">正常</a></li>
          <li><a href="javascript:;" data-id="锁定">锁定</a></li>
        </ul>
    </div>
    <button class="btn" data-toggle="dropdown" id="delBtn">删除</button>
    <div class="btn-group" id="stateBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">全部信息(<span class="totalCount"></span>)<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部信息(<span class="totalCount"></span>)</a></li>
        <li><a href="javascript:;" data-id="1">正常(<span class="normal"></span>)</a></li>
        <li><a href="javascript:;" data-id="2">锁定(<span class="lock"></span>)</a></li>
      </ul>
    </div>
    <div class="btn btn-warning" id="import"><span>导入号码</span><input type="file" accept=".csv" id="Filedata" name="Filedata"></div>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row15 left">号码</li>
  <li class="row15 left">归属地</li>
  <li class="row15 left">运营商</li>
  <li class="row15 left">绑定次数</li>
  <li class="row15 left">使用中</li>
  <li class="row15 left">状态</li>
  <li class="row7">删除</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="cityCode"></span>
</div>

<script>var adminPath = "{#$adminPath#}", type = "";</script>
{#$jsFile#}
</body>
</html>
