<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>手机底部导航</title>
{#$cssFile#}
<script>
    var thumbSize = {#$thumbSize#}, thumbType = "{#$thumbType#}", adminPath = "{#$adminPath#}", modelType = action = "{#$action#}", cfg_staticVersion = '{#$cfg_staticVersion#}';
    var platform = '{#$platform#}';
</script>
<style>
    .advanced {padding-bottom: 20px;}
    .modulelist {position: relative; float: left; margin-left: 20px; width: 183px;}
    .modulelist ul {padding: 0; margin: 0;}
    .modulelist li {width: 85px; float: left; height: 35px; line-height: 35px; background: #c4c4c4; font-size: 14px; margin: 0 1px 1px 0; list-style: none;}
    .modulelist li a {display: block; padding: 0 12px; color: #fff; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
    .modulelist li.current {background: #2c75e9;}
    /* .main {position: relative; overflow: hidden; padding-left: 15px;} */
    .main {margin-left: 210px;}

	.main img {width: 30px!important; height: 30px!important; display: block; margin: 0 auto;}
    .main .reupload {margin: 0 auto;}
    .main .table {width: 1050px;}
    .main .table th {min-width: 90px; height: 30px; text-align: center; line-height: 30px; font-size: 14px; font-weight: 500;}
    .main .table td {text-align: center; height: 34px; line-height: 31px;}
    .disabled .disabled {color: red;}
	.main ul {margin-bottom: 0;}
	.main li {list-style: none;}
	.thumb .uplogo {background: none!important;}

	.input-large {width: 270px;}
</style>
</head>

<body>
<div class="alert alert-success" style="margin:10px 90px 0 20px"><button type="button" class="close" data-dismiss="alert">×</button>
    图标尺寸：150×150像素，勾选【发布按钮】代表此按钮将以图标样式展示（不显示文字）。<br />
    跳转链接自定义参数说明：<a href="https://help.kumanyun.com/help-200-726.html" target="_blank">https://help.kumanyun.com/help-200-726.html</a><br />
    链接中不需要带城市目录，比如：https://ihuoniao.cn/sz/business&nbsp;&nbsp;&nbsp;只需要填写：https://ihuoniao.cn/business<br />
    {#if $platform == 'app'#}
    <font color="#ff0000">APP端首页由【<a href="app/appConfig.php" class="bottomButton">APP配置</a>】页面的风格模板控制！</font><br >
    APP端自定义链接参数，如：https://ihuoniao.cn?appTitle=1&hideAppMenu=1<br />
    &nbsp;&nbsp;&nbsp;&nbsp;<code>hideAppMenu</code> 隐藏右上角快捷菜单按钮<br />
    &nbsp;&nbsp;&nbsp;&nbsp;<code>appFullScreen</code> 页面全屏沉浸式显示<br />
    &nbsp;&nbsp;&nbsp;&nbsp;<code>appTitle</code> 强制显示原生标题栏<br />
    &nbsp;&nbsp;&nbsp;&nbsp;<code>currentPageOpen</code> 在当前页面打开链接<br />
    &nbsp;&nbsp;&nbsp;&nbsp;<code>appPage=info/shop/waimai/...</code> 同时勾选发布按钮选项，表示在新窗口打开模块首页<br />
    如果模块不想使用APP原生页面，选择H5即可！<br />
    {#elseif $platform == 'wxmini' || $platform == 'dymini'#}
    小程序原生页面的跳转链接直接以<code>/pages/xxx/xxx</code>填写即可，如果不知道原生页面路径，请联系售后获取。<br />
    {#/if#}
</div>

<ul class="nav nav-tabs" style="margin: 20px 20px 0;">
    <li{#if !$platform#} class="active"{#/if#}><a href="?action={#$action#}">H5端</a></li>
    {#if $has_app#}<li{#if $platform == 'app'#} class="active"{#/if#}><a href="?action={#$action#}&platform=app">APP端</a></li>{#/if#}
    {#if $has_wxmini#}<li{#if $platform == 'wxmini'#} class="active"{#/if#}><a href="?action={#$action#}&platform=wxmini">微信小程序端</a></li>{#/if#}
    {#if $has_dymini#}<li{#if $platform == 'dymini'#} class="active"{#/if#}><a href="?action={#$action#}&platform=dymini">抖音小程序端</a></li>{#/if#}
</ul>

<form action="?" method="post" name="editform" id="editform" class="advanced editform clearfix">
    <input type="hidden" name="action" value="{#$action#}" />
    <input type="hidden" name="token" id="token" value="{#$token#}" />
    <input type="hidden" name="resetType" id="resetType" value="" />
    <input type="hidden" name="platform" id="platform" value="{#$platform#}" />
    <div class="modulelist">
        <ul class="clearfix">
            <li{#if $action == 'siteConfig'#} class="current"{#/if#}><a href="?action=siteConfig&platform={#$platform#}">系统首页</a></li>
            <li{#if $action == 'business'#} class="current"{#/if#}><a href="?action=business&platform={#$platform#}">商家首页</a></li>
            {#foreach from=$moduleArr item=m#}
            <li{#if $action == $m.name#} class="current"{#/if#}><a href="?action={#$m.name#}&platform={#$platform#}" title="{#$m.title#}">{#$m.title#}</a></li>
            {#/foreach#}
        </ul>
    </div>

    <div class="main">

		<table class="table table-hover table-bordered table-striped">
		  <thead>
			<tr>
			  <th>名称</th>
			  <th>默认图标</th>
			  <th>选中图标</th>
			  <th>跳转链接</th>
			  <th style="min-width: 60px;">发布按钮</th>
			  <th style="min-width: 60px;">消息按钮</th>
			  {#if $platform == 'app'#}<th>原生页面</th>{#/if#}
			  <th style="min-width: 60px;">删除</th>
			</tr>
		  </thead>
		  <tbody>
			{#foreach from=$customBottomButton key=myId item=button#}
			<tr>
			  <td><input type="text" class="input-mini" name="bottomButton[name][{#$myId#}]" value="{#$button['name']#}" /></td>
			  <td class="thumb clearfix listImgBox">
				<div class="uploadinp filePicker thumbtn{#if $button['icon'] != ""#} hide{#/if#}" id="filePicker{#$myId#}" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
				{#if $button['icon'] != ""#}
				<ul id="listSection{#$myId#}" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_{#$myId#}"><a href='{#$cfg_attachment#}{#$button['icon']#}' target="_blank" title=""><img src="{#$cfg_attachment#}{#$button['icon']#}" data-val="{#$button['icon']#}"/></a><a class="reupload li-rm" href="javascript:;">重新上传</a></li></ul>
				{#else#}
				<ul id="listSection{#$myId#}" class="listSection thumblist fn-clear"></ul>
				{#/if#}
				<input type="hidden" name="bottomButton[icon][{#$myId#}]" value="{#$button['icon']#}" class="imglist-hidden">
			  </td>
			  <td class="thumb clearfix listImgBox">
				<div class="uploadinp filePicker thumbtn{#if $button['icon_h'] != ""#} hide{#/if#}" id="filePicker{#$myId#}_" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
				{#if $button['icon_h'] != ""#}
				<ul id="listSection{#$myId#}_" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_{#$myId#}_"><a href='{#$cfg_attachment#}{#$button['icon_h']#}' target="_blank" title=""><img src="{#$cfg_attachment#}{#$button['icon_h']#}" data-val="{#$button['icon_h']#}"/></a><a class="reupload li-rm" href="javascript:;">重新上传</a></li></ul>
				{#else#}
				<ul id="listSection{#$myId#}_" class="listSection thumblist fn-clear"></ul>
				{#/if#}
				<input type="hidden" name="bottomButton[icon_h][{#$myId#}]" value="{#$button['icon_h']#}" class="imglist-hidden">
			  </td>
			  <td>
                {#if $platform == 'wxmini' || $platform == 'dymini'#}
                <input class="input-large" type="text" name="bottomButton[url][{#$myId#}]" value="{#if $button['miniPath']#}{#$button['miniPath']#}{#else#}{#$button['url']#}{#/if#}" placeholder="链接地址">
                <input type="hidden" name="bottomButton[code][{#$myId#}]" value="{#$button['code']#}" />
                {#else#}
                <input class="input-large" type="text" name="bottomButton[url][{#$myId#}]" value="{#$button['url']#}" placeholder="链接地址">
                {#/if#}
				<!-- <div class="input-prepend input-append">
				  <span class="add-on">默认链接</span>
				  <input class="input-large" type="text" name="bottomButton[url][{#$myId#}]" value="{#$button['url']#}" placeholder="移动端默认链接">
				</div>
				<div class="input-prepend input-append">
				  <span class="add-on">小程序端</span>
				  <input class="input-large" type="text" name="bottomButton[miniPath][{#$myId#}]" value="{#$button['miniPath']#}" placeholder="跳转路径，如/pages/info/index，可留空">
				</div> -->
			  </td>
			  <td><input type="checkbox" name="bottomButton[fabu][{#$myId#}]" value="1" {#if $button.fabu==1#} checked{#/if#}></td>
			  <td><input type="checkbox" name="bottomButton[message][{#$myId#}]" value="1" {#if $button.message==1#} checked{#/if#}></td>
			  {#if $platform == 'app'#}
              <td>
                  <select class="input-small" name="bottomButton[code][{#$myId#}]"{#if $action == 'siteConfig' && $myId == 0#} style="display: none;"{#/if#}>
                      <option value="">H5</option>
                      {#if $touchTplList#}
                      {#foreach from=$touchTplList item=tplItem#}
                      <option value="{#$tplItem.directory#}" {#if $button['code'] == $tplItem.directory#}selected{#/if#}>{#$tplItem.tplname#}</option>
                      {#/foreach#}
                      {#/if#}
                  </select>
                  {#if $action == 'siteConfig' && $myId == 0#}
                  <a href="app/appConfig.php" class="bottomButton"><small>去配置</small></a>
                  {#/if#}
              </td>
              {#/if#}
			  <td><a href="javascript:;" class="del"><i class="icon-trash"></i></a></td>
			</tr>
			{#/foreach#}
		  </tbody>
		</table>

        {#if $customBottomButton && $customBottomButton|@count == 5#}
		<a href="javascript:;" class="btn btn-small addBtn disabled">最多5个按钮</a>
        {#else#}
		<a href="javascript:;" class="btn btn-small addBtn">+增加一个按钮</a>
        {#/if#}

        <dl class="formbtn">
            <dd style="padding-left: 0; margin-left: 0; max-width: 1055px; text-align: right;">

                {#if $platform != ''#}
                <div class="btn-group operBtn dropup" id="resetAll" style="float: left; text-align: left; margin-top: 10px;">
                    <button type="button" class="btn dropdown-toggle" data-toggle="dropdown">重置<font color="#f83824"><u>所有模块</u></font>链接<span class="caret"></span></button>
                    <ul class="dropdown-menu">

                      <li><a href="javascript:;" data-id="">恢复系统默认</a></li>
                      <li><a href="javascript:;" data-id="h5">复制H5端配置</a></li>

                      {#if $platform == 'wxmini'#}
                      <li><a href="javascript:;" data-id="dymini">复制抖音小程序端配置</a></li>
                      {#/if#}

                      {#if $platform == 'dymini'#}
                      <li><a href="javascript:;" data-id="wxmini">复制微信小程序端配置</a></li>
                      {#/if#}
                    </ul>
                </div>
                {#else#}
                <a href="javascript:;" class="btn" id="resetAll" style="float: left; margin-top: 10px;">重置<font color="#f83824"><u>所有模块</u></font>链接</a>
                {#/if#}
				<!-- <a href="javascript:;" class="btn-link btn" id="resetAll" style="float: left; margin-left: -10px; color: #b94a48;">重置所有模块链接</a> -->

                {#if $platform != ''#}
                <div class="btn-group operBtn dropup" id="resetCurrent" style="float: left; text-align: left; margin-left: 20px; margin-top: 10px;">
                    <button type="button" class="btn dropdown-toggle" data-toggle="dropdown">重置<font color="#f83824"><u>当前模块</u></font>链接<span class="caret"></span></button>
                    <ul class="dropdown-menu">

                      <li><a href="javascript:;" data-id="">恢复系统默认</a></li>
                      <li><a href="javascript:;" data-id="h5">复制H5端配置</a></li>

                      {#if $platform == 'wxmini'#}
                      <li><a href="javascript:;" data-id="dymini">复制抖音小程序端配置</a></li>
                      {#/if#}

                      {#if $platform == 'dymini'#}
                      <li><a href="javascript:;" data-id="wxmini">复制微信小程序端配置</a></li>
                      {#/if#}
                    </ul>
                </div>
                {#else#}
                <a href="javascript:;" class="btn" id="resetCurrent" style="float: left; margin-left: 20px; margin-top: 10px;">重置<font color="#f83824"><u>当前模块</u></font>链接</a>
                {#/if#}

				<!-- <a href="javascript:;" class="btn-link btn" id="resetCurrent" style="margin-right: 15px; color: #b94a48;">重置当前模块链接</a> -->
				<input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认保存" />
			</dd>
        </dl>
    </div>
</form>

{#$editorFile#}
{#$jsFile#}
</body>
</html>
