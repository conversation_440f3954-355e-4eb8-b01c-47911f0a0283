<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var atlasSize = {#$atlasSize#}, atlasType = "{#$atlasType#}", atlasMax = 0;  //图集配置
var ad_class = {#$ad_class#}, action = modelType = '{#$action#}', adminPath = "{#$adminPath#}", aid = {#$aid#}, cityid = {#$cityid#}, cityList = {#$cityList#};
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="module">城市：</label></dt>
    <dd style="overflow: visible; padding-left: 140px;">
      <select class="chosen-select" id="cityid" name="cityid" style="width: auto; min-width: 150px;"></select>
    </dd>
  </dl>
  <div id="class1" class="hide">
    <dl class="clearfix hide">
      <dt>展现方式：</dt>
      <dd>
        <label><input type="radio" name="style" value="code" checked="checked" />代码</label>&nbsp;&nbsp;
        <label><input type="radio" name="style" value="text" />文字</label>&nbsp;&nbsp
        <label><input type="radio" name="style" value="pic" />图片</label>&nbsp;&nbsp
        <label><input type="radio" name="style" value="flash" />Flash</label>&nbsp;&nbsp
				<span class="input-tips" style="display: inline-block;"><s></s>提交后，类型不可修改！</span>
      </dd>
    </dl>
    <div id="style_code">
      <dl class="clearfix">
        <dt><label for="codehtml">HTML代码：</label></dt>
        <dd><textarea name="codehtml" id="codehtml" class="input-xxlarge" rows="5"></textarea></dd>
      </dl>
    </div>
    <div id="style_text" class="hide">
      <dl class="clearfix">
        <dt><label for="texttitle">文字内容：</label></dt>
        <dd>
          <input class="input-large" type="text" name="texttitle" id="texttitle" />
          <div class="color_pick"><em style="background:{#$color#};"></em></div>
          <input type="hidden" name="color" id="color" value="{#$color#}" />
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="textlink">文字链接：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="textlink" id="textlink" />
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="textsize">文字大小：</label></dt>
        <dd>
          <input class="input-mini" type="number" min="0" name="textsize" id="textsize" />px
        </dd>
      </dl>
    </div>
    <div id="style_pic" class="hide">
      <dl class="clearfix">
        <dt>上传图片：</dt>
				<dd class="thumb clearfix listImgBox">
					<div class="uploadinp filePicker thumbtn{#if $class1pic != ""#} hide{#/if#}" id="filePicker1" data-type="advthumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
					<ul id="listSection1" class="listSection thumblist clearfix"></ul>
					<input type="hidden" name="class1pic" value="{#$class1pic#}" class="imglist-hidden" id="class1pic">
				</dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="piclink">图片链接：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="piclink" id="piclink" />
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="picalt">替换文字：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="picalt" id="picalt" />
        </dd>
      </dl>
      <dl class="clearfix">
        <dt>图片尺寸：</dt>
        <dd>
          <div class="input-prepend input-append">
            <span class="add-on">宽</span>
            <input class="span1" id="class1picwidth" name="class1picwidth" type="text" value="" />
            <span class="add-on">px</span>
          </div>
          &times;
          <div class="input-prepend input-append">
            <span class="add-on">高</span>
            <input class="span1" id="class1picheight" name="class1picheight" type="text" value="" />
            <span class="add-on">px</span>
          </div>
        </dd>
      </dl>
    </div>
    <div id="style_flash" class="hide">
      <dl class="clearfix">
        <dt>上传Flash：</dt>
        <dd>
          <input name="class1flash" type="hidden" id="class1flash" value="" />
          <div class="spic hide">
            <div class="sholder"></div>
            <a href="javascript:;" class="reupload">重新上传</a>
          </div>
          <iframe src ="/include/upfile.inc.php?mod={#$action#}&type=adv&obj=class1flash&filetype=flash" style="width:100%; height:25px;" scrolling="no" frameborder="0" marginwidth="0" marginheight="0" ></iframe>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt>Flash尺寸：</dt>
        <dd>
          <div class="input-prepend input-append">
            <span class="add-on">宽</span>
            <input class="span1" id="flashwidth" name="flashwidth" type="text" value="" />
            <span class="add-on">px</span>
          </div>
          &times;
          <div class="input-prepend input-append">
            <span class="add-on">高</span>
            <input class="span1" id="flashheight" name="flashheight" type="text" value="" />
            <span class="add-on">px</span>
          </div>
        </dd>
      </dl>
    </div>
	  <dl class="clearfix">
	    <dt><label for="mark1">广告标识：</label></dt>
	    <dd class="radio">
				<label><input type="radio" name="mark1" value="1" />隐藏</label>&nbsp;&nbsp
				<label><input type="radio" name="mark1" value="0" checked="checked" />显示</label>&nbsp;&nbsp;
	    </dd>
	  </dl>
  </div>
  <div id="class2" class="hide">
    <dl class="clearfix">
      <dt>图片尺寸：</dt>
      <dd>
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="class2picwidth" name="class2picwidth" type="text" value="" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="class2picheight" name="class2picheight" type="text" value="" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>上传图片：</dt>
      <dd class="listImgBox hide">
        <div class="list-holder">
          <ul id="listSection2" class="clearfix listSection"></ul>
          <input type="hidden" id="imglist1" value='' class="imglist-hidden">
        </div>
        <div class="btn-section clearfix">
          <div class="uploadinp filePicker" id="filePicker2" data-type="adv" data-count="999" data-size="{#$atlasSize#}" data-imglist=""><div id="flasHolder"></div><span>添加图片</span></div>
          <div class="upload-tip">
            <p><a href="javascript:;" class="hide deleteAllAtlas">删除所有</a>&nbsp;&nbsp;{#$atlasType|replace:"*.":""|replace:";":"、"#}&nbsp;&nbsp;单张最大{#$atlasSize/1024#}M<span class="fileerror"></span></p>
          </div>
        </div>
      </dd>
    </dl>
  </div>
  <div id="class3" class="hide">
    <dl class="clearfix">
      <dt><label for="showtime">显示时间：</label></dt>
      <dd>
        <input class="input-mini" type="number" min="0" name="showtime" id="showtime" />秒
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="adwidth">广告宽度：</label></dt>
      <dd><input class="input-mini" type="number" min="0" name="adwidth" id="adwidth" />px</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="extrulink">广告链接：</label></dt>
      <dd>
        <input class="input-xlarge" type="text" name="extrulink" id="extrulink" />
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>大图：</dt>
			<dd class="thumb clearfix listImgBox">
				<div class="uploadinp filePicker thumbtn{#if $class1pic != ""#} hide{#/if#}" id="filePicker3" data-type="advthumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
				<ul id="listSection3" class="listSection thumblist clearfix"></ul>
				<input type="hidden" name="class3bigpic" value="{#$class3bigpic#}" class="imglist-hidden" id="class3bigpic">
			</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="bigheight">大图高度：</label></dt>
      <dd><input class="input-mini" type="number" min="0" name="bigheight" id="bigheight" />px</dd>
    </dl>
    <dl class="clearfix">
      <dt>小图：</dt>
			<dd class="thumb clearfix listImgBox">
				<div class="uploadinp filePicker thumbtn" id="filePicker4" data-type="advthumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
				<ul id="listSection4" class="listSection thumblist clearfix"></ul>
				<input type="hidden" name="class3smallpic" value="{#$class3smallpic#}" class="imglist-hidden" id="class3smallpic">
			</dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="smallheight">小图高度：</label></dt>
      <dd><input class="input-mini" type="number" min="0" name="smallheight" id="smallheight" />px</dd>
    </dl>
	  <dl class="clearfix">
	    <dt><label for="mark3">广告标识：</label></dt>
	    <dd class="radio">
				<label><input type="radio" name="mark3" value="1" />隐藏</label>&nbsp;&nbsp
				<label><input type="radio" name="mark3" value="0" checked="checked" />显示</label>&nbsp;&nbsp;
	    </dd>
	  </dl>
  </div>
  <div id="class4" class="hide">
    <dl class="clearfix">
      <dt><label for="bodywidth">页面宽度：</label></dt>
      <dd>
        <input class="input-mini" type="number" min="0" name="bodywidth" id="bodywidth" />px
        <span class="help-inline">如果窗口大小小于此宽度，广告将不显示</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>广告尺寸：</dt>
      <dd>
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="adwidth_" name="adwidth_" type="text" value="" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="adheight_" name="adheight_" type="text" value="" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="topheight">距离顶部：</label></dt>
      <dd><input class="input-mini" type="number" min="0" name="topheight" id="topheight" />px</dd>
    </dl>
    <div class="row-fluid">
      <div class="span6">
        <p style="padding-left:50px;"><strong>左边：</strong></p>
        <dl class="clearfix">
          <dt>上传图片：</dt>
					<dd class="thumb clearfix listImgBox">
						<div class="uploadinp filePicker thumbtn" id="filePicker5" data-type="advthumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
						<ul id="listSection5" class="listSection thumblist clearfix"></ul>
						<input type="hidden" name="class4leftpic" value="{#$class4leftpic#}" class="imglist-hidden" id="class4leftpic">
					</dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="leftpiclink">广告链接：</label></dt>
          <dd>
            <input class="input-xlarge" type="text" name="leftpiclink" id="leftpiclink" />
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="leftpicalt">替换文字：</label></dt>
          <dd>
            <input class="input-xlarge" type="text" name="leftpicalt" id="leftpicalt" />
          </dd>
        </dl>
				<dl class="clearfix">
			    <dt><label for="markLeft">广告标识：</label></dt>
			    <dd class="radio">
						<label><input type="radio" name="markLeft" value="1" />隐藏</label>&nbsp;&nbsp
						<label><input type="radio" name="markLeft" value="0" checked="checked" />显示</label>&nbsp;&nbsp;
			    </dd>
			  </dl>
      </div>
      <div class="span6">
        <p style="padding-left:50px;"><strong>右边：</strong></p>
        <dl class="clearfix">
          <dt>上传图片：</dt>
					<dd class="thumb clearfix listImgBox">
						<div class="uploadinp filePicker thumbtn" id="filePicker6" data-type="advthumb"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
						<ul id="listSection6" class="listSection thumblist clearfix"></ul>
						<input type="hidden" name="class4rightpic" value="{#$class4rightpic#}" class="imglist-hidden" id="class4rightpic">
					</dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="rightpiclink">广告链接：</label></dt>
          <dd>
            <input class="input-xlarge" type="text" name="rightpiclink" id="rightpiclink" />
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="rightpicalt">替换文字：</label></dt>
          <dd>
            <input class="input-xlarge" type="text" name="rightpicalt" id="rightpicalt" />
          </dd>
        </dl>
				<dl class="clearfix">
			    <dt><label for="markRight">广告标识：</label></dt>
			    <dd class="radio">
						<label><input type="radio" name="markRight" value="1" />隐藏</label>&nbsp;&nbsp
						<label><input type="radio" name="markRight" value="0" checked="checked" />显示</label>&nbsp;&nbsp;
			    </dd>
			  </dl>
      </div>
    </div>
  </div>
  <div id="class5" class="hide">
    <dl class="clearfix">
      <dt>头部高度：</dt>
      <dd>
        <div class="input-prepend input-append">
          <input class="span1" id="class5headHeight" name="class5headHeight" type="text" value="" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>图片链接：</dt>
      <dd>
        <div class="input-prepend input-append">
          <input class="input-xlarge" id="class5picurl" name="class5picurl" type="text" value="" />
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt>上传图片：</dt>
      <dd class="thumb clearfix listImgBox">
        <div class="uploadinp filePicker thumbtn{#if $class1pic != ""#} hide{#/if#}" id="filePicker10" data-type="holidayadv"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
        <ul id="listSection10" class="listSection thumblist clearfix"></ul>
        <input type="hidden" name="litpic" value="" class="imglist-hidden" id="litpic">
      </dd>
    </dl>
  </div>
  <div id="class6" class="hide">
    <dl class="clearfix">
      <dt>广告内容：</dt>
      <dd>
		<script id="notifyAdv" name="notifyAdv" type="text/plain" style="width:85%;height:400px"></script>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="notifyLink">广告链接：</label></dt>
      <dd>
        <input class="input-xlarge" type="text" name="notifyLink" id="notifyLink" />
      </dd>
    </dl>
  </div>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
</form>

<div id="ad_class" class="hide">{#$ad_class#}</div>
<div id="ad_Body" class="hide">{#$ad_body#}</div>
<div id="adBody" class="hide">{#$body#}</div>
<div id="type1" class="hide">{#$type1#}</div>
<div id="type2" class="hide">{#$type2#}</div>
{#$editorFile#}
{#$jsFile#}
</body>
</html>
