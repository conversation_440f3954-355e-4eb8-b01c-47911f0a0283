<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>{#$pagetitle#}</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="dopost" id="dopost" value="{#$dopost#}" />
  <input type="hidden" name="id" id="id" value="{#$id#}" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <dl class="clearfix">
    <dt><label for="title">消息名称：</label></dt>
    <dd>
      <input class="input-xlarge" type="text" name="title" id="title" data-regex=".{1,}" value="{#$title#}"{#if $system == 1#} readonly{#/if#} />
      <span class="input-tips"><s></s>请输入消息名称</span>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt>通知方式：</dt>
    <dd>
        <ul class="nav nav-tabs">
            <li class="active"><a href="#tab0">电子邮件</a></li>
            <li><a href="#tab1">手机短信</a></li>
            <li><a href="#tab2">微信公众号</a></li>
            <li><a href="#tab3">网页即时消息</a></li>
            <li><a href="#tab4">APP推送</a></li>
        </ul>
        <div class="tagsList">
            <div class="tag-list" id="tab0">
                <dl class="clearfix">
                  <dt><label for="email_state">状态：</label></dt>
                  <dd class="radio">
                    <label><input type="checkbox" id="email_state" name="email_state" value="1"{#if $email_state == 1#} checked{#/if#} /> 启用</label>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="email_title">邮件标题：</label></dt>
                  <dd>
                    <input class="input-xxlarge" type="text" name="email_title" id="email_title" value="{#$email_title#}" />
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="email_body">邮件模板：</label></dt>
                  <dd>
                    <textarea name="email_body" id="email_body" class="input-xxlarge" rows="10">{#$email_body#}</textarea>
                  </dd>
                </dl>
            </div>
            <div class="tag-list hide" id="tab1">
                <dl class="clearfix">
                  <dt><label for="sms_state">状态：</label></dt>
                  <dd class="radio">
                    <label><input type="checkbox" id="sms_state" name="sms_state" value="1"{#if $sms_state == 1#} checked{#/if#} /> 启用</label>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="sms_tempid">平台模板ID：</label></dt>
                  <dd>
                    <input class="input-xlarge" type="text" name="sms_tempid" id="sms_tempid" value="{#$sms_tempid#}" />
                    {#if $id && $cfg_smsAlidayu == 1#}
                    &nbsp;&nbsp;<a class="btn btn-small" href="javascript:;" id="syncSmsTemplate">一键导入</a>
                    {#/if#}
                    &nbsp;&nbsp;<a href="https://help.kumanyun.com/help-48-6.html" target="_blank">配置教程</a>
                    <span class="input-tips" style="display: inline-block;"><s></s>阿里大于、阿里云、腾讯云短信模板ID必填</span>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="sms_tempid">国际模板ID：</label></dt>
                  <dd>
                    <input class="input-xlarge" type="text" name="sms_intempid" id="sms_intempid" value="{#$sms_intempid#}" />
                    &nbsp;&nbsp;
                    <span class="input-tips" style="display: inline-block;"><s></s>阿里大于、阿里云、腾讯云短信模板ID必填</span>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="sms_body">短信模板：</label></dt>
                  <dd>
                    <textarea name="sms_body" id="sms_body" class="input-xxlarge" rows="10">{#$sms_body#}</textarea>
                  	<span class="input-tips" style="display: inline-block;"><s></s>阿里大于、阿里云、腾讯云短信模板不必修改</span>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="sms_note">申请说明：</label></dt>
                  <dd>
                    <textarea name="sms_note" id="sms_note" class="input-xxlarge" rows="2">{#$sms_note#}</textarea>
                  	<span class="input-tips" style="display: inline-block;"><s></s>用于接口自动申请阿里云、腾讯云短信模板，<a href="https://help.kumanyun.com/help-48-6.html" target="_blank">参考说明</a></span>
                  </dd>
                </dl>
            </div>
            <div class="tag-list hide" id="tab2">
                <dl class="clearfix">
                  <dt><label for="wechat_state">状态：</label></dt>
                  <dd class="radio">
                    <label><input type="checkbox" id="wechat_state" name="wechat_state" value="1"{#if $wechat_state == 1#} checked{#/if#} /> 启用</label>
                    <span class="input-tips input-error" style="display: inline-block; color: #FF4F38;"><s></s>微信公众平台已下线该功能，已经申请过消息模板的暂不受影响。</span>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="wechat_tempid">微信模板ID：</label></dt>
                  <dd>
                    <input class="input-xxlarge" type="text" name="wechat_tempid" id="wechat_tempid" value="{#$wechat_tempid#}" />
                    &nbsp;&nbsp;<a href="https://help.kumanyun.com/help-48-7.html" target="_blank">配置教程</a>
                  </dd>
                </dl>
                <dl class="clearfix">
                    <dt><label for="sms_tempid">微信模板编号：</label></dt>
                    <dd>
                        <input class="input-xlarge" type="text" name="wechat_serial" id="wechat_serial" value="{#$wechat_serial#}" />
                        &nbsp;&nbsp;{#*<a class="btn btn-small" href="javascript:;" id="syncSmsWeixinTemplate">一键导入</a>*#}
                        <span class="input-tips" style="display: inline-block;"><s></s>微信模板编号必填</span>
                    </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="wechat_body">微信模板配置：</label></dt>
                  <dd>
                    <textarea name="wechat_body" id="wechat_body" class="input-xxlarge" rows="10">{#$wechat_body#}</textarea>
                  </dd>
                </dl>
            </div>
            <div class="tag-list hide" id="tab3">
                <dl class="clearfix">
                  <dt><label for="site_state">状态：</label></dt>
                  <dd class="radio">
                    <label><input type="checkbox" id="site_state" name="site_state" value="1"{#if $site_state == 1#} checked{#/if#} /> 启用</label>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="site_title">消息标题：</label></dt>
                  <dd>
                    <input class="input-xxlarge" type="text" name="site_title" id="site_title" value="{#$site_title#}" />
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="site_body">消息内容：</label></dt>
                  <dd>
                    <textarea name="site_body" id="site_body" class="input-xxlarge" rows="10">{#$site_body#}</textarea>
                  </dd>
                </dl>
            </div>
            <div class="tag-list hide" id="tab4">
                <dl class="clearfix">
                  <dt><label for="app_state">状态：</label></dt>
                  <dd class="radio">
                    <label><input type="checkbox" id="app_state" name="app_state" value="1"{#if $app_state == 1#} checked{#/if#} /> 启用</label>
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="app_title">推送标题：</label></dt>
                  <dd>
                    <input class="input-xxlarge" type="text" name="app_title" id="app_title" value="{#$app_title#}" />
                  </dd>
                </dl>
                <dl class="clearfix">
                  <dt><label for="app_body">推送内容：</label></dt>
                  <dd>
                    <textarea name="app_body" id="app_body" class="input-xxlarge" rows="10">{#$app_body#}</textarea>
                  </dd>
                </dl>
            </div>
        </div>
    </dd>
  </dl>
  <dl class="clearfix">
    <dt><label for="state">状态：</label></dt>
    <dd class="radio">
      <label><input type="checkbox" id="state" name="state" value="1"{#if $state == 1#} checked{#/if#} /> 启用</label>
    </dd>
  </dl>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><button class="btn btn-large btn-success" type="submit" name="button" id="btnSubmit">确认提交</button></dd>
  </dl>
  <dl class="clearfix" style="margin-top: 50px;">
    <dt>系统公共参数：</dt>
    <dd class="systemLabel">
        <div class="input-prepend input-append">
          <span class="add-on">网站域名：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$basehost">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">网站名称：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$webname">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">网站简称：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$shortname">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">网站Logo：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$weblogo">
        </div><br />
        <div class="input-prepend input-append">
          <span class="add-on">400电话：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$hotline">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">当前时间：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$time">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">验证码：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$code">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">用户名：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$username">
        </div><br />
        <div class="input-prepend input-append">
          <span class="add-on">信息标题：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$title">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">信息URL：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$url">
        </div>
        <div class="input-prepend input-append">
          <span class="add-on">状态：</span>
          <input class="span2" disabled style="cursor: text; background: #fff;" type="text" value="$status">
        </div>

        <br />
        <p class="text-warning"><small>请根据实际情况使用系统公共参数！</small></p>
    </dd>
  </dl>
</form>

{#$jsFile#}
</body>
</html>
