<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>生成静态页面</title>
{#$cssFile#}
<style media="screen">
    body {padding: 20px;}
    .bs-docs-example {position: relative; margin: 15px 0; padding: 80px 19px 60px; *padding-top: 19px; background-color: #fff; border: 1px solid #ddd; -webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 4px; text-align: center;}
    .bs-docs-example:after {content: "页面选项"; position: absolute; top: -1px; left: -1px; padding: 3px 7px; font-size: 12px; font-weight: bold; background-color: #f5f5f5; border: 1px solid #ddd; color: #9da0a4; -webkit-border-radius: 4px 0 4px 0; -moz-border-radius: 4px 0 4px 0; border-radius: 4px 0 4px 0;}
    .bs-docs-example p {margin: 0; text-align: center; padding: 20px 0;}
    .bs-docs-example a.btn-large {display: inline-block; width: 300px;}
</style>
</head>

<body>

<div class="alert alert-success" style="margin:0 0 15px!important;"><button type="button" class="close" data-dismiss="alert">×</button>生成静态页面可以有效提升电脑端网站打开速度！<br />如果生成的页面有问题，可以在清除页面缓存中勾选（纯静态页面）删除生成的文件，或者手动删除网站根目录/templates_c/html/中的文件；<br />生成后的页面将不提供管理员登录状态下的广告位名称外显效果，如果需要该提示，可以在地址栏中手动增加csp=1的参数，如：https://ihuoniao.cn/sz/news?csp=1<br /><font color="red">注意：由于涉及分站切换功能，在生成的过程中，请停止当前浏览器的任何操作，包括访问前台页面，建议使用其他浏览器或者当前浏览器的无痕模式进行操作！</font></div>

<div class="bs-docs-example form-horizontal">
    <p>
        <a class="btn btn-large btn-info" href="?action=index">自动生成所有分站首页({#$siteCityList|count#}个)</a>&nbsp;&nbsp;&nbsp;&nbsp;-<a class="btn btn-link createSiteIndex" href="javascript:;">手动生成指定分站首页</a>
    </p>
    <p>
        <a class="btn btn-large btn-success" href="?action=moduleIndex">自动生成所有模块首页({#$moduleIndexCount#}个)</a>&nbsp;&nbsp;&nbsp;&nbsp;-<a class="btn btn-link createModuleIndex" href="javascript:;">手动生成指定模块首页</a>
    </p>
</div>

<script id="siteIndexObj" type="text/html">
    <form action="" class="quick-editForm" name="editForm">
      <dl class="clearfix">
        <dt></dt>
        <dd style="padding: 20px 0;">
          <select id="cityid" class="chosen-select">
            <option value="">请选择分站</option>
            {#foreach from=$siteCityList item=city#}
            <option value="{#$city.cityid#}">{#$city.pinyin|strtoupper|truncate:1:""#} - {#$city.name#}</option>
            {#/foreach#}
          </select>
        </dd>
      </dl>
    </form>
</script>

<script id="moduleIndexObj" type="text/html">
    <form action="" class="quick-editForm" name="editForm">
      <dl class="clearfix">
        <dt></dt>
        <dd style="padding: 20px 0;">
          <select id="moduleid" class="chosen-select">
            <option value="">请选择模块</option>
            {#foreach from=$moduleArr item=module#}
            <option value="{#$module.name#}">{#$module.title#}</option>
            {#/foreach#}
          </select>
        </dd>
      </dl>
    </form>
</script>

<script>
  var adminPath = "{#$adminPath#}";
</script>
{#$jsFile#}
</body>
</html>
