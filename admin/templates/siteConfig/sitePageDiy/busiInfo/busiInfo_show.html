<!-- 会员组件 -->
<div :class="['memberInfoBox ' + modCon.content.themeType + 'Theme']">
    <div class="memberBg" :style="`height:${modCon.content.style.bgHeight / 2}px; background-color:${modCon.content.bgType == 'color' ? modCon.content.style.bgColor : ''};`"> 
        <img  crossOrigin='anonymous'  :src="`${modCon.content.style.bgImage}?v=${Math.random()}`" alt="" v-if="modCon.content.bgType == 'image'">
    </div>
    <div class="memberInfo" >
        
        <div class="flex_header">
            <div class="headerBtn flexBet" v-if="modCon.content.showHeader || currPlatform.id == 'wxmini'" :style="`padding-left:${modCon.content.style.marginLeft / 2}px; padding-right:${modCon.content.style.marginLeft / 2}px;`">
                <!-- 左上 个人版 -->
                <div class="flex_l">
                    <div class="icon">
                        <svg class="filter" height="0" width="0" xmlns="http://www.w3.org/2000/svg" v-if="modCon.content.themeType == 'light'">
                            <defs>
                                <filter id="bicon">
                                    <feColorMatrix type="matrix" :values="checkSvgColor('#ffffff')" />
                                </filter>
                            </defs>
                        </svg>
                        <img style="filter:url(#bicon)" :src="modCon.content.business.icon" alt="">
                    </div>
                    <div class="txt">{{modCon.content.business.text}}</div>
                </div>
                <!-- 右上 按钮 -->
                <div class="flex_r" v-if="modCon.content.rtBtns.btns.length">
                    <div class="ul flexbox">
                        <div :class="['li',{'smText':modCon.content.rtBtns.txtStyle == 1}]" v-for="btn in modCon.content.rtBtns.btns">
                            <div class="icon">
                                <img  :src="btn.icon || btnDefault2" onerror="this.src = '/static/images/admin/siteMemberPage/default_icon2'" alt="">
                            </div>
                            <div class="text" v-if="modCon.content.rtBtns.txtStyle">{{btn.text || '按钮'}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wx_btn" v-if="currPlatform.id == 'wxmini'"><img src="{#$cfg_basehost#}/static/images/admin/wx_btn.png" alt=""></div>
        </div>
        <div class="infobox flexBet" :style="`padding-left:${modCon.content.style.marginLeft / 2}px; padding-right:${currPlatform.id != 'wxmini' ? modCon.content.style.marginLeft / 2 : 18}px;`">
            <div class="info_member flexbox">
                <div class="photo"><img src="/static/images/admin/siteConfigPage/defaultImg/busi_logo.png" alt=""></div>
                <div class="nickname">
                    <div class="h4">承德汽车租赁有限公司</div>
                    <div :class="['vipbox flexbox',{'noVip':!showVipSet}]" >
                        <div class="txt">商家会员</div>
                        <div class="line"></div>
                        <div class="txt">{{showVipSet ? "已开通" : "未开通"}}</div>
                    </div>
                </div>
            </div>
            <div class="flex_r" v-if="showVipSet" style="flex-direction: column;">
                <div :class="'btn btn'+ modCon.content.rightInfo.btnStyle +' flexbox ' + (modCon.content.rightInfo.btn.txtStyle == 1 ? 'flexColumn' : '')" :style="modCon.content.rightInfo.btnStyle == 3 ? `background:${modCon.content.rightInfo.btn.style.btnBgColor || '#ff6a4d'};border:solid ${modCon.content.rightInfo.btn.style.borderSize / 2}px ${modCon.content.rightInfo.btn.style.bordeColor};` : ''">
                    <div class="icon" v-if="modCon.content.rightInfo.btnStyle > 1">
                        <svg class="filter" height="0" width="0" xmlns="http://www.w3.org/2000/svg" v-if="modCon.content.rightInfo.btn.style.iconColor">
                            <defs>
                                <filter id="scoreIocn">
                                    <feColorMatrix type="matrix" :values="checkSvgColor(modCon.content.rightInfo.btn.style.iconColor)" />
                                </filter>
                            </defs>
                        </svg>
                        <img crossOrigin='anonymous' style="filter:url(#scoreIocn)" :src="modCon.content.rightInfo.btn.icon || btnDefault2 " alt=""/>
                    </div>

                    <template v-if="modCon.content.rightInfo.btnStyle != 4 || modCon.content.rightInfo.btn.txtStyle">
                        <div class="txt" v-if="modCon.content.rightInfo.btnStyle != 2"  :style="`color:${checkBgColor(modCon.content.rightInfo.btn.style.textColor,modCon.content.rightInfo.btn.style.text_opacity)};`">{{ modCon.content.rightInfo.btn.text }}</div>
                        <div class="txt score" v-else  :style="`color:${checkBgColor(modCon.content.rightInfo.btn.style.textColor,modCon.content.rightInfo.btn.style.text_opacity)};`">
                            <b>4.9</b><span>分</span>
                        </div>

                    </template>

                    <div class="arr" v-if="modCon.content.rightInfo.btnStyle <= 1">
                        <img   :style="modCon.content.rightInfo.btn.style.iconColor ? `filter: drop-shadow(15px 0 0 ${checkBgColor(modCon.content.rightInfo.btn.style.iconColor,modCon.content.rightInfo.btn.style.icon_opacity)}); transform: translateX(-100%);`: ''" src="{#$cfg_basehost#}/static/images/admin/siteConfigPage/arr_busi.png" alt="">
                    </div>
                    
                </div>
                <!-- btnPlace是让样式4 的按钮 升高 -->
                <div class="btnPlace" style="height: 15px; width: 1px;" v-if="modCon.content.rightInfo.btnStyle == 4 && modCon.content.rightInfo.btn.txtStyle != 1"></div>
            </div>
        </div>
    </div>
</div>