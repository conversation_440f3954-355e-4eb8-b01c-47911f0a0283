<!-- 瓷片广告 s-->
<div :class="['rightCon',{'show':currEditPart === 6}] " v-cloak>
    <div class="financeContainer cipianBox modBox" >
        <div class="title" v-if="currEditPart === 6">瓷片广告
            
            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" v-if="!checkHasIn('adv',6)">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('adv')"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end" v-if="checkHasIn('adv',6)">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('adv',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">恢复上次保存</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 6 && showResetPop" v-if="checkModelHasIn() && checkCompInModel()" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复成模板样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="reChangeToModel('adv',6);pageChange()"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置模板样式</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 6 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('adv',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>

        </div>
        <div class="financeStyle">
            <div class="modstyleBox">
                <h2 class="stitle">组件设置</h2>
                <div class="column_chose">
                    <s :style="'left:'+ offsetLeft +'px;'"></s>
                    <span v-for="item in 3" :class="['item_' + item]" @click="choseAdvType(item)"><em v-for="i in item"></em></span>

                </div>
            </div>
            <div class="modstyleBox contentSetBox">
                <h2 class="stitle">内容设置</h2>
                <div class="sContent">
                    <div  class="btnsBox btn_item sortBox"  data-drag=".item" data-sort="advFormData.list" data-filter="input">
                        <div class="option_item item" v-for="(btn,ind) in (advFormData.list).slice(0,advFormData.column == 1 ? advFormData.list.length : advFormData.column)" :data-id="ind" :key="btn.image">
                            <s class="del_item"  v-if="advFormData.column === 1 && advFormData.list.length > 1" @click="advFormData.list.splice(ind,1)"></s>
                            <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                            <div class="item_con">
                                <div class="img_up fn-clear">
                                    <div :class="['upbtn fullW',{'hasup':btn.image}]">
                                        <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp"  :id="'adv_'+ind+'_filedata'" @change="fileInpChange(`advFormData.list.${ind}.image`)" :data-type="'adv_'+ind" >
                                        <img v-show="btn.image" :src="btn.image" alt="">
                                        <span>更换图片</span>
                                    </div>
                                    <div class="imgText">
                                        <h4>广告图片</h4>
                                        <p v-if="advFormData.column === 3">建议宽度400px</p>
                                        <p v-else-if="advFormData.column === 2">建议宽度600px</p>
                                        <p v-else-if="advFormData.column === 1">建议宽度1080px</p>
                                    </div>
                                </div>
                                <div class="inpbox linkbox linkChose" style="margin-top: 10px;">
                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/'+(btn.linkInfo && btn.linkInfo.selfSetTel ? '/siteMemberPage/tel' : 'link')+'.png'" alt=""></s>
                                    <input type="text" placeholder="请选择链接" class="iconLink"  :value="solveShowtext(btn.linkInfo,btn.link)" readonly @click="changeLink('advFormData.list.' + ind +'.link' )">
                                </div>
                            </div>
                        </div>
                        <a href="javascript:;" class="add_more" @click="addMoreBtn('adv')" v-show="advFormData.column === 1"><s></s>添加广告</a>
                    </div>
                </div>
            </div>

            <!-- 间距设置 -->
            <div class="marginSetBox  modstyleBox contentSetBox" >
                <h2 class="stitle">间距设置</h2>
                <el-collapse-transition>
                    <div class="style_item" v-show="showMarginSet">
                        <div class="sContent">
                            <div class="style_options">
                                <label for="">圆角值：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="advFormData.style.borderRadius"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="advFormData.style.borderRadius"  @input="checkMax(0,50,'advFormData.style.borderRadius')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options" >
                                <label for="">广告高度：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :step="4" :min="100" :max="300" v-model="advFormData.style.height"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  :value="advFormData.style.height"  @change="checkMax(100,300,'advFormData.style.height')" >
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options" v-if="advFormData.column > 1">
                                <label for="">互相间隔：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="40" v-model="advFormData.style.splitMargin"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  :value="advFormData.style.splitMargin"   @input="checkMax(0,40,'advFormData.style.splitMargin')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">上间距：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="40" v-model="advFormData.style.marginTop"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  :value="advFormData.style.marginTop"   @input="checkMax(0,40,'advFormData.style.marginTop')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">左右间距：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip"  :min="0" :max="40"  v-model="advFormData.style.marginLeft"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"    :value="advFormData.style.marginLeft"    @input="checkMax(0,40,'advFormData.style.marginLeft')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-collapse-transition>
            </div>
        </div>
    </div>
</div>