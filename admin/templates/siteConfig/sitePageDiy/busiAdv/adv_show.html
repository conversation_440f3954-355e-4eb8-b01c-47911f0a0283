<!-- 瓷片广告位 -->
<div :class="['orderInfoBorderBox']"  v-if="modCon.id == 6">
    <div class="orderInfoBox cipianInfoBox" :style="`margin:${modCon.content.style.marginTop / 2}px ${modCon.content.style.marginLeft / 2}px 0; border-radius:${modCon.content.style.borderRadius / 2}px;`">
        <div :class="'cipianList cipianList'+  modCon.content.column" :data-style="1" v-if="modCon.content.column !== 1" :style="`grid-template-columns: repeat(${modCon.content.column},1fr);column-gap:${modCon.content.style.splitMargin / 2}px;`">
            <div class="cipian" v-for="(adv,ind) in (modCon.content.list.slice(0,modCon.content.column))" :key="ind" :data-style="1" :style="` border-radius:${modCon.content.style.borderRadius / 2}px; height:${modCon.content.style.height / 2}px; overflow:hidden;`">
                <img crossOrigin='anonymous' :src="adv.image ? (adv.image + '?v=' + Math.random()) : '/static/images/404.jpg'" onerror="this.src='/static/images/404.jpg'" alt="" v-if="adv.image &&!adv.image.includes('data:image')">
                <img   :src="adv.image"  alt="" v-else-if="adv.image">
            </div>
            
        </div>
        <el-carousel  :autoplay="false" v-else :height="(modCon.content.style.height / 2) + 'px'">
            <el-carousel-item class="cipian" v-for="(adv,lind) in modCon.content.list" :key="lind" data-style="1" :style="` border-radius:${modCon.content.style.borderRadius / 2}px; height:${modCon.content.style.height / 2}px; overflow:hidden;`">
                <div class="cipian_img"  >
                    <img crossOrigin='anonymous' v-if="adv.image &&!adv.image.includes('data:image')" style="width:100%; height:100%; object-fit:cover;" :src="adv.image ? (adv.image + '?v=' + Math.random()) : '/static/images/404.jpg'" onerror="this.src='/static/images/404.jpg'" alt="" >
                    <img v-else-if="adv.image" style="width:100%; height:100%; object-fit:cover;" :src="adv.image"  alt="" >
                </div>
            </el-carousel-item>
        </el-carousel>
    </div>
</div> 