<div class="msgConBox" v-if="modCon.id == 20">
    <div class="msgBox flexbox" :style="`margin:${modCon.content.style.marginTop / 2}px ${modCon.content.style.marginLeft / 2}px 0; border-radius:${modCon.content.style.borderRadiusTop / 2}px ${modCon.content.style.borderRadiusTop / 2}px ${modCon.content.splitLine ? (modCon.content.style.borderRadiusBottom / 2) : (modCon.content.style.borderRadiusTop / 2)}px ${modCon.content.splitLine ? (modCon.content.style.borderRadiusBottom / 2) : (modCon.content.style.borderRadiusTop / 2)}px; background:${checkBgColor(modCon.content.style.bgColor,modCon.content.style.opacity)}; border:solid ${modCon.content.style.borderSize / 2}px ${modCon.content.style.borderColor}; height:${modCon.content.style.height / 2}px; padding-left:${!modCon.content.style.marginLeft ? modCon.content.style.paddingLeft / 2 : '12'}px; padding-right:${!modCon.content.style.marginLeft ? modCon.content.style.paddingLeft / 2 : '12'}px;`">
        <div class="msg_title">
            <div class="h4" v-if="modCon.content.title.type == 'text'" :style="`color:${modCon.content.title.style.color};`">{{modCon.content.title.text || '标题'}}</div>
            <div class="img_tit" v-else><img crossOrigin='anonymous' :src="modCon.content.title.image" alt=""></div>
        </div>
        <div class="msg_text">
            <el-carousel
            indicator-position="none"
                :autoplay="true"
                height="34px"
                direction="vertical" :autoplay="true">
                <el-carousel-item v-for="(item,ind) in (modCon.content.msgType == 2 ? modCon.content.listArr :  systemMsgList.slice(0,modCon.content.listShow))"
                    :key="ind">
                    <div class="msg_item" :style="`color:${modCon.content.style.textColor};`">
                        <p v-if="modCon.content.msgType == 2">{{item.text || '这是一条自定义公告内容标题'}}</p>
                        <p v-else>{{item.title}}</p>
                    </div>
                </el-carousel-item>
            </el-carousel>
        </div>
        <div class="splitLine" :style="`background-color:${modCon.content.style.splitLine}; bottom:${modCon.content.splitLine == 2 ? 0 : 'auto'}; top:${modCon.content.splitLine == 1 ? 0 : 'auto'}; `" v-if="modCon.content.splitLine"></div>
    </div>
</div>