<!-- 展播链接 s -->
<div :class="['rightCon ',{'show':currEditPart == 8}] "  v-cloak>
    <div class="modBox linksContainer ">
        <div class="title" v-if="currEditPart == 8">展播链接
            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 8 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('advLinks',8) && (!checkModelHasIn() || !checkCompInModel())">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('advLinks')"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 8 && showResetPop" v-if="checkHasIn('advLinks',8)"  @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('advLinks',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">恢复上次保存</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 8 && showResetPop" v-if="checkModelHasIn() && checkCompInModel()" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复成模板样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="reChangeToModel('advLinks',8)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置模板样式</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 8 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('advLinks',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>
        </div>
        <div class="modInfoStyle">
            <div class="modstyleBox">
                <h2 class="stitle">组件样式</h2>
                <div class="sContent ">
                    <div class="flexItem style_options">
                        <div class="column_chose">
                            <s :class="{'layout2':advLinksFormData.styletype !== 1}"></s>
                            <span @click="advLinksFormData.styletype = 1"><em>样式一</em></span>
                            <span @click="advLinksFormData.styletype = 2"><em>样式二</em></span>
                        </div>
                        <div class="colorPickerBox">
                            <div class="colorPicker">
                                <!-- <div class="color_picker"  data-type="2">
                                    <div class="color bgimg" v-show="advLinksFormData.bgType == 'image' ">
                                        <div class="imgShow" v-if="advLinksFormData.bgType == 'image'"><img :src="advLinksFormData.style.bg_image" alt=""></div>
                                        <div class="imgShow" v-else :style="'background:'+ advLinksFormData.style.bg_color +';'"></div>
                                    </div>
                                    <div class="color" id="colorPicker2"></div>
                                </div> -->
                                <div class="color_picker">
                                    <div class="color bgimg" v-show="advLinksFormData.bgType == 'image' ">
                                        <div class="imgShow" v-if="advLinksFormData.bgType == 'image'"><img :src="advLinksFormData.style.bg_image" alt=""></div>
                                        <div class="imgShow" v-else :style="'background:'+ advLinksFormData.style.bg_color +';'"></div>
                                    </div>
                                    <el-color-picker v-model="advLinksFormData.style.bg_color" popper-class="borderColorPicker"  @active-change="value => {activeChangeColor(value,'advLinksFormData.style.bg_color'); advLinksFormData.bgType = 'color'}" @change="value => {resetColor('advLinksFormData.style.bg_color',value); advLinksFormData.bgType = 'color'}"></el-color-picker>
                                </div>
                            </div>
                            <a href="javascript:;" class="uploadBtn" v-if="advLinksFormData.styletype !== 1">
                                <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="advLinksBg_filedata" data-type="advLinksBg">
                            </a>
                        </div>
                    </div>
                    <div class="style_options flexItem " v-if="advLinksFormData.styletype !== 1">
                        <div class="titleBox">
                            <div class="inpCon">
                                <!-- 文字  只有图片标题为空时显示 -->
                                <el-input placeholder="请输入标题" v-model="advLinksFormData.title.text" v-if="advLinksFormData.title.type == 1"></el-input>
                                <div class="imgTitle" v-else> 
                                    <div class="imgbox"><img :src="advLinksFormData.title.img" alt=""></div>
                                    <span class="del_title" @click="advLinksFormData.title.type = 1; advLinksFormData.title.img = ''"></span>
                                </div>
                            </div>
                            <div class="colorPicker">
                                <el-color-picker v-model="advLinksFormData.title.style.color"   @active-change="value => activeChangeColor(value,'advLinksFormData.title.style.color')"></el-color-picker>
                            </div>
                            <div class="pic_upload"><input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="advLinksTitle_filedata" data-type="advLinksTitle"></div>
                        </div>
                    </div>
                    <div class="style_options flexItem" >
                        <el-checkbox v-model="advLinksFormData.showDesc">显示补充描述</el-checkbox>
                        <el-checkbox v-model="advLinksFormData.showBtn" v-show="advLinksFormData.styletype !== 1">显示按钮</el-checkbox>
                        <el-checkbox v-model="advLinksFormData.more.show" v-show="advLinksFormData.styletype == 2">显示查看更多</el-checkbox>
                    </div>
                </div>
            </div>
            <div class="modstyleBox contentSetBox">
                <h2 class="stitle">内容设置</h2>
                <div class="sContent ">
                    <div  class="btnsBox btn_item btnsSort sortBox" data-sort="advLinksFormData.linksArr" data-drag=".item" data-filter="input">
                        <div class="option_item item" v-for="(btn,ind) in advLinksFormData.linksArr" :key="'advLinks_' + btn.id" :data-id="ind">
                            <s class="del_item" @click="delTopBtns('advLinksFormData',ind)"></s>
                            <s class="left_icon"><img src="/static/images/admin/order_icon.png" alt=""></s>
                            <div class="item_con flex_r">
                                <div class="img_up fn-clear">
                                    
                                    <div class="imgText">
                                        <div class="inpbox" >
                                            <el-input   show-word-limit maxlength="6"  placeholder="请输入文字内容" class="iconName" v-model="btn.text" ></el-input>
                                        </div>
                                        <div :class="['inpbox linkbox linkChose']" v-if="!advLinksFormData.showDesc && !advLinksFormData.showBtn" @click="changeLink('advLinksFormData.linksArr.' + ind + '.link')">
                                            <s>
                                                <img v-if="btn.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                                <img v-else src="/static/images/admin/link.png">
                                            </s>
                                            <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="btn.linkInfo.linkText ? btn.linkInfo.linkText : btn.link ">
                                        </div>
                                        <div class="inpbox flexItem" v-else>
                                            <el-input placeholder="补充描述" class="iconName" v-model="btn.desc" v-if="advLinksFormData.showDesc"></el-input>
                                            <el-input placeholder="按钮名" class="btn" v-model="btn.btnText"  v-if="advLinksFormData.showBtn"></el-input>
                                        </div>
                                        
                                    </div>
                                    <div :class="['upbtn',{'hasup':btn.img}]">
                                        <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" :id="'advIcon_'+ind+'_filedata'" :data-type="'advIcon_'+ind" >
                                        <img v-if="btn.img" :src="btn.img"  alt="">
                                        <span>更换图片</span>
                                    </div>
                                </div>
                                <div :class="['inpbox linkbox linkChose fullLink']" v-if="advLinksFormData.showDesc || advLinksFormData.showBtn" @click="changeLink('advLinksFormData.linksArr.' + ind + '.link')">
                                    <s>
                                        <img v-if="btn.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                        <img v-else src="/static/images/admin/link.png">
                                    </s>
                                    <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="btn.linkInfo.linkText ? btn.linkInfo.linkText : btn.link ">
                                </div>
                                
                            </div>
                        </div>
                        <a href="javascript:;" class="add_more" @click="addNewTopBtns('advLinksFormData')"  ><s></s>添加链接</a>
                    </div>
                </div>
            </div>

            <div class="modstyleBox contentSetBox">
                <h2 class="stitle">样式设置</h2>
                <div class="sContent ">
                    <div class="style_item">
                        <div class="style_options" v-show="advLinksFormData.styletype == 1" >
                            <label for="">图片位置：</label>
                            <div class="radioBox split_options">
                                <span :class="{'onChose':advLinksFormData.imgPosi === 1}" @click="advLinksFormData.imgPosi = 1"><s></s>左侧</span>
                                <span :class="{'onChose':advLinksFormData.imgPosi === 2}" @click="advLinksFormData.imgPosi = 2"><s></s>右侧</span>
                                <span :class="{'onChose':advLinksFormData.imgPosi === 3}" @click="advLinksFormData.imgPosi = 3"><s></s>右下</span>
                            </div>
                        </div>
                        <div class="style_options">
                            <label for="">链接名称：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="advLinksFormData.style.textColor" @active-change="value => activeChangeColor(value,'advLinksFormData.style.textColor')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="advLinksFormData.style.textColor.replace('#','')" @change="changeColor('advLinksFormData.style.textColor')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('advLinksFormData.style.textColor')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" v-if="advLinksFormData.showDesc">
                            <label for="">补充描述：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="advLinksFormData.style.descColor" @active-change="value => activeChangeColor(value,'advLinksFormData.style.descColor')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="advLinksFormData.style.descColor.replace('#','')" @change="changeColor('advLinksFormData.style.descColor')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('advLinksFormData.style.descColor')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" v-if="advLinksFormData.showBtn">
                            <label for="">按钮文本：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="advLinksFormData.style.btnColor" @active-change="value => activeChangeColor(value,'advLinksFormData.style.btnColor')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="advLinksFormData.style.btnColor.replace('#','')" @change="changeColor('advLinksFormData.style.btnColor')"  spellcheck="false" maxlength="6">
                                <button @click="resetColor('advLinksFormData.style.btnColor')">重置</button>
                            </div>
                        </div>
                        <div class="style_options"  v-if="advLinksFormData.showBtn">
                            <label for="">按钮背景：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="advLinksFormData.style.btnBgColor" @active-change="value => activeChangeColor(value,'advLinksFormData.style.btnBgColor')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="advLinksFormData.style.btnBgColor.replace('#','')" @change="changeColor('advLinksFormData.style.btnBgColor')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('advLinksFormData.style.btnBgColor')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modstyleBox contentSetBox" v-show="advLinksFormData.more.show && advLinksFormData.styletype == 2 ">
                <h2 class="stitle">查看更多</h2>
                <div class="sContent">
                    <div class="item style_item ">
                        <div class="style_options" >
                            <label for="">显示文本：</label>
                            <div class="textSetBox">
                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="advLinksFormData.more.text"></el-input>
                            </div>
                            <span :class="['showArr',{'noColor':!advLinksFormData.more.arr}] " @click="advLinksFormData.more.arr = !advLinksFormData.more.arr"></span>
                        </div>
                        <div class="style_options" @click="changeLink('advLinksFormData.more.link')">
                            <label for="">链接：</label>
                            <div class="inpbox linkbox">
                                <s><img src="/static/images/admin/link.png" alt=""></s>
                                <input type="text" placeholder="请输入链接" class="iconLink"   readonly :value="advLinksFormData.more.linkInfo.linkText ? advLinksFormData.more.linkInfo.linkText : advLinksFormData.more.link ">
                            </div>
                        </div>

                        <div class="style_options" >
                            <label for="">文本颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker  class="colorpicker" v-model="advLinksFormData.more.style.color" @active-change="(value) => activeChangeColor(value,'advLinksFormData.more.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="(advLinksFormData.more.style.color).replace('#','')" @change="changeColor('advLinksFormData.more.style.color')" maxlength="6">
                                <button @click="resetColor('advLinksFormData.more.style.color')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 间距设置 -->
            <div class="marginSetBox modstyleBox contentSetBox" >
                <h2 class="stitle">间距设置</h2>
                <el-collapse-transition>
                    <div class="style_item" >
                        <div class="sContent">
                           
                            <div class="style_options">
                                <label for="">圆角值：</label>
                                <el-slider tooltip-class="tooltip_show"  v-model="advLinksFormData.style.borderRadius" :show-tooltip="showTooltip"  :min="0" :max="50"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  @input="checkMax(0,50,'advLinksFormData.style.borderRadius')" :value="advLinksFormData.style.borderRadius">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            
                           
                            <div class="style_options">
                                <label for="">上间距：</label>
                                <el-slider tooltip-class="tooltip_show" v-model="advLinksFormData.style.marginTop" :show-tooltip="showTooltip"  :min="0" :max="40"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow" @input="checkMax(0,40,'advLinksFormData.style.marginTop')" :value="advLinksFormData.style.marginTop">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">左右间距：</label>
                                <el-slider tooltip-class="tooltip_show" v-model="advLinksFormData.style.marginLeft" :show-tooltip="showTooltip"  :min="0" :max="40"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow" @input="checkMax(0,40,'advLinksFormData.style.marginLeft')" :value="advLinksFormData.style.marginLeft">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-collapse-transition>
            </div>

        </div>
    </div>
</div>
<!-- 展播链接 e -->