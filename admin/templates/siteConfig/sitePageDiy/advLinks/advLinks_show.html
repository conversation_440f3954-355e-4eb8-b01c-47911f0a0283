 <!-- 展播链接 -->
 <!-- :style="'margin-left: '+advLinksFormData.style.marginLeft+'px;margin-top:'+ advLinksFormData.style.marginTop +'px; -->
 <div class="advLinksConBox" v-if="modCon.id == 8">
    <div :class="['advLinksCon',{'advLinksCon2':modCon.content.styletype == 2}]" :style="getObjStyle(modCon.content,'advLinksCon') ">
        <div class="con_header" v-if="modCon.content.styletype == 2">
            <h4 class="con_title">
                <span v-if="modCon.content.title.type == 1" :style="'color:' + modCon.content.title.style.color ">{{modCon.content.title.text ? modCon.content.title.text : '标题'}}</span>
                <img crossOrigin='anonymous' :src="modCon.content.title.img ? (modCon.content.title.img + '?v=' + randomNum) : '/static/images/404.jpg'" v-else alt="" onerror="this.src='/static/images/404.jpg'" >
            </h4>
            <span class="more" v-show="modCon.content.more.show" :style="'color:'+(modCon.content.more.style.color)+';'">{{modCon.content.more.text}}<s v-if="modCon.content.more.arr" :style="'background-color:'+checkBgColor(modCon.content.more.style.color,'15')+';'">
                <!-- <em :style="'filter: drop-shadow(19px 0 0 '+ modCon.content.more.style.color +')'"></em> -->
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="12px" height="20px" viewBox="-12 -9 22 40"> <path fill-rule="evenodd"  opacity="0.6" :fill="modCon.content.more.style.color ? modCon.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg>
            </s></span>
        </div>
        <template v-if="modCon.content.styletype == 1">
            <ul class="noHeader" :style="'padding:0 '+ modCon.content.style.marginLeft / 2 +'px;'">
                <li v-for="(item,ind) in modCon.content.linksArr" :class="[{'hasImg':item.img},'link_' + modCon.content.imgPosi]" :style="getObjStyle(modCon.content,'advLinks')">
                    <div class="advImg" v-if="item.img"><img crossOrigin='anonymous' :src="item.img ? (item.img + '?v=' + randomNum) : '/static/images/404.jpg'" alt="" onerror="this.src='/static/images/404.jpg'"></div>
                    <div class="linkText">
                        <h4 :class="{'placeholder':!item.text}" :style="'color:'+ modCon.content.style.textColor +';'">{{item.text ? item.text : '添加标题'}}</h4>
                        <p v-if="modCon.content.showDesc" :style="'color:'+ modCon.content.style.descColor +';'">{{item.desc ? item.desc : '添加描述'}}</p>
                    </div>
                </li>
            </ul>
        </template>
        <template v-else>
            <ul class="hasHeader">
                <li v-for="(item,ind) in modCon.content.linksArr" :class="[{'hasImg':item.img},'link_3']" :style="getObjStyle(modCon.content,'advLinks')">
                    <div class="advImg" v-if="item.img"><img crossOrigin='anonymous' :src="item.img + '?v=' + randomNum" alt="" onerror="this.src='/static/images/404.jpg'"></div>
                    <div class="linkText">
                        <h4 :class="{'placeholder':!item.text}" :style="'color:'+ modCon.content.style.textColor +';'">{{item.text ? item.text : '添加标题' + (ind + 1)}}</h4>
                        <p v-if="modCon.content.showDesc" :style="'color:'+ modCon.content.style.descColor +';'">{{item.desc ? item.desc : '添加描述'}}</p>
                        <a href="javascript:;" class="adv_btn" v-if="modCon.content.showBtn" :style="'background-color:'+modCon.content.style.btnBgColor+'; color:'+modCon.content.style.btnColor+';'">{{item.btnText ? item.btnText : '按钮'}}</a>
                    </div>
                </li>
            </ul>
        </template>
    </div>
</div>