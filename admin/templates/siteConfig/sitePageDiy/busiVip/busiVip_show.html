<div class="busiVipConBox" v-if="modCon.id == 33">

    <div class="busiVipBox" :style="`margin:${modCon.content.setInfo.style.marginTop / 2}px ${modCon.content.setInfo.style.marginLeft / 2}px 0 ; border-radius:${modCon.content.setInfo.style.borderRadius / 2}px; overflow:hidden;`">
        <div class="vipstyle1"  v-if="modCon.content.styletype == 1" :style="modCon.content.setInfo.style.marginLeft == 0 ? `padding-left:${modCon.content.setInfo.style.paddingLeft / 2}px;padding-right: ${modCon.content.setInfo.style.paddingLeft / 2}px;` : ''">
            <div class="header_bg" :style="modCon.content.setInfo.bgType == 'color' ? `background-color:${modCon.content.setInfo.style.bgColor};` : `background-image:url(${modCon.content.setInfo.style.bgImage});background-color:${modCon.content.setInfo.style.bgMask};`"></div>
            <div class="bg_mask" :style="`background:linear-gradient(180deg, ${checkBgColor(modCon.content.setInfo.style.bgMask,'0')} 0%, ${checkBgColor(modCon.content.setInfo.style.bgMask,'0')} 20%, ${modCon.content.setInfo.style.bgMask} 40%, ${modCon.content.setInfo.style.bgMask} 100%);`"></div>
            <div class="con_header flexbox">
                <div class="h4" :style="`color:${modCon.content.setInfo.title.style.color};`" v-if="modCon.content.setInfo.title.type == 'text' || !modCon.content.setInfo.title.image">{{modCon.content.setInfo.title.text || '标题'}}</div>
                <div class="title_img" v-else><img  crossOrigin='anonymous' :src="modCon.content.setInfo.title.image" alt=""></div>
                <div class="stitle_box" v-if="modCon.content.setInfo.stitle.textArr.length">
                    <el-carousel
                    indicator-position="none"
                        :autoplay="true"
                        height="50px"
                        direction="vertical" :autoplay="true">
                        <el-carousel-item v-for="(item,ind) in modCon.content.setInfo.stitle.textArr"
                            :key="ind">
                            <div class="msg_item" :style="`color:${modCon.content.setInfo.stitle.style.color};`">
                                {{item.value}}
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </div>
            <div class="con_box">
                <div :class="'gridBox grid' + modCon.content.setInfo.showMod.length">
                    <div :class="'ulbox ulbox'+ modCon.content.setInfo.showMod.length">
                        <div class="li" v-for="(item,ind) in modCon.content.setInfo.showMod">
                            <div class="left_txt">
                                <div class="h5" >{{item.title || '开通会员模块'}}</div>
                                <div class="desc">{{item.subTitle || '商家会员描述'}}</div>
                            </div>
                            <div :class="['flex_r',{'noBg':ind >= 1 || modCon.content.setInfo.showMod.length != 3}]" :style="`background: linear-gradient(90deg, ${checkBgColor(item.iconMask,5)} 0%, ${checkBgColor(item.iconMask,25)} 100%);`">
                                <div class="tip">{{item.code == 'job' ? '招聘工作台>' : '开通商家会员>'}}</div>
                                <div class="icon" :style="`width:${modCon.content.setInfo.style.iconSize / 2}px; height:${modCon.content.setInfo.style.iconSize / 2}px;`"><img   :src="item.icon || btnDefault"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="more_btn" v-if="modCon.content.setInfo.more.show" :style="`color:${modCon.content.setInfo.more.style.color};`">
                <div class="btnMask" :style="`background:linear-gradient(180deg, ${checkBgColor(modCon.content.setInfo.style.bgMask,'0')} 0%,  ${modCon.content.setInfo.style.bgMask} 100%);`"></div>
                <div class="text">{{modCon.content.setInfo.more.text || '查看全部商家服务'}}</div>
            </div>
        </div>
        <div :class="['vipstyle2',{'noImg': !showVipSet && !modCon.content.setInfo.noVip.imgPath || (showVipSet && !modCon.content.setInfo.isVip.imgPath)}]" v-else >
            <div class="con_box" v-if="!showVipSet" :style="`height:${modCon.content.setInfo.noVip.style.height / 2}px;`">
                <img crossOrigin='anonymous' v-if="modCon.content.setInfo.noVip.imgPath"  :src="`${modCon.content.setInfo.noVip.imgPath}?v=${Math.random()}`" alt="" v-if="modCon.content.setInfo.noVip.imgPath">
            </div>
            <div class="con_box" v-else :style="`height:${modCon.content.setInfo.isVip.style.height / 2}px;`">
                <img crossOrigin='anonymous' v-if="modCon.content.setInfo.isVip.imgPath" :src="`${modCon.content.setInfo.isVip.imgPath}?v=${Math.random()}`" alt="" >
            </div>
        </div>
    </div>
</div>