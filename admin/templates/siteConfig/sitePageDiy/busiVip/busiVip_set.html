<div :class="['rightCon',{'show':currEditPart === 33}] " v-cloak>
    <div class="modBox">
        <div class="title">商家会员
            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 33 && showResetPop" @show="showResetPop = true" v-if="!checkHasIn('busiVip',33)">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('busiVip',0)"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop"  v-if="checkHasIn('busiVip',33)" :value="currEditPart === 33 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('busiVip',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference" @click="">恢复上次保存</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 33 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end" >
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('busiVip',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>
            
        </div>
        <div class="modInfoStyle">
            <div class="modstyleBox contentSetBox marginTopBox">
                <h2 class="stitle">组件设置</h2>
                <div class="sContent" >
                    <div class="style_options">
                        <div class="column_chose fullwidth">
                            <!-- busiVipFormData.styletype = 1 busiVipFormData.styletype = 2-->
                            <s :class="{'layout2': busiVipFormData.styletype === 2}" style="width: calc(50% - 2px);"></s>
                            <span @click="changeStyle(1)">基础样式</span>
                            <span @click="changeStyle(2)">自定义样式</span>
                        </div>
                    </div>
                    <dl class="setbox" v-show="vipAllSet || !showVipSet">
                        <dt><h5>未开通</h5></dt>
                        <!-- 基础样式未开通 -->
                        <dd v-show="busiVipFormData.styletype == 1" @click="showVipSet = false">
                            <div  class="btnsBox btn_item btnsSort sortBox" data-sort="busiVipFormData.setInfo.showMod" data-drag=".item" data-filter="input">
                                <div class="option_item item" v-for="(btn,ind) in busiVipFormData.setInfo.showMod" :key="'busiVip_' + btn.code" :data-id="ind">
                                    <s class="del_item" @click="delTopBtns('busiVipFormData',ind)"></s>
                                    <s class="left_icon"><img src="/static/images/admin/order_icon.png" alt=""></s>
                                    <div class="item_con flex_r flexnone">
                                        <div :class="['inpbox linkbox linkChose']" style="margin-bottom: 12px;" @click="showOpenVip('busiVipFormData.setInfo.showMod.' + ind + '.link')">
                                            <s>
                                                <img v-if="btn.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                                <img v-else src="/static/images/admin/link.png">
                                            </s>
                                            <input type="text" placeholder="选择开通模块" class="iconLink" readonly :value="btn.linkInfo.linkText ? btn.linkInfo.linkText : btn.link ">
                                        </div>
                                        <div class="img_up fn-clear flexbox">
                                            
                                            <div class="imgText ">
                                                <div class="inpbox" >
                                                    <el-input   show-word-limit maxlength="6"  placeholder="请输入文字内容" class="iconName" v-model="btn.title" ></el-input>
                                                </div>
                                                <div class="inpbox flexItem" >
                                                    <el-input placeholder="补充描述" class="iconName" v-model="btn.subTitle"></el-input>
                                                </div>
                                                
                                            </div>
                                            <div :class="['upbtn',{'hasup':btn.icon}]" style="margin-right:0; margin-left:14px;">
                                                <input type="file" name="Filedata" @change="fileInpChange(`busiVipFormData.setInfo.showMod.${ind}.icon`)" accept=".png,.jpg,.jpeg" class="fileUp" :id="'advIcon_'+ind+'_filedata'" :data-type="'advIcon_'+ind" >
                                                <img v-if="btn.icon" :src="btn.icon"  alt="">
                                                <span>更换图片</span>
                                            </div>
                                        </div>
                                        
                                        
                                    </div>
                                </div>
                                <a href="javascript:;" class="add_more" v-if="busiVipFormData.styletype == 1 && busiVipFormData.setInfo.showMod.length < 4" @click="addNewTopBtns('busiVipFormData')"  ><s></s>添加按钮</a>
                            </div>
                        </dd>
                        <dd v-if="busiVipFormData.styletype !=  1" @click="showVipSet = false">
                            <div class="style_item item">
                                <div class="style_options">
                                    <label for="">设置内容：</label>
                                    <div class="img_up fn-clear">
                                        <div :class="['upbtn vip_icon fullW', {'hasup':busiVipFormData.setInfo.noVip.imgPath}]  ">
                                            <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="icon_filedata" data-type="icon"  @change="fileInpChange('busiVipFormData.setInfo.noVip.imgPath')">
                                            <img :src="busiVipFormData.setInfo.noVip.imgPath"  alt="" v-if="busiVipFormData.setInfo.noVip.imgPath">
                                            <span>更换图片</span>
                                        </div>
                                        <div class="imgText">
                                            <p>建议宽度1080px</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">设置链接：</label>
                                    <div :class="[{'inpbox': busiVipFormData.setInfo.noVip.linkInfo.type != 3} ,{'editbox': busiVipFormData.setInfo.noVip.linkInfo.type == 3},{'marginTop': busiVipFormData.setInfo.noVip.txtStyle === 0},'linkbox linkChose']" @click="changeLink('busiVipFormData.setInfo.noVip.link',busiVipFormData.setInfo.noVip.linkInfo.type == 3)"  :style="busiVipFormData.setInfo.noVip.txtStyle ? '' : 'padding-top:0;'">
                                        <s>
                                            <img v-if="busiVipFormData.setInfo.noVip.linkInfo.type == 1"  src="/static/images/admin/link.png">
                                            <img v-else-if="busiVipFormData.setInfo.noVip.linkInfo.type == 2"  src="/static/images/admin/siteConfigPage/scan_icon.png?v=1">
                                            <img v-else-if="busiVipFormData.setInfo.noVip.linkInfo.type == 3"  src="/static/images/admin/siteConfigPage/editlink_icon.png?v=1">
                                            <img v-else-if="busiVipFormData.setInfo.noVip.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                        </s>
                                        <input type="text" placeholder="请选择链接" class="iconLink" v-if="busiVipFormData.setInfo.noVip.linkInfo.type != 3" readonly :value="busiVipFormData.setInfo.noVip.linkInfo.linkText ? busiVipFormData.setInfo.noVip.linkInfo.linkText : busiVipFormData.setInfo.noVip.link " >
                                    </div>
                                </div>
                                <div class="style_options"  v-if="vipAllSet">
                                    <label for="">组件高度：</label>
                                    <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="120" :max="640" v-model="busiVipFormData.setInfo.noVip.style.height"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.noVip.style.height"  @change="checkMax(120,640,'busiVipFormData.setInfo.noVip.style.height')">
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                            </div>
                        </dd>
                    </dl>

                    <dl class="setbox" v-if="vipAllSet || showVipSet">
                        <dt class="style_options">
                            <h5>开通后</h5>
                            <div class="split_options radioBox">
                                <span :class="{'onChose':busiVipFormData.vipShow == (key == 0 ? 1 : 0)}" v-for="(item,key) in 2" :key="key" @click="(showVipSet = key == 0 ? true : showVipSet) ; busiVipFormData.vipShow = (key == 0 ? 1 : 0)"><s></s>{{!key ? '显示' : '不显示'}}</span> 
                            </div>
                        </dt>

                        <!-- 自定义样式已开通 -->
                        <dd v-if="busiVipFormData.styletype != 1 && busiVipFormData.vipShow"  @click="showVipSet = true">
                            <div class="style_item item">
                                <div class="style_options">
                                    <label for="">设置内容：</label>
                                    <div class="img_up fn-clear">
                                        <div :class="['upbtn vip_icon fullW', {'hasup':busiVipFormData.setInfo.isVip.imgPath}]  ">
                                            <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="icon_filedata" data-type="icon"  @change="fileInpChange('busiVipFormData.setInfo.isVip.imgPath')">
                                            <img :src="busiVipFormData.setInfo.isVip.imgPath"  alt="" v-if="busiVipFormData.setInfo.isVip.imgPath">
                                            <span>更换图片</span>
                                        </div>
                                        <div class="imgText">
                                            <p>建议宽度1080px</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">设置链接：</label>
                                    <div :class="[{'inpbox': busiVipFormData.setInfo.isVip.linkInfo.type != 3} ,{'editbox': busiVipFormData.setInfo.isVip.linkInfo.type == 3},{'marginTop': busiVipFormData.setInfo.isVip.txtStyle === 0},'linkbox linkChose']" @click="changeLink('busiVipFormData.setInfo.isVip.link',busiVipFormData.setInfo.isVip.linkInfo.type == 3)"  :style="busiVipFormData.setInfo.isVip.txtStyle ? '' : 'padding-top:0;'">
                                        <s>
                                            <img v-if="busiVipFormData.setInfo.isVip.linkInfo.type == 1"  src="/static/images/admin/link.png">
                                            <img v-else-if="busiVipFormData.setInfo.isVip.linkInfo.type == 2"  src="/static/images/admin/siteConfigPage/scan_icon.png?v=1">
                                            <img v-else-if="busiVipFormData.setInfo.isVip.linkInfo.type == 3"  src="/static/images/admin/siteConfigPage/editlink_icon.png?v=1">
                                            <img v-else-if="busiVipFormData.setInfo.isVip.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                        </s>
                                        <input type="text" placeholder="请选择链接" class="iconLink" v-if="busiVipFormData.setInfo.isVip.linkInfo.type != 3" readonly :value="busiVipFormData.setInfo.isVip.linkInfo.linkText ? busiVipFormData.setInfo.isVip.linkInfo.linkText : busiVipFormData.setInfo.isVip.link " >
                                    </div>
                                </div>
                                <div class="style_options" v-if="vipAllSet" >
                                    <label for="">组件高度：</label>
                                    <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="120" :max="640" v-model="busiVipFormData.setInfo.isVip.style.height"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.isVip.style.height"  @change="checkMax(120,640,'busiVipFormData.setInfo.isVip.style.height')">
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>

            <div class="modstyleBox contentSetBox" v-if="busiVipFormData.styletype == 1">
                <h2 class="stitle">组件样式</h2>
                <div class="sContent" >

                    <div class="flexItem bgSetBox">
                        <div class="item ">
                            <span>渐变 </span>
                            <el-color-picker  class="colorpicker" v-model="busiVipFormData.setInfo.style.bgMask" @active-change="(value) => activeChangeColor(value,'busiVipFormData.setInfo.style.bgMask')"></el-color-picker>
                        </div>
                        <div class="item" style="max-width: 80%;">
                            <span>背景 </span>
                            <div class="colorPickerBox cPicker">
                                <div class="colorPicker">
                                    <div class="color_picker"  data-type="pros5">
                                        <div class="color bgimg" >
                                            <div class="imgShow" v-if="busiVipFormData.setInfo.bgType == 'image'"><img :src="busiVipFormData.setInfo.style.bgImage" alt=""></div>
                                            <div class="imgShow" v-else-if="busiVipFormData.setInfo.style" :style="'background:'+ busiVipFormData.setInfo.style.bgColor +';'"></div>
                                        </div>
                                        <el-color-picker class="colorpicker" v-model="busiVipFormData.setInfo.style.bgColor" popper-class="borderColorPicker"  @active-change="value => {activeChangeColor(value,'busiVipFormData.setInfo.style.bgColor'); busiVipFormData.setInfo.bgType = 'color'}" @change="value => {resetColor('busiVipFormData.setInfo.style.bgColor',value); busiVipFormData.setInfo.bgType = 'color'}"></el-color-picker>
                                    </div>
                                </div>
                                <a href="javascript:;" class="uploadBtn"><input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="pros5_filedata" data-type="pros5" @change="fileInpChange('busiVipFormData.setInfo.style.bgImage')"></a>
                            </div>
                        </div>
                    </div>

                    <div class="style_item marginTopSetBox ">
                        <div class="style_options flexItem">
                            <label for="">组件标题：</label>
                            <div class="inpbox">
                                <div class="imgShow hasUpBtn" v-if="busiVipFormData.setInfo.title.type == 'image'" >
                                    <div class="flex_l">
                                        <img  alt="" :src="busiVipFormData.setInfo.title.image" >
                                        <span class="clearPath" @click="busiVipFormData.setInfo.title.type = 'text';busiVipFormData.setInfo.title.image = ''"></span>
                                    </div>
                                    <i class="btn_upload">
                                        <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" data-type="pros_title" @change="fileInpChange('busiVipFormData.setInfo.title.image')">
                                    </i>
                                </div>
                                <el-input class="hasUpBtn" placeholder="请输入" v-model="busiVipFormData.setInfo.title.text" v-else maxlength="8">
                                    <div class="inpSuffix"  slot="suffix">
                                        <span class="el-input__count">
                                            <span class="el-input__count-inner">{{busiVipFormData.setInfo.title.text.length}}/5</span>

                                        </span>
                                        <i class="btn_upload">
                                            <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" data-type="pros_title" @change="fileInpChange('busiVipFormData.setInfo.title.image')">
                                        </i>
                                    </div>
                                </el-input>
                            </div>
                        </div>
                        <div class="style_options flexbox" >
                            <label for="" style="align-self:flex-start; line-height:40px;">副标题：</label>
                            <div class="allInpbox inpbox moreInpBox">
                                <ul>
                                    <li v-for="(stitle,ind) in busiVipFormData.setInfo.stitle.textArr">
                                        <div class="inpbox">
                                            <el-input placeholder="请输入"  v-model="stitle.value" >
                                                <a href="javascript:;" slot="suffix" v-if="ind ==( busiVipFormData.setInfo.stitle.textArr.length - 1 )" class="addCon_btn" @click="addStitle('pros')"><i class="el-icon-plus"></i>滚动内容</a>
                                            </el-input>
                                        </div>
                                        <a href="javascript:;" class="delBtn"  @click="delInpObj('busiVipFormData.setInfo.stitle.textArr',ind)" ></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="style_options" v-if="busiVipFormData.setInfo.title.type == 'text'">
                            <label for="">标题颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="busiVipFormData.setInfo.title.style.color" @active-change="value => activeChangeColor(value,'busiVipFormData.setInfo.title.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="busiVipFormData.setInfo.title.style.color.replace('#','')" @change="changeColor('busiVipFormData.setInfo.title.style.color')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('busiVipFormData.setInfo.title.style.color')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" >
                            <label for="">副标题：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="busiVipFormData.setInfo.stitle.style.color" @active-change="value => activeChangeColor(value,'busiVipFormData.setInfo.stitle.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="busiVipFormData.setInfo.stitle.style.color.replace('#','')" @change="changeColor('busiVipFormData.setInfo.stitle.style.color')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('busiVipFormData.setInfo.stitle.style.color')">重置</button>
                            </div>
                        </div>
                    </div>

                    <div class="style_item marginTopSetBox">
                        <div class="style_options">
                            <label for="">更多按钮：</label>
                            <div class="split_options radioBox">
                                <span :class="{'onChose':busiVipFormData.setInfo.more.show == (key == 0 ? 1 : 0)}" v-for="(item,key) in 2" :key="key" @click="busiVipFormData.setInfo.more.show = (key == 0 ? 1 : 0)"><s></s>{{!key ? '显示' : '不显示'}}</span> 
                            </div>
                        </div>
                        <div class="style_options">
                            <label for="">按钮文本：</label>
                            <el-input   show-word-limit maxlength="8"  placeholder="请输入文字内容" class="iconName" v-model="busiVipFormData.setInfo.more.text" ></el-input>
                        </div>
                        <div class="style_options" >
                            <label for="">文本颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" v-model="busiVipFormData.setInfo.more.style.color" @active-change="value => activeChangeColor(value,'busiVipFormData.setInfo.more.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="busiVipFormData.setInfo.more.style.color.replace('#','')" @change="changeColor('busiVipFormData.setInfo.more.style.color')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('busiVipFormData.setInfo.more.style.color')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 间距设置 -->
            <div class="marginSetBox modstyleBox" >
                <h2 class="stitle">间距设置</h2>
                <el-collapse-transition>
                    <div class="style_item" v-show="showMarginSet">
                        <div class="sContent">
                            <div class="style_options" v-if="busiVipFormData.styletype == 1">
                                <label for="">图标大小：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="64" :max="108" v-model="busiVipFormData.setInfo.style.iconSize"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.style.iconSize"  @change="checkMax(64,108,'busiVipFormData.setInfo.style.iconSize')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <template v-else-if="!vipAllSet">
                                <div class="style_options" v-if="showVipSet">
                                    <label for="">组件高度：</label>
                                    <el-slider step="8" tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="120" :max="640" v-model="busiVipFormData.setInfo.isVip.style.height"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.isVip.style.height"  @change="checkMax(120,640,'busiVipFormData.setInfo.isVip.style.height')">
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                                <div class="style_options" v-else>
                                    <label for="">组件高度：</label>
                                    <el-slider  step="8" tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="120" :max="640" v-model="busiVipFormData.setInfo.noVip.style.height"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.noVip.style.height"  @change="checkMax(120,640,'busiVipFormData.setInfo.noVip.style.height')">
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                            </template>
                            <div class="style_options">
                                <label for="">圆角值：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="busiVipFormData.setInfo.style.borderRadius"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.style.borderRadius"  @change="checkMax(0,50,'busiVipFormData.setInfo.style.borderRadius')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">上间距：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="busiVipFormData.setInfo.style.marginTop"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.style.marginTop"  @change="checkMax(0,50,'busiVipFormData.setInfo.style.marginTop')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">左右间距：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="busiVipFormData.setInfo.style.marginLeft"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.style.marginLeft"  @change="checkMax(0,50,'busiVipFormData.setInfo.style.marginLeft')">
                                    <span class="single back">px</span>
                                </div>
                            </div>

                            <!-- 内边距只有基础样式才有 -->
                            <div class="style_options"  :style="busiVipFormData.setInfo.style.marginLeft == 0 && busiVipFormData.styletype == 1 ? '' : 'visibility:hidden; opacity:0;'">
                                <label for="">内边距：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip" :min="0" :max="50" v-model="busiVipFormData.setInfo.style.paddingLeft"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number"  class="colorShow"  :value="busiVipFormData.setInfo.style.paddingLeft"  @change="checkMax(0,50,'busiVipFormData.setInfo.style.paddingLeft')">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </el-collapse-transition>
            </div>

        </div>
    </div>
</div>