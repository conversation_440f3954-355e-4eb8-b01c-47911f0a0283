<!-- 分隔标题 -->
<div :class="['rightCon',{'show':currEditPart === 7}] " v-cloak>
    <div class="financeContainer titleBox modBox">
        <div class="title" v-if="currEditPart === 7">分隔标题

            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('title',7) && (!checkModelHasIn() || !checkCompInModel())">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('title')"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('title',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">恢复上次保存</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 7 && showResetPop" v-if="checkModelHasIn() && checkCompInModel()" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复成模板样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="reChangeToModel('title',7)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置模板样式</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 7 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('title',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>

        </div>
        <div class="financeStyle">

            <!-- 组件样式 -->
            <div class="modstyleBox">
                <h2 class="stitle">组件样式</h2>
                <div class="titleSetBox">
                    <div class="style fn-clear">
                        <div class="column_chose" style="width:200px;">
                            <s :class="!titleFormData.layout ? 'dark' : 'light'"></s>
                            <span data-col="dark" @click="titleFormData.layout = 0">居左</span>
                            <span data-col="light" @click="titleFormData.layout = 1">居中</span>
                        </div>
                        <el-checkbox label="显示查看更多" class="showMore" v-model="titleFormData.more.show"></el-checkbox>
                    </div>
                    <el-input class="bgColorInp" show-word-limit maxlength="10" placeholder="请输入标题" v-model="titleFormData.title.text"></el-input>
                </div> 
            </div>

            <!-- 样式设置 -->
            <div class="modstyleBox contentSetBox">
                <h2 class="stitle">样式设置</h2>
                <div class="sContent">
                    <div class="item style_item ">
                        <div class="style_options" >
                            <label for="">标题样式：</label>
                            <div class="radioBox">
                                <span :class="[{'on_chose':titleFormData.title.type == ind}]" @click="titleFormData.title.type = ind" v-for="(item,ind) in showTypeOpt1"><s></s>{{item}}</span>
                            </div>
                            
                        </div>
                        

                        <div class="style_options" v-show="titleFormData.title.type === 1" >
                            <label for="">划线颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker  class="colorpicker" v-model="titleFormData.title.style.borderColor" @active-change="(value) => activeChangeColor(value,'titleFormData.title.style.borderColor')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="(titleFormData.title.style.borderColor).replace('#','')" @change="changeColor('titleFormData.title.style.borderColor')" maxlength="6">
                                <button @click="resetColor('titleFormData.title.style.borderColor')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" v-show="titleFormData.title.type === 2" >
                            <label for="" style="align-self:flex-start; padding-top:6px;">添加图标：</label>
                            <div class="img_up fn-clear">
                                <div :class="['upbtn vip_icon',{'hasup':titleFormData.title.icon}]">
                                    <input type="file" name="Filedata" @change="fileInpChange(`titleFormData.title.icon`)" accept=".png,.jpg,.jpeg" class="fileUp" id="titleFormData_filedata"  data-type="titleFormData">
                                    <img :src="titleFormData.title.icon" alt="" v-if="titleFormData.title.icon">
                                    <span v-show="titleFormData.title.icon">更换图片</span>
                                </div>
                            </div>
                            <div class="imgText">
                                <p class="tip">建议高度60px</p>
                            </div>
                        </div>
                        <div class="style_options" >
                            <label for="">标题颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker  class="colorpicker" v-model="titleFormData.title.style.color" @active-change="(value) => activeChangeColor(value,'titleFormData.title.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="(titleFormData.title.style.color).replace('#','')" @change="changeColor('titleFormData.title.style.color')" maxlength="6">
                                <button @click="resetColor('titleFormData.title.style.color')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" >
                            <label for="">标题大小：</label>
                            <el-slider tooltip-class="tooltip_show"  :show-tooltip="showTooltip" :min="24" :max="48" v-model="titleFormData.title.style.fontSize"></el-slider>
                            <div class="colorShowBox" style="background-color: #E8EAEC;">
                                <input type="number" class="colorShow" v-model="titleFormData.title.style.fontSize"  @change="checkMax(24,40,'titleFormData.title.style.fontSize')">
                                <span class="single back">px</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modstyleBox contentSetBox" v-show="titleFormData.more.show">
                <h2 class="stitle">查看更多</h2>
                <div class="sContent">
                    <div class="item style_item ">
                        <div class="style_options" >
                            <label for="">显示文本：</label>
                            <div class="textSetBox">
                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="titleFormData.more.text"></el-input>
                            </div>
                            <span :class="['showArr',{'noColor':!titleFormData.more.arr}] " @click="titleFormData.more.arr = !titleFormData.more.arr"></span>
                        </div>
                        <div class="style_options" @click="changeLink('titleFormData.more.link')">
                            <label for="">链接：</label>
                            <div class="inpbox linkbox">
                                <s><img src="/static/images/admin/link.png" alt=""></s>
                                <input type="text" placeholder="请选择链接" readonly class="iconLink" :value="titleFormData.more.linkInfo && titleFormData.more.linkInfo.linkText ? titleFormData.more.linkInfo.linkText : titleFormData.more.link ">
                            </div>
                        </div>

                        <div class="style_options" >
                            <label for="">文本颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker  class="colorpicker" v-model="titleFormData.more.style.color" @active-change="(value) => activeChangeColor(value,'titleFormData.more.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="(titleFormData.more.style.color).replace('#','')" @change="changeColor('titleFormData.more.style.color')" maxlength="6">
                                <button @click="resetColor('titleFormData.more.style.color')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 间距设置 -->
            <div class="marginSetBox modstyleBox contentSetBox" >
                <h2 class="stitle"  @click="showMarginSet = !showMarginSet">间距设置</h2>
                <el-collapse-transition>
                    <div class="style_item" v-show="showMarginSet">
                        <div class="sContent">
                            <div class="style_options" >
                                <label for="">上间距：</label>
                                <el-slider tooltip-class="tooltip_show"  :show-tooltip="showTooltip" :min="0" :max="40" v-model="titleFormData.style.marginTop"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  @change="checkMax(0,40,'titleFormData.style.marginTop')" :value="titleFormData.style.marginTop"  >
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options" >
                                <label for="">高度：</label>
                                <el-slider tooltip-class="tooltip_show"  :show-tooltip="showTooltip" :min="80" :max="180" v-model="titleFormData.style.height"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  @change="checkMax(80,180,'titleFormData.style.height')" :value="titleFormData.style.height"  >
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">左右间距：</label>
                                <el-slider  tooltip-class="tooltip_show"  :show-tooltip="showTooltip" :min="0" :max="40"  v-model="titleFormData.style.marginLeft"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"   @input="checkMax(0,40,'titleFormData.style.marginLeft')" :value="titleFormData.style.marginLeft">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-collapse-transition>
            </div>
        </div>
    </div>

</div>