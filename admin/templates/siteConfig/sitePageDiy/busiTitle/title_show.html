<!-- 分隔标题 -->
<div class=" titleInfoConBox" v-if="modCon.id == 7">
    <div class="titleInfoBox" :style="'margin:'+ (modCon.content.style.marginTop ? modCon.content.style.marginTop / 2 : 0)  +'px '+ (modCon.content.style.marginLeft/2) +'px 0; height:'+ modCon.content.style.height/2+'px;'">
        <div class="place" v-show="modCon.content.layout=== 1"></div>
        <div :class="['titleInfo',{'alignCenter':modCon.content.layout=== 1}]">
            <div class="icon" v-show="modCon.content.title.type == 2"><img crossOrigin='anonymous' :src="modCon.content.title.icon ? modCon.content.title.icon : ('/static/images/admin/siteMemberPage/default_icon2.png')"  alt=""></div>
            <h4 :style="'font-weight:bold; font-size:'+(modCon.content.title.style.fontSize / 2)+'px; color:'+modCon.content.title.style.color+';'">
                <span>{{modCon.content.title.text ? modCon.content.title.text : '标题'}}</span>
                <div class="tit_line" :style="'background:'+modCon.content.title.style.borderColor+';'" v-show="modCon.content.title.type === 1"></div>
            </h4>
        </div>
        <div class="moreInfo" v-if="modCon.content.more.show" :style="'color:'+modCon.content.more.style.color+';'">{{modCon.content.more.text}}<s v-show="modCon.content.more.arr">
            <!-- <em :style="'filter: drop-shadow(12px 0 0 '+ modCon.content.more.style.color +')'"></em> -->
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 0 22 30"> <path fill-rule="evenodd"  opacity="0.6" :fill="modCon.content.more.style.color ? modCon.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg>
        </s></div> 
    </div>
</div>