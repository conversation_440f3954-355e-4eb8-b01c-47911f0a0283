 <!-- 数据列表 -->
 <div :class="['rightCon',{'show':currEditPart === 28}] " style="width:528px;" v-if="listFormData && listFormData.currInd != undefined">
    <div class="modBox listContainer ">
        <div class="title">数据列表
            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 28 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('list',28)">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('list')"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 28 && showResetPop" v-if="checkHasIn('list',28)" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('list',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">恢复上次保存</li> 
                        </el-popover>
                        <!-- <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 28 && showResetPop" v-if="checkModelHasIn() && checkCompInModel()" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复成模板样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="reChangeToModel('list',28)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置模板样式</li> 
                        </el-popover> -->
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 28 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('list',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>
        </div>
        <div class="modInfoStyle">
            <div class="modstyleBox" v-if="labelFormData.labelShow">
                <h2 class="stitle flex_l">选择选项 <a href="javascript:;" class="edit" @click="currEditPart = 11">编辑选项</a></h2>
                <div class="sContent">
                    <div class="labsConBox">
                        <!-- labNum3  个数小于3 -->
                        <div  :class="['labBox', 'labBox' + labelFormData.styletype, {'labNum3' : labelFormData.labsArr && labelFormData.labsArr.length <= 3},{'moreLen': labelFormData.labsArr && labelFormData.labsArr.length > 3 && labelFormData.more && labelFormData.more.show}, {'labCenter' : labelFormData.labsArr && labelFormData.labsArr.length <= 3 && labelFormData.position == 'center' }] ">
                            <ul>
                                <li :class="[{'on_chose': labelFormData.labelChose == ind || (!labelFormData.labelChose && ind == 0)}]" v-for="(item,ind) in labelFormData.labsArr" @click=" changeEditlabel('',ind) ">
                                    <a href="javascript:;" class="tab">
                                        <h3>
                                            <span v-if="item.title.type == 'text'" :style="'color:' +(labelFormData.labelChose == ind || (!labelFormData.labelChose && ind == 0) ? labelFormData.style.chose_title : labelFormData.style.title)+ ';'">{{item.title.text ? item.title.text : ('标题' + (ind + 1))}}</span>
                                            <span class="tit_img" v-else><img :src="item.title.path" alt=""></span>
                                            <s class="line" :style="'background:'+ labelFormData.style.chose_block +';'" v-if="labelFormData.styletype !== 1 && labelFormData.styletype !== 5">
                                                <em v-if="labelFormData.styletype == 2" :style="'filter: drop-shadow(20px 0 0 '+ labelFormData.style.chose_block +'); transform: translateX(-100%);'"></em>
                                            </s>
                                        </h3>
                                        <span class="stit" :style="(labelFormData.labelChose == ind || (!labelFormData.labelChose && ind == 0) ? getObjStyle(labelFormData,'labStyle') : getObjStyle(labelFormData,'labStyle1'))" v-if="labelFormData.styletype == 1 || labelFormData.styletype == 5">{{item.subtitle ? item.subtitle : '副标题'}}</span>
                                    </a>
                                </li>
                               
                            </ul>
                        </div>

                        <template v-for="(item,ind) in labelFormData.labsArr" >
                            <div class="slabBox" v-if="item.type == 2 && (labelFormData.labelChose == ind || (!labelFormData.labelChose && ind == 0))">
                                <ul>
                                    <li @click="changeEditlabel('',ind,sid)" :class="[{'on_chose':labelFormData.slabelChose == sid  || (!labelFormData.slabelChose && sid == 0)}]" v-for="(slab,sid) in item.children" :style="getObjStyle(labelFormData,(labelFormData.slabelChose == sid || (!labelFormData.slabelChose && sid == 0)? 'labSub1' : 'labSub'))">
                                        <a href="javascript:;" :style="'color:'+ (labelFormData.slabelChose == sid || (!labelFormData.slabelChose && sid == 0) ? labelFormData.style.chose_sub_title : labelFormData.style.sub_title) +';'" >{{slab.title ? slab.title : '选项' + (sid + 1)}}</a>
                                    </li>
                                </ul>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="modstyleBox" v-if="!labelFormData.labelShow">
                <h2 class="stitle flex_l">组件设置 <span @click="changeToLab(11); labelFormData.titleMore.show = false" class="btn_changeTab">添加可切换的标题</span></h2>
                <div class="sContent">
                    <el-checkbox v-model="labelFormData.titleMore.show">组件标题</el-checkbox>
                    <!-- <el-checkbox v-model="labelFormData.titleMore.more.show">显示查看更多</el-checkbox> -->
                    <div class="inpbox" v-show="labelFormData.titleMore.show">
                        <el-input class="bgColorInp" placeholder="请输入标题" v-model="labelFormData.titleMore.title.text"></el-input>
                        <a href="javascript:;" class="btn_changeTab" @click="changeToLab(7)">编辑标题样式</a>
                    </div>
                </div>
            </div>
            <template>
                <div class="modstyleBox">
                    <h2 class="stitle">添加数据</h2>
                    <div class="sContent">
                        <div class="style_options">
                            <label for="">选择方式：</label>
                            <div class="radioBox split_options ">
                                <span :class="[{'onChose':listFormData.addType === 1}]" @click="listFormData.addType = 1; "><s></s>动态数据</span>
                                <span :class="[{'onChose':listFormData.addType === 2}]" @click="listFormData.addType = 2; tableSortInit()"><s></s>手动选择</span>
                            </div>
                        </div>
                        <!-- <div class="style_options" v-if="listFormData.addType == 2">
                            <label for="">失效内容：</label>
                            <div class="radioBox split_options ">
                                <span :class="[{'onChose':listFormData.failure == 1}]" @click="listFormData.failure = 1"><s></s>显示</span>
                                <span :class="[{'onChose':listFormData.failure == 0}]" @click="listFormData.failure = 0"><s></s>隐藏</span>
                            </div>
                        </div> -->
                        <!-- {{listFormData.showObj && listFormData.showObj.id ? (((['info','circle','sfcar','tieba','business','article'].includes(listFormData.showObj.service) ||  !dataArr[listFormData.showObj.service]? '' : (dataArr[listFormData.showObj.service](dataArr[listFormData.showObj.service].name) +'-') : '')) + listFormData.showObj.text) : '选择数据来源'}} -->
                        <div :class="['item flexItem selectBoxItem',{'noData':loadEnd.listFormData  && listFormData.addType === 1 && (!listFormData.listArr || listFormData.listArr.length == 0) && listFormData.showObj.service}]">
                            <div class="flexItem">
                                <div class="selectItem dataType" @click="showlabConPop(listFormData.currInd,listFormData.currCInd)"><s></s><span>{{listFormData.showObj && listFormData.showObj.id && checkModuleIn(listFormData.showObj.service)? (((['info','circle','sfcar','tieba','business','article'].includes(listFormData.showObj.service) ? '' : (dataArr[listFormData.showObj.service].name) +'-')) +listFormData.showObj.text) : '选择数据来源'}}</span></div>
                                <el-cascader
                                    class="selectItem" v-if="listFormData.showObj.service == 'business'"
                                    :show-all-levels="false"
                                    v-model="listFormData.dataObj.interface.typeArr"
                                    :props="{label:'typename', value:'id', children:'lower'}"
                                    filterable
                                    popper-class="classify"
                                    @change="(value) => typeChange('listFormData',value)"
                                    :options="allTypeList[listFormData.showObj.service]">
                                    <template slot-scope="{ node,data }">
                                        <span>{{ data.title ? data.title : '全部'}}</span>
                                    </template>
                                </el-cascader>
                                <el-select  placeholder="默认排序"  class="selectItem" v-show="listFormData.addType == 1 && listFormData.showObj.service"  v-model="listFormData.dataObj.interface.orderby" @change="(value) => changeOrder(value,'listFormData')">
                                    <el-option :value="item.value" v-for="item in listFormData.dataObj.modCon.orderby" :key="item.value" :label="item.text" >{{item.text}}</el-option>
                                </el-select>
                                <a href="javascript:;" class="showProPop" v-show="listFormData.addType == 1 && listFormData.showObj && listFormData.showObj.service" @click="showChosePop(1)"></a>
                            </div>
                            <div class="noListTip" v-show="loadEnd.listFormData && listFormData.addType === 1 && listFormData.showObj.service && (!listFormData.listArr || listFormData.listArr.length == 0)"><em class="tip"></em>该条件下暂无数据，建议调整！</div>
                            <!-- <div class="noListTip" v-show="loadEnd.listFormData && (!listFormData.listArr || listFormData.listArr.length < (listFormData.dataObj.load == 1 ? listFormData.dataObj.pageSize: listFormData.dataObj.totalCount ))">{{!listFormData.listArr || !listFormData.listArr.length ? '该条件下暂无数据，建议调整！': '该条件下仅有'+ listFormData.listArr.length +'条数据'}}</div> -->
                        </div>
                        <div class="listChosedShow listTableBox"  v-show="listFormData.addType == 2 && listFormData.showObj.service">
                            <div class="table shopTb" v-if="listFormData.listArr && listFormData.listArr.length">
                                <div class="thead">
                                    <div class="tr" v-if="listFormData.showObj.service == 'shop'">
                                        <div class="td detail">商品信息</div>
                                        <div class="td price">价格/操作</div>
                                        <div class="td opt">
                                            <a href="javascript:;" class="showProPop"  @click="showChosePop(2)"></a>
                                        </div>
                                    </div>
                                    <div class="tr" v-else>
                                        <div class="td detail" v-if="listFormData.showObj.service == 'article'">资讯内容</div>
                                        <div class="td detail" v-else>信息内容</div>
                                        <div class="td price">{{listFormData.showObj.service == 'business' ? '分类/' : (listFormData.showObj.service == 'job' ? ( listFormData.showObj.action == 'qzList' ? '发布人/':'薪资/') : '') }}操作</div>
                                        <div class="td opt">
                                            <a href="javascript:;" class="showProPop"  @click="showChosePop(2)"></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="tbody listSortTb" :data-sort="'listFormData.listArr'" data-drag=".tr" >
                                    <div class="tr" v-for="(item,ind) in listFormData.listArr" :key="'list' + item.id" :data-id="ind">
                                        <!-- 商城 -->
                                        <template v-if="listFormData.showObj.service == 'shop'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox"><img :src="item.litpic" onerror="this.src='/static/images/404.jpg'" alt=""></div>
                                                <h4>{{item.title}}</h4>
                                            </div>
                                            <div class="td price">
                                                <span>￥{{parseFloat(item.price)}}</span>
                                            </div>
                                        </template>

                                        <!-- 房产 -->
                                        <template v-else-if="listFormData.showObj.service == 'house'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox"><img :src="item.litpic" alt=""></div>
                                                <h4>
                                                    <span v-if="listFormData.showObj.action == 'zuList'">【{{item.rentype}}】</span>
                                                    <span v-else-if="[ 'spList','xzlList','cwList','cfList'].includes(listFormData.showObj.action) ">【{{listFormData.showObj.text}}{{item.type === 1 ? '出售' : item.type ? '转让' : '出租'}}】</span>
                                                    {{item.title}}
                                                </h4>
                                            </div>
                                            <div class="td price"></div>
                                        </template>

                                        <!-- 贴吧 -->
                                        <template v-else-if="listFormData.showObj.service == 'tieba'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox" v-if=" item.imgGroup && item.imgGroup.length"><img :src="item.imgGroup[0]" alt=""  onerror="this.src='/static/images/404.jpg'"></div>
                                                <h4> {{item.title}} </h4>
                                            </div>
                                            <div class="td price"></div>
                                        </template>

                                        <!-- 顺风车 -->
                                        <template v-else-if="listFormData.showObj.service == 'sfcar'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <h4> {{item.startaddr}} ———— {{item.endaddr}}</h4>
                                            </div>
                                            <div class="td price"></div>
                                        </template>

                                        <!-- 商家 -->
                                        <template v-else-if="listFormData.showObj.service == 'business'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox" v-if="item.logo"><img :src="item.logo" alt=""  onerror="this.src='/static/images/404.jpg'"></div>
                                                <h4>{{item.title}}</h4>
                                            </div>
                                            <div class="td price">{{item.typename.join('/')}}</div>
                                        </template>

                                        <!-- 招聘 -->
                                        <template v-else-if="listFormData.showObj.service == 'job'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <h4>{{item.title}}</h4>
                                            </div>
                                            <div class="td price" v-if="listFormData.showObj.action == 'qzList'">{{item && item.nickname ? item.nickname : ''}}</div>
                                            <div class="td price" v-else-if="listFormData.showObj.action == 'pgList'">{{item.min_salary}}{{item.min_salary && item.max_salary ? '-' : ''}}{{item.max_salary}}</div>
                                            <div class="td price" v-else>{{item.show_salary}}</div>
                                        </template>

                                        <!-- 分类信息 -->
                                        <template v-else-if="listFormData.showObj.service == 'info'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox" v-if="item.litpic"><img :src="item.litpic" alt=""  onerror="this.src='/static/images/404.jpg'"></div>
                                                <h4>{{item.title}}</h4>
                                            </div>
                                            
                                        </template>

                                        <!-- 资讯 -->
                                        <template v-else-if="listFormData.showObj.service == 'article'">
                                            <div class="td detail">
                                                <a href="javascript:;" class="sort_btn"></a>
                                                <div class="picbox" v-if="item.litpic"><img :src="item.litpic" alt=""  onerror="this.src='/static/images/404.jpg'"></div>
                                                <h4 v-html="item.title"></h4>
                                            </div>
                                            <div class="td price"></div>
                                            
                                        </template>

                                        <!-- 公共部分 -->
                                        <div class="td opt">
                                            <a href="javascript:;" class="change" @click="changeChosePro(item.id)">更换</a>
                                            <a href="javascript:;" class="del"  @click="delChosedPro(item.id)">删除</a>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <a href="javascript:;" class="add_tr" v-if="listFormData.listArr.length < 50" @click="showChosePop(2)"><s></s>添加{{listFormData.showObj && listFormData.showObj.service && ['shop','business'].includes(listFormData.showObj.service)  ? (listFormData.showObj.service == 'shop' ? '商品' : '商家') : '数据'}}<span v-if="listFormData.listArr && listFormData.listArr.length">({{listFormData.listArr.length}}/{{countLimit(currEditPart)}})</span></a>
                        </div>
                        
                        <div class="style_item" v-if="listFormData.addType == 1 && listFormData.showObj.service">
                            <!-- <div class="style_options">
                                <label for="">失效内容：</label>
                                <div class="radioBox split_options ">
                                    <span :class="[{'onChose':listFormData.failure == 1}]" @click="listFormData.failure = 1"><s></s>显示</span>
                                    <span :class="[{'onChose':listFormData.failure == 0}]" @click="listFormData.failure = 0"><s></s>隐藏</span>
                                </div>
                            </div> -->
                            <div class="style_options">
                                <label for="" style="align-self:flex-start; line-height:40px;">显示条数：</label>
                                <div class="radioBox split_options pageSizeSet">
                                    <span :class="[{'onChose':listFormData.dataObj.load == 1},{'disabled': currEditInd !== (showContainerArr.length - 1) && unlimitedLoad}]" @click="checkUnlimitedLoad(); changeLoad(1) "><s></s>无限加载，每次加载<el-input class="pageSizeInp" v-model="listFormData.dataObj.pageSize" @change="changeListLoad"><template slot="append">条</template></el-input><em class="tip">*建议10-30之间</em></span>
                                    <span :class="[{'onChose':listFormData.dataObj.load == 2}]" @click="listFormData.dataObj.load = 2;changeListLoad()"><s></s>固定数量<el-input class="pageSizeInp" :placeholder="(currEditInd < (showContainerArr.length - 1) ? 16 : 50)" v-model="listFormData.dataObj.totalCount" @input="val => checkLimit(val,currEditInd < (showContainerArr.length - 1) ? 16 : 50)" @change="changeListLoad();"><template slot="append">条</template></el-input><em class="tip">*固定条数最多设置{{currEditInd < (showContainerArr.length - 1) ? 16 : 50}}</em></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 没设置数据源  以下内容需要隐藏 -->
                <template v-if="listFormData.showObj.service">
                
                    <div class="modstyleBox">
                        <h2 class="stitle flex_l">列表样式</h2>
                        <div class="sContent">
                            <div class="style_options">
                                <template v-if="listFormData.showObj.service && listFormData.showObj.service != 'sfcar' && listFormData.showObj.service != 'business'  && listFormData.showObj.service != 'shop'">
                                    
                                    <label for="">列表样式：</label>
                                    <div class="radioBox split_options " style="margin-right:20px;">
                                        <span :class="[{'onChose':listFormData.dataObj.cardStyle == true}]" @click="listFormData.dataObj.cardStyle = true,listCardChange()"><s></s>卡片</span>
                                        <span :class="[{'onChose':listFormData.dataObj.cardStyle == false}]" @click="listFormData.dataObj.cardStyle = false,listCardChange()"><s></s>列表</span>
                                    </div>
                                </template>
                                <!-- 开启无限加载时禁用显示查看看到 -->
                                <!-- :disabled="listFormData.dataObj.load == 1 && listFormData.addType === 1" -->
                                <el-checkbox  v-model="listFormData.more.show"  :class="{'is-disabled':listFormData.dataObj.load == 1 && listFormData.addType === 1}" @change="value => {checkMoreAvailable(value,listFormData),setListMore(value)}" >显示查看更多</el-checkbox>
                            </div>
                            <!-- <el-checkbox v-model="listFormData.dataObj.cardStyle">显示卡片样式</el-checkbox> -->
                            <div class="styletype " v-show="listFormData.showObj.service == 'shop' || listFormData.showObj.service == 'business'">
                                <ul>
                                    <!-- <li>
                                        <div class="icon"></div>
                                        <p>大图</p>
                                    </li>
                                    <li>
                                        <div class="icon"></div>
                                        <p>双列</p>
                                    </li>
                                    <li>
                                        <div class="icon"></div>
                                        <p>列表</p>
                                    </li>
                                    <li>
                                        <div class="icon"></div>
                                        <p>瀑布流</p>
                                    </li> -->
                                    <li @click="listFormData.dataObj.styletype = 2; listFormData.dataObj.modCon.imgScale = (listFormData.dataObj.modCon.imgScale == 1 ? 2 : listFormData.dataObj.modCon.imgScale)">
                                        <div class="icon"></div>
                                        <p>双列</p>
                                    </li>
                                    <li @click="listFormData.dataObj.styletype = 3; listFormData.dataObj.modCon.imgScale = (listFormData.dataObj.modCon.imgScale == 1 ? 3 : listFormData.dataObj.modCon.imgScale)" v-if="listFormData.showObj.service == 'business'">
                                        <div class="icon"></div>
                                        <p>列表</p>
                                    </li>
                                  
                                    <li @click="setFlow()">
                                        <div class="icon"></div>
                                        <p>瀑布流</p>
                                    </li>
                                </ul>
                                <s :class="['slide4',{'slide3':listFormData.showObj.service == 'business'}]" :style="listFormData.dataObj.styletype == 4 ? 'left:calc('+(100 / (listFormData.showObj.service == 'business' ? 3 : 2)  * (listFormData.showObj.service == 'business' ? 2 : 1))+'% - 2px);': (listFormData.dataObj.styletype == 3 ? 'left:calc('+(100 / (listFormData.showObj.service == 'business' ? 3 : 2)  * (listFormData.showObj.service == 'business' ? 1 : 0))+'% - 2px);' : '') "></s>
                            </div>
                            <div class="flexItem marginTop">
                                <div class="style_options item " v-if="listFormData.dataObj.cardStyle" >
                                    <!-- 非瀑布/双列列此处需改成列表 -->
                                    <!-- listFormData.dataObj.styletype == 4 ||listFormData.dataObj.styletype == 2 ||  -->
                                    <label for="">{{listFormData.dataObj.cardStyle ? '卡片':'列表'}}</label> 
                                    <el-color-picker class="colorpicker" popper-class="borderColorPicker" @active-change="(value) => {activeChangeColor(value,'listFormData.dataObj.style.listBgColor'); changeMoreBtn()}" v-model="listFormData.dataObj.style.listBgColor" @change="value => resetColor('listFormData.dataObj.style.listBgColor',value)"></el-color-picker>
                                </div>
                                <div class="style_options item">
                                    <label for="">{{listFormData.dataObj.cardStyle ? '背景':'列表'}}</label>
                                    <el-color-picker :class="['colorpicker',{'opciatyColor':!listFormData.dataObj.style.bgColor}] " v-if="!listFormData.dataObj.cardStyle" popper-class="borderColorPicker" @active-change="(value) => {activeChangeColor(value,'listFormData.dataObj.style.bgColor');changeMoreBtn()}" v-model="listFormData.dataObj.style.bgColor" @change="value => resetColor('listFormData.dataObj.style.bgColor',value,'#ffffff')"></el-color-picker>
                                    <el-color-picker :class="['colorpicker',{'opciatyColor':!listFormData.dataObj.style.cardBgColor}] "  v-else popper-class="borderColorPicker" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.style.cardBgColor')" v-model="listFormData.dataObj.style.cardBgColor" @change="value => resetColor('listFormData.dataObj.style.cardBgColor',value,'')"></el-color-picker>
                                </div>
                                <!-- 此处只有非瀑布流 才会有 -->
                                <div class="style_options item" v-if="!listFormData.dataObj.cardStyle && (listFormData.dataObj.styletype == 1 || listFormData.dataObj.styletype == 3)">
                                    <label for="">分割线</label>
                                    <el-color-picker class="colorpicker"  popper-class="borderColorPicker" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.style.lineColor')" v-model="listFormData.dataObj.style.lineColor" @change="value => resetColor('listFormData.dataObj.style.lineColor',value)"></el-color-picker>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- 各模块不同内容 s-->

                    <!-- 商城 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'shop'">
                        <h2 class="stitle flex_l">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id" v-show="listFormData.dataObj.styletype == 4 || item.id != 1"><s></s>{{ item.text + (item.id == 1 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">商品标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">价格：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.pcolor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.pcolor')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.pcolor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.pcolor')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.pcolor')">重置</button>
                                    </div>
                                </div>
                            </div>
                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">返佣：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">会员价：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.huiyuan.show">显示</el-checkbox>
                                </div>
                                <div class="style_options" >
                                    <label for="">划线价格：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.huaxian.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.huaxian.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.huaxian.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.huaxian.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.huaxian.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.huaxian.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">活动状态：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.hdState.show">显示</el-checkbox>
                                </div>
                                <div class="style_options" >
                                    <label for="">销量：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.sale.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.sale.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.sale.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.sale.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.sale.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.sale.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">可用券：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.quan.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.quan.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.quan.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.quan.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.quan.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.quan.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">副标题：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.stitle.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.stitle.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.stitle.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.stitle.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.stitle.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.stitle.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <div class="style_item marginTop ">
                                <div class="style_options" >
                                    <label for="">置顶标签：</label>
                                    <el-checkbox>标签1</el-checkbox>
                                    <el-checkbox>标签2</el-checkbox>
                                    <el-checkbox>自定义</el-checkbox>
                                </div>
                                <div class="style_options" >
                                    <label for="">加购图标：</label>
                                    <el-checkbox v-for="item in 4" :key="item" :value="listFormData.dataObj.modCon.cartIcon.cartStyle === item"   @change="(value)=>checkBoxChange(value,item,'listFormData.dataObj.modCon.cartIcon.cartStyle')">{{item == 4 ? '自定义' : ('图标' + item)}}</el-checkbox>
                                </div>
                                <template v-if="listFormData.dataObj.modCon.cartIcon.cartStyle != 4">
                                    <div class="style_options" >
                                        <label for="">图标：</label>
                                        <div :class="['colorpickerBox',{'noColor':!listFormData.dataObj.modCon.cartIcon.style.color}]">
                                            <el-color-picker  popper-class="borderColorPicker" class="colorpicker" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.cartIcon.style.color')" v-model="listFormData.dataObj.modCon.cartIcon.style.color" @change="value => pickerChangeColor(value,'listFormData.dataObj.modCon.cartIcon.style.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.cartIcon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.cartIcon.style.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.cartIcon.style.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">背景：</label>
                                        <div :class="['colorpickerBox',{'noColor':!listFormData.dataObj.modCon.cartIcon.style.bgColor}]">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.cartIcon.style.bgColor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.cartIcon.style.bgColor')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.cartIcon.style.bgColor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.cartIcon.style.bgColor')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.cartIcon.style.bgColor')">重置</button>
                                        </div>
                                    </div>
                                </template>
                                
                                <template v-else>
                                    <div class="style_options" >
                                        <label for=""
                                        style="align-self:flex-start; padding-top:6px;">图标：</label>
                                        <div class="img_up fn-clear">
                                            <div :class="['upbtn vip_icon', {'hasup':listFormData.dataObj.modCon.cartIcon.path}]  ">
                                                <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" id="cartIcon_filedata" data-type="cartIcon">
                                                <img :src="listFormData.dataObj.modCon.cartIcon.path"  alt="" v-if="listFormData.dataObj.modCon.cartIcon.path">
                                                <span>更换图片</span>
                                            </div>
                                            <div class="imgText">
                                                <p>建议尺寸70x70px</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">图标大小：</label>
                                        <el-slider :min="40" :max="90" v-model="listFormData.dataObj.modCon.cartIcon.style.size" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                        <div class="colorShowBox" style="background-color: #E8EAEC;">
                                            <input type="number" class="colorShow" @chnage="checkMax(40,90,'listFormData.dataObj.modCon.cartIcon.style.size')" :value="listFormData.dataObj.modCon.cartIcon.style.size"  >
                                            <span class="single back">px</span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            
                            </template>


                        </div>
                    </div>

                    <!-- 招聘 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'job'">
                        <h2 class="stitle flex_l">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options" >
                                    <label for="">{{listFormData.dataObj.interface.action == 'postList' ? '招聘' : '信息'}}标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                    <el-checkbox>查看过置灰</el-checkbox>
                                </div>
                                <div class="style_options" >
                                    <label for="">薪资：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.scolor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.scolor')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.scolor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.scolor')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.scolor')">重置</button>
                                    </div>
                                </div>
                            </div>
                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <!-- 第二版 出 -->
                            <div class="style_item marginTop" v-if="listFormData.dataObj.interface.action == 'postList'">
                                <div class="style_options" >
                                    <label for="">经验要求：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.jingyan.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.jingyan.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.jingyan.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.jingyan.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.jingyan.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.jingyan.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">学历要求：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.xueli.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.xueli.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.xueli.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.xueli.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.xueli.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.xueli.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">公司福利：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fuli.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fuli.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fuli.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fuli.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fuli.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fuli.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">公司名：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.cname.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.cname.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.cname.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.cname.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.cname.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.cname.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">公司信息：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.cinfo.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.cinfo.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.cinfo.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.cinfo.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.cinfo.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.cinfo.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">就职区域：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.area.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.area.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.area.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.area.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.area.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.area.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 普工 -->
                            <template v-if="listFormData.dataObj.interface.action == 'pgList' || listFormData.dataObj.interface.action == 'qzList'">
                                <!-- 招聘 -->
                                <div class="style_item marginTop" v-if="listFormData.dataObj.interface.action == 'pgList'">
                                    <div class="style_options" >
                                        <label for="">职位福利：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fuli.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fuli.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fuli.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fuli.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fuli.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.fuli.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">全职兼职：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.posttype.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.posttype.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.posttype.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.posttype.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.posttype.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.posttype.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">学历经验：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.jingyan.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.jingyan.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.jingyan.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.jingyan.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.jingyan.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.jingyan.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">联系人：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.phone.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.phone.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.phone.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.phone.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.phone.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.phone.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">地区：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.carea.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.carea.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.carea.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.carea.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.carea.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.carea.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">就职地区：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.area.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.area.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.area.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.area.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.area.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.area.color')">重置</button>
                                        </div>
                                    </div>

                                </div>
                                <!-- 求职 -->
                                <div class="style_item marginTop" v-if="listFormData.dataObj.interface.action == 'qzList'">
                                    <div class="style_options" >
                                        <label for="">求职地区：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.area.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.area.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.area.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.area.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.area.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.area.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">联系人：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.phone.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.phone.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.phone.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.phone.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.phone.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.phone.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">性别：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.sex.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.sex.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.sex.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.sex.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.sex.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.sex.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">年龄：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.age.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.age.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.age.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.age.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.age.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.age.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">经验：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.jingyan.show">显示</el-checkbox>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.jingyan.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.jingyan.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.jingyan.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.jingyan.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.jingyan.color')">重置</button>
                                        </div>
                                    </div>
                                    

                                </div>

                                <div class="style_item marginTop">
                                    <div class="style_options" >
                                        <label for="">联系电话：</label>
                                        <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.phone.show">显示</el-checkbox>
                                    </div>
                                    <div class="style_options">
                                        <label for="">按钮文本：</label>
                                        <div class="inpbox">
                                            <el-input maxlength="6" show-word-limit v-model="listFormData.dataObj.modCon.phone.showText"></el-input>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">文本颜色：</label>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.phone.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.phone.color')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.phone.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.phone.color')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.phone.color')">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">按钮颜色：</label>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.phone.bgColor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.phone.bgColor')"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.phone.bgColor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.phone.bgColor')" maxlength="6">
                                            <button @click="resetColor('listFormData.dataObj.modCon.phone.bgColor')">重置</button>
                                        </div>
                                    </div>

                                </div>
                            </template>

                            </template>
                        </div>

                    </div>

                    

                    <!-- 分类信息 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'info'">
                        <h2 class="stitle flex_l">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" v-show="listFormData.dataObj.styletype == 4 || item.id != 1" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id"><s></s>{{ item.text + (item.id == 2 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">文本字号：</label>
                                    <div class="split_options radioBox" >
                                        <div class="split_options radioBox" >
                                            <span :class="{'onChose':listFormData.dataObj.modCon.style.fontSize == 30}" @click="listFormData.dataObj.modCon.style.fontSize = 30"><s></s>常规</span>
                                            <span :class="{'onChose':listFormData.dataObj.modCon.style.fontSize == 32}"  @click="listFormData.dataObj.modCon.style.fontSize = 32"><s></s>加大</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" v-if="listFormData.dataObj.modCon.style">
                                    <label for="">文本内容：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                                <!-- 发布人 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">发布者：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">发布时间：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">信息标签：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 定位/图标 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">坐标定位：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 收藏/图标 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">收藏：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">仅图标</el-checkbox>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">图标+文字</el-checkbox>
                                
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">默认图标：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">已收藏：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 电话 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">联系电话：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">样式一</el-checkbox>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">样式二</el-checkbox>
                                </div>
                                <div class="style_options">
                                    <label for="">按钮文本：</label>
                                    <div class="inpbox" style="flex-grow:1;">
                                        <el-input maxlength="6" show-word-limit></el-input>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本颜色：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">按钮颜色：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>

                            </div>

                            <!-- 推广 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">推广红包：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                </div>
                                <div class="style_options">
                                    <label for="">显示文本：</label>
                                    <div class="inpbox" style="flex-grow:1;">
                                        <el-input maxlength="8" show-word-limit></el-input>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 分享 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                </div>
                                <div class="style_options">
                                    <label for="">显示文本：</label>
                                    <div class="inpbox" style="flex-grow:1;">
                                        <el-input maxlength="8" show-word-limit></el-input>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            </template>
                            
                            <!-- 结束 -->
                        </div>
                    </div>

                    <!-- 房产 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'house'">
                        <h2 class="stitle flex_l">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" v-show="listFormData.dataObj.styletype == 4 || item.id != 1" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id"><s></s>{{ item.text + (item.id == 3 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">标题字号：</label>
                                    <div class="split_options radioBox" >
                                        <div class="split_options radioBox" >
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 30}]" @click="listFormData.dataObj.modCon.style.fontSize = 30"><s></s>常规</span>
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 32}]"  @click="listFormData.dataObj.modCon.style.fontSize = 32"><s></s>加大</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">价格：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.pcolor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.pcolor')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.pcolor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.pcolor')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.pcolor')">重置</button>
                                    </div>
                                </div>
                            </div>
                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">面积：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" v-if="!['spList','cfList','cwList','xzlList'].includes(listFormData.dataObj.interface.action)">
                                    <label for="">几室居：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" v-if="listFormData.dataObj.interface.action == 'saleList'">
                                    <label for="">单价：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <template>
                                        <label for="" v-if="listFormData.dataObj.interface.action == 'zuList'">租房类型：</label>
                                        <label for="" v-else-if="listFormData.dataObj.interface.action == 'spList' || listFormData.dataObj.interface.action == 'xzlList'">房源类型：</label>
                                        <label for="" v-else-if="listFormData.dataObj.interface.action == 'cfList'">发布类型：</label>
                                        <label for="" v-else-if="listFormData.dataObj.interface.action == 'cwList'">车位类型：</label>
                                        <label for="" v-else>建筑类型：</label>

                                    </template>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 小区 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">建筑性质：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">建成时间：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">房源情况</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">位置：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <div class="style_item marginTop">
                                <div class="style_options">
                                    <label for="">位置</label>
                                    <el-checkbox>区域</el-checkbox>
                                    <el-checkbox>具体位置</el-checkbox>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>
                            </template>
                        </div>
                    </div>

                    <!-- 贴吧 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'tieba'">
                        <h2 class="stitle">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" v-show="listFormData.dataObj.styletype == 4 || item.id != 1" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id"><s></s>{{ item.text + (item.id == 3 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">标题字号：</label>
                                    <div class="split_options radioBox" >
                                        <div class="split_options radioBox" >
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 30}]" @click="listFormData.dataObj.modCon.style.fontSize = 30"><s></s>常规</span>
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 32}]"  @click="listFormData.dataObj.modCon.style.fontSize = 32"><s></s>加大</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本内容：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">发布人：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.authColor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.authColor')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.authColor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.authColor')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <!-- 帖子相关设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">帖子分类：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">ip地址：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">浏览量：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 评论相关设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">评论：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标：</label>
                                    
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 点赞相关设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">点赞：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">默认图标：</label>
                                    
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">已赞颜色：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>
                            </template>
                        </div>
                    </div>

                    <!-- 顺风车 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'sfcar'">
                        <h2 class="stitle">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">标题字号：</label>
                                    <div class="split_options radioBox" >
                                        <div class="split_options radioBox" >
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 30}]" @click="listFormData.dataObj.modCon.style.fontSize = 30"><s></s>常规</span>
                                            <span :class="[{'onChose':listFormData.dataObj.modCon.style.fontSize == 32}]"  @click="listFormData.dataObj.modCon.style.fontSize = 32"><s></s>加大</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">出发日期：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.startDate" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.startDate')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.startDate).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.startDate')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.startDate')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">出发时间：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.startTime" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.startTime')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.startTime).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.startTime')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.startTime')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <!-- 标签设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options">
                                    <label for=""><span>顺风车/乘客<br/>标签</span><span>：</span></label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for=""></label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                
                                <div class="style_options">
                                    <label for=""><span>货车/货物<br/>标签</span><span>：</span></label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for=""></label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 信息设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">发布时间：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">备注：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">座位/人数：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">信息标签：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 电话 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">联系电话：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                </div>
                                <div class="style_options">
                                    <label for="">按钮文本：</label>
                                    <div class="inpbox" style="flex-grow:1;">
                                        <el-input maxlength="6" show-word-limit></el-input>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">按钮：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>

                            </div>
                            </template>
                        </div>
                    </div>

                    <!-- 大商家 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'business'">
                        <h2 class="stitle">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" v-show="listFormData.dataObj.styletype == 4 || item.id != 1" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id"><s></s>{{ item.text + (item.id == 3 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                            </div>
                            <!-- 此处暂时不渲染 -->
                            <template v-if="false">
                            <!-- 商家经营相关设置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">商家评分：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">经营分类：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">营业时间：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">商家标签：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">店内设施：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for=""></label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.fanyong.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.fanyong.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.fanyong.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.fanyong.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.fanyong.color')">重置</button>
                                    </div>
                                </div>

                            </div>

                            <!-- 商家位置 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">商家位置：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">区域</el-checkbox>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">附近地标</el-checkbox>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">具体地址</el-checkbox>
                                    
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">图标：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>

                            </div>

                            <!-- 买单优惠 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">买单优惠：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    
                                </div>
                                <div class="style_options" >
                                    <label for="">“惠”：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">标签：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>

                            </div>

                            <!-- 下单返佣 -->
                            <div class="style_item marginTop">
                                <div class="style_options" >
                                    <label for="">下单返佣：</label>
                                    <el-checkbox class="marginLeft" v-model="listFormData.dataObj.modCon.fanyong.show">显示</el-checkbox>
                                    
                                </div>
                                <div class="style_options" >
                                    <label for="">文本：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options" >
                                    <label for="">标签：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>

                            </div>
                            </template>
                        </div>
                    </div>

                    <!-- 资讯 -->
                    <div class="modstyleBox" v-if="listFormData.showObj.service == 'article'">
                        <h2 class="stitle">列表内容</h2>
                        <div class="sContent">
                            <div class="style_item">
                                <div class="style_options">
                                    <label for="">图片比例：</label>
                                    <div class="radioBox split_options">
                                        <span v-for="item in imgScaleArr" v-show="listFormData.dataObj.styletype == 4 || item.id != 1" :class="{'onChose': item.id == listFormData.dataObj.modCon.imgScale}" @click="listFormData.dataObj.modCon.imgScale = item.id"><s></s>{{ item.text + (item.id == 3 ? '(默认)' : '')}}</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">新闻标题：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">媒体：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.style.sourceColor" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.style.sourceColor')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.style.sourceColor).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.style.sourceColor')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.style.sourceColor')">重置</button>
                                    </div>
                                </div>
                            </div>
                            <div class="style_item marginTop fn-hide">
                                <div class="style_options">
                                    <label for="">发布时间：</label>
                                    <el-checkbox class="marginLeft"  v-model="listFormData.dataObj.modCon.pubDate.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.pubDate.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.pubDate.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.pubDate.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.pubDate.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.pubDate.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">浏览量：</label>
                                    <el-checkbox class="marginLeft"  v-model="listFormData.dataObj.modCon.read.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.read.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.read.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.read.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.read.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.read.color')">重置</button>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">浏览量图标：</label>
                                    <el-checkbox class="marginLeft"  v-model="listFormData.dataObj.modCon.readIcon.show">显示</el-checkbox>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.dataObj.modCon.readIcon.color" @active-change="(value) => activeChangeColor(value,'listFormData.dataObj.modCon.readIcon.color')"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.dataObj.modCon.readIcon.color).replace('#','')" @change="changeColor('listFormData.dataObj.modCon.readIcon.color')" maxlength="6">
                                        <button @click="resetColor('listFormData.dataObj.modCon.readIcon.color')">重置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <!-- 各模块不同内容 e-->

                    <!-- 信息流广告 -->
                    <div class="modstyleBox flowAdvBox" v-if="['article','info','tieba','sfcar'].includes(listFormData.showObj.service)">
                        <h2 class="stitle flex_l">
                            信息流广告
                            <span class="tip">*编辑页面不显示广告效果，前台访问才会显示！<a href="https://help.kumanyun.com/help-222-1116.html" target="_blank" class="link">查看广告教程</a></span>
                        </h2>
                        <div class="sContent">
                            <div class="style_options">
                                <label for="">广告：</label>
                                <div class="split_options radioBox">
                                    <span :class="{'onChose':listFormData.dataObj.modCon.flowAdv.show}" @click="listFormData.dataObj.modCon.flowAdv.show = true"><s></s>开启</span>
                                    <span :class="{'onChose':!listFormData.dataObj.modCon.flowAdv.show}" @click="listFormData.dataObj.modCon.flowAdv.show = false"><s></s>关闭</span>
                                </div>
                            </div>
                            <div class="style_item marginTop largeInp" v-show="listFormData.dataObj.modCon.flowAdv.show">
                                <div class="style_options" v-if="!curr_cp['platform'] || curr_cp['platform'].id == 'app'">
                                    <label for="">安卓端：</label>
                                    <div class="imgText inpGroup">
                                        <div class="inpbox" >
                                            <el-input class="btn" v-model="listFormData.dataObj.modCon.flowAdv.android.app_id"><template slot="prepend">媒体ID</template></el-input>
                                        </div>
                                        <div class="inpbox" >
                                            <el-input  class="iconName"  v-model="listFormData.dataObj.modCon.flowAdv.android.placement_id"><template slot="prepend">广告位ID</template></el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" v-if="!curr_cp['platform'] || curr_cp['platform'].id == 'h5'">
                                    <label for="">H5端：</label>
                                    <div class="imgText inpGroup">
                                        <div class="inpbox" >
                                            <el-input class="btn"  v-model="listFormData.dataObj.modCon.flowAdv.h5.app_id"><template slot="prepend">媒体ID</template></el-input>
                                        </div>
                                        <div class="inpbox" >
                                            <el-input  class="iconName"  v-model="listFormData.dataObj.modCon.flowAdv.h5.placement_id" ><template slot="prepend">广告位ID</template></el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options" v-if="!curr_cp['platform'] || curr_cp['platform'].id == 'wxmini'">
                                    <label for="">微信小程序：</label>
                                    <div class="imgText inpGroup">
                                        <div class="inpbox" >
                                            <el-input class="btn" v-model="listFormData.dataObj.modCon.flowAdv.wxmini"><template slot="prepend">广告位ID</template></el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="style_options"  v-if="!curr_cp['platform'] || curr_cp['platform'].id == 'dymini'">
                                    <label for="">抖音小程序：</label>
                                    <div class="imgText inpGroup">
                                        <div class="inpbox" >
                                            <el-input class="btn" v-model="listFormData.dataObj.modCon.flowAdv.dymini"><template slot="prepend">广告位ID</template></el-input>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 查看更多 滚动加载不显示-->
                    <div class="modstyleBox contentSetBox" v-show="(listFormData.dataObj.load == 2 || listFormData.addType == 2)" >
                        <h2 class="stitle">查看更多</h2>
                        <div class="sContent">
                            <div class="style_options">
                                <label for="">查看更多：</label>
                                <div class="radioBox split_options ">
                                    <span :class="[{'onChose':listFormData.more.show}]" @click="listFormData.more.show = true,setListMore(1)"><s></s>开启</span>
                                    <span :class="[{'onChose':!listFormData.more.show}]" @click="listFormData.more.show = false"><s></s>关闭</span>
                                </div>
                            </div>
                            <div class="item style_item " v-show=" listFormData.more.show">
                                <div class="style_options" >
                                    <label for="">显示文本：</label>
                                    <div class="textSetBox">
                                        <el-input placeholder="请输入文本" maxlength="10" show-word-limit v-model="listFormData.more.text" @focus="setListMore(1)" @change="setListMore(1)"></el-input>
                                    </div>
                                    <span :class="['showArr',{'noColor':(listFormData.more && listFormData.more.show && !listFormData.more.arr)}] " @click="listFormData.more.arr = !listFormData.more.arr"></span>
                                </div>
                                <div class="style_options"  @click="changeLink('listFormData.more.link')" >
                                    <label for="">链接：</label>
                                    <div class="inpbox linkbox">
                                        <s><img src="/static/images/admin/link.png" alt=""></s>
                                        <input type="text" placeholder="请选择链接" class="iconLink"  readonly :value="listFormData.more.linkInfo.linkText ? listFormData.more.linkInfo.linkText : listFormData.more.link " @focus="setListMore(1)" @change="setListMore(1)">
                                    </div>
                                </div>


                                <div class="style_options" >
                                    <label for="">文本颜色：</label>
                                    <div class="colorpickerBox">
                                        <el-color-picker  class="colorpicker" v-model="listFormData.more.style.color" @active-change="(value) => {activeChangeColor(value,'listFormData.more.style.color'); setListMore(1)}"></el-color-picker>
                                    </div>
                                    <div class="colorShowBox">
                                        <span class="single">#</span>
                                        <input type="text" class="colorShow" :value="(listFormData.more.style.color).replace('#','')" @change="changeColor('listFormData.more.style.color');setListMore(1)" @focus="setListMore(1)" maxlength="6">
                                        <button @click="resetColor('listFormData.more.style.color'); setListMore(1)">重置</button>
                                    </div>
                                </div>
                                <template v-if="listFormData.dataObj.load == 2">
                                    <div class="style_options" >
                                        <label for="">按钮颜色：</label>
                                        <div class="colorpickerBox">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.more.style.btnBg" @active-change="(value) => {activeChangeColor(value,'listFormData.more.style.btnBg'); setListMore(1)}"></el-color-picker>
                                        </div>
                                        <div class="colorShowBox">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.more.style.btnBg).replace('#','')" @focus="setListMore(1)" @change="changeColor('listFormData.more.style.btnBg');setListMore(1)" maxlength="6">
                                            <button @click="resetColor('listFormData.more.style.btnBg'); setListMore(1)">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">按钮边框：</label>
                                        <div :class="['colorpickerBox',{'noColor':!listFormData.more.style.btnBorder}]">
                                            <el-color-picker  class="colorpicker" v-model="listFormData.more.style.btnBorder" @active-change="(value) => {activeChangeColor(value,'listFormData.more.style.btnBorder'); setListMore(1)} "></el-color-picker>
                                        </div>
                                        <div :class="['colorShowBox']">
                                            <span class="single">#</span>
                                            <input type="text" class="colorShow" :value="(listFormData.more.style.btnBorder).replace('#','')" @change="changeColor('listFormData.more.style.btnBorder');setListMore(1)" maxlength="6" @focus="setListMore(1)">
                                            <button @click="resetColor('listFormData.more.style.btnBorder'); setListMore(1)">重置</button>
                                        </div>
                                    </div>
                                    <div class="style_options" >
                                        <label for="">按钮高度：</label>
                                        <el-slider :min="70" :max="120" v-model="listFormData.more.style.btnHeight" tooltip-class="tooltip_show" @change="setListMore(1)"  :show-tooltip="showTooltip"></el-slider>
                                        <div class="colorShowBox" style="background-color: #E8EAEC;">
                                            <input type="number" class="colorShow"  @focus="setListMore(1)" @change="setListMore(1);checkMax(70,120,'listFormData.more.style.btnHeight')" :value="listFormData.more.style.btnHeight"  >
                                            <span class="single back">px</span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- 列表广告  只有瀑布流或者2列分布有 -->
                    <!-- listFormData.dataObj.modCon.advList && listFormData.dataObj.modCon.advList.list && -->
                    <div class="modstyleBox contentSetBox" v-if=" (listFormData.dataObj.styletype == 2 || listFormData.dataObj.styletype == 4)">
                        <h2 class="stitle">列表广告</h2>
                        <div class="sContent">
                            <div class="style_options">
                                <label for="">广告：</label>
                                <div class="radioBox split_options">
                                    <span :class="{'onChose':listFormData.dataObj.modCon.advList.show}" @click="showListAdv(1)"><s></s>开启</span>
                                    <span :class="{'onChose':!listFormData.dataObj.modCon.advList.show}" @click=" showListAdv(0)"><s></s>关闭</span>
                                </div>
                            </div>
                            <div  class="btnsBox btn_item advList sortBox" v-show="listFormData.dataObj.modCon.advList.show" data-drag=".item" data-sort="listFormData.dataObj.modCon.advList.list" data-filter="input">
                                <div class="option_item item" v-for="(item,ind) in listFormData.dataObj.modCon.advList.list" :key="'listAdv_' + ind" :data-id="ind">
                                    <s class="del_item" @click="delBtns('listadv',ind)"></s>
                                    <s class="left_icon"><img src="{#$cfg_basehost#}/static/images/admin/order_icon.png" alt=""></s>
                                    <div class="item_con">
                                        <div class="img_up fn-clear">
                                            <div :class="['upbtn fullW',{'hasup':item.path}]">
                                                <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" :id="'listadv_'+ind+'_filedata'" :data-type="'listadv_'+ind">
                                                <img :src="item.path" alt="">
                                                <span>更换图片</span>
                                            </div>
                                            <div class="imgText">
                                                <div class="inpbox linkbox linkChose"  @click="changeLink('listFormData.dataObj.modCon.advList.list.' + ind + '.link')">
                                                    <s><img :src="'{#$cfg_basehost#}/static/images/admin/link.png'" alt=""></s>
                                                    <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="item.linkInfo.linkText ? item.linkInfo.linkText  : item.link ">
                                                </div>
                                                <p>建议图标尺寸40*40px</p>
                                            </div>
                                        </div>
                                    
                                    </div>
                                </div>
                                <a href="javascript:;" class="add_more" @click="addMoreBtn('listadv')" ><s></s>添加广告</a>
                            </div>
                            
                        </div>
                    </div>

                </template>


                <!-- 间距设置 -->
                <div class="marginSetBox modstyleBox" >
                    <h2 class="stitle"  @click="showMarginSet = !showMarginSet">间距设置</h2>
                    <el-collapse-transition>
                        <div class="style_item">
                            <div class="sContent">
                                <div class="style_options" >
                                    <label for="">圆角值：</label>
                                    <el-slider :min="0" :max="40" v-model="listFormData.dataObj.style.borderRadius" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number" class="colorShow" @input="checkMax(0,40,'listFormData.dataObj.style.borderRadius')" :value="listFormData.dataObj.style.borderRadius"  >
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">{{listFormData.dataObj.styletype == 3 || listFormData.dataObj.style == 1 ? '每条' : '中间'}}间隔：</label>
                                    <el-slider  :min="0" :max="listFormData.dataObj.styletype == 3 && !listFormData.dataObj.cardStyle ? 60 : 40"  v-model="listFormData.dataObj.style.splitMargin" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number" class="colorShow"  @input="checkMax(0,(listFormData.dataObj.styletype == 3 && !listFormData.dataObj.cardStyle ? 60 : 40),'listFormData.dataObj.style.splitMargin')" :value="listFormData.dataObj.style.splitMargin" >
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                                <div class="style_options">
                                    <label for="">上间距：</label>
                                    <el-slider  :min="0" :max="40"  v-model="listFormData.dataObj.style.marginTop" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number" class="colorShow"  @input="checkMax(0,40,'listFormData.dataObj.style.marginTop')" :value="listFormData.dataObj.style.marginTop" >
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                                
                                <div class="style_options" v-if="(listFormData.dataObj.styletype == 3 || listFormData.dataObj.styletype == 1) && listFormData.dataObj.cardStyle && listFormData.dataObj.style.marginLeft == 0">
                                    <label for="">内边距：</label>
                                    <el-slider  :min="0" :max="40"  v-model="listFormData.dataObj.style.padding" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number" class="colorShow"  @input="checkMax(0,40,'listFormData.dataObj.style.padding')" :value="listFormData.dataObj.style.padding" >
                                        <span class="single back">px</span>
                                    </div>
                                </div>

                                <div class="style_options">
                                    <label for="">左右间距：</label>
                                    <el-slider  :min="0" :max="40"  v-model="listFormData.dataObj.style.marginLeft" tooltip-class="tooltip_show"  :show-tooltip="showTooltip"></el-slider>
                                    <div class="colorShowBox" style="background-color: #E8EAEC;">
                                        <input type="number" class="colorShow"  @input="checkMax(0,40,'listFormData.dataObj.style.marginLeft')" :value="listFormData.dataObj.style.marginLeft" >
                                        <span class="single back">px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-collapse-transition>
                </div>
            </template>

        </div>
    </div>
</div>