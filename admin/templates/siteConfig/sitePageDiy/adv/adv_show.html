<!-- 瓷片广告位 -->
<div :class="['orderInfoBorderBox']"  v-if="modCon.id == 6">
    <!-- <div class="del_btn" @click="delModBox(modInd)"><s></s>删除</div> -->
    <div class="orderInfoBox cipianInfoBox" :style="getStyle('advInfo','', modCon.content)">
        <div :class="'cipianList cipianList'+  modCon.content.column"  :style="getStyle('advInfo','grid',modCon.content)" v-if="modCon.content.column !== 1">
            <div class="cipian" v-for="(adv,ind) in (modCon.content.list.slice(0,modCon.content.column))" :key="ind" :style="getStyle('advInfo','height', modCon.content)" >
                <img crossOrigin='anonymous' :src="adv.image ? (adv.image + '?v='+ Math.random()) : '/static/images/404.jpg'" onerror="this.src='/static/images/404.jpg'" alt="" v-if="adv.image">
            </div>
            
        </div>
        <el-carousel  :autoplay="false" v-else :height="(modCon.content.style.height / 2) + 'px'">
            <el-carousel-item class="cipian" v-for="(adv,lind) in modCon.content.list" :key="lind" :style="getStyle('advInfo','height', modCon.content)">
                <div class="cipian_img"  >
                    <img crossOrigin='anonymous'  style="width:100%; height:100%; object-fit:cover;" :src="adv.image ? (adv.image + '?v='+ Math.random()) : '/static/images/404.jpg'" onerror="this.src='/static/images/404.jpg'" alt="" v-if="adv.image">
                </div>
            </el-carousel-item>
        </el-carousel>
    </div>
</div> 