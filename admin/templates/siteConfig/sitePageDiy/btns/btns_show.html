<!-- 普通按钮组 -->
<div :class="['navBtnsConBox ' ,{'cardStyle': modCon.content.cardStyle}]" v-if="modCon.id == 27"  :style="'margin-top:'+ (modCon.content.style.marginTop / 2) +'px;' ">
    <div class="navBtnsCon"  v-if="modCon.content.slide == 1" :style="'border-radius:' + modCon.content.style.cardRadius/2 + 'px;  background:'+ ( modCon.content.cardStyle ? modCon.content.style.cardBg : '') +'; ' + (modCon.content.title.show || modCon.content.more.show ? '' : 'padding-top:10px;') + (modCon.content.cardStyle ? 'margin:0 '+(modCon.content.style.marginLeft / 2)+'px;' : '')">
        <div class="con_header" v-if="modCon.content.title.show || modCon.content.more.show  " :style="modCon.content.cardStyle ? '' : 'padding-left:0; padding-right:0;margin:0 '+(modCon.content.style.marginLeft / 2)+'px;'">
            <h4 class="con_title" :style="'color:'+ modCon.content.title.style.color +';'">{{modCon.content.title.show ? (modCon.content.title.text ?  modCon.content.title.text : '按钮组') : ''}}</h4>
            <span class="more" v-show="modCon.content.more.show" :style="'color:'+(modCon.content.more.style.color)+';'">{{modCon.content.more.text}}<s v-if="modCon.content.more.arr">
                <!-- <em :style="'filter: drop-shadow(10px 0 0 '+ modCon.content.more.style.color +')'"></em> -->
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 0 22 40"> <path fill-rule="evenodd"  opacity="0.6" :fill="modCon.content.more.style.color ? modCon.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg>
            </s></span>
        </div>
        <el-carousel :height="(modCon.content.style.btnSize / 2  + 32 )* modCon.content.layout + 'px'" @change="(value) => modCon.content.activeInd = value" indicator-position="none" :autoplay="false"  :arrow="arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout).length > 1 ? 'hover' : 'never'">
            <el-carousel-item v-for="(group,index) in (arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout))" :key="index" class="swiper-item">
                <ul :class="'grid' + modCon.content.column" :style="modCon.content.cardStyle ? '' :'padding:0 '+(modCon.content.style.marginLeft / 2)+'px;'">
                    <li v-for="(item,ind) in group">
                        <div class="icon" :style="'width:'+(modCon.content.style.btnSize / 2)+'px; height:'+(modCon.content.style.btnSize / 2)+'px;'">
                            <span class="lab" v-if="item.lab.show" :style="'background-color:' + item.lab.style.bgColor + '; color:' + item.lab.style.color + ';'">{{item.lab.text ? item.lab.text : '标签'}}</span>
                            <img crossOrigin='anonymous' :src="item.icon ? (item.icon + '?v=' + randomNum) : '/static/images/admin/siteMemberPage/default_icon1.png'" onerror="this.src='/static/images/admin/siteMemberPage/default_icon1.png'" alt="" :style="'border-radius:'+ (modCon.content.style.btnRadius ? (modCon.content.style.btnRadius / 2) : 0 ) +'px;'">
                        </div>
                        <p :style="'font-size:'+(modCon.content.style.fontSize / 2)+'px;color:'+modCon.content.style.color+';'">{{item.text ? item.text : '按钮' +( (modCon.content.column * modCon.content.layout ) * index + (ind + 1)) }}</p>
                    </li>
                </ul>
            </el-carousel-item>
        </el-carousel>
        <div class="pagination" v-if="modCon.content.slide == 1 && (arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout)).length > 1">
            <span v-for="item in arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout).length" :class="{'bulletActive':modCon.content.activeInd == (item - 1)}" :style="modCon.content.activeInd == (item - 1) ? ('background:'+modCon.content.style.dotColor+';') : ''"></span>
        </div>
    </div>

     <!-- 平滑 -->
     <div class="smoothCon navBtnsCon" v-else :style="(modCon.content.title.show || modCon.content.more.show ? '' : 'padding-top:10px;') + 'border-radius:' + modCon.content.style.cardRadius/2 + 'px; background:'+ ( modCon.content.cardStyle ? modCon.content.style.cardBg : '') +'; ' + (modCon.content.cardStyle ? 'margin:0 '+(modCon.content.style.marginLeft / 2)+'px;' : '')">
        <div class="con_header" v-if="modCon.content.title.show || modCon.content.more.show  " :style="modCon.content.cardStyle ? 'padding-left:0; padding-right:0; margin:0 14px 6px; ' : ''">
            <h4 class="con_title" :style="'color:'+ modCon.content.title.style.color +';'">{{modCon.content.title.show ? (modCon.content.title.text ?  modCon.content.title.text : '按钮组') : ''}}</h4>
            <span class="more" v-show="modCon.content.more.show" :style="'color:'+(modCon.content.more.style.color)+';'">{{modCon.content.more.text}}<s v-if="modCon.content.more.arr">
                <!-- <em :style="'filter: drop-shadow(10px 0 0 '+ modCon.content.more.style.color +')'"></em> -->
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 0 22 40"> <path fill-rule="evenodd"  opacity="0.6" :fill="modCon.content.more.style.color ? modCon.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg>
            </s></span>
        </div>
        <div class="swiper-item" @scroll.capture="btnsScroll" :style="modCon.content.cardStyle ? '  ':' padding:0 '+(modCon.content.style.marginLeft / 2)+'px; '" :data-padding="modCon.content.style.marginLeft">
            <template v-if="modCon.content.layout > 1">

                <div class="flexSwiper" v-for="(group,index) in (arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout))" :key="index">
                    <ul :class="'grid' + modCon.content.column" >
                        <li v-for="(item,ind) in group">
                            <div class="icon" :style="'width:'+(modCon.content.style.btnSize / 2)+'px; height:'+(modCon.content.style.btnSize / 2)+'px;'">
                                <span class="lab" v-if="item.lab.show" :style="'background-color:' + item.lab.style.bgColor + '; color:' + item.lab.style.color + ';'">{{item.lab.text ? item.lab.text : '标签'}}</span>
                                <img crossOrigin='anonymous' :src="item.icon ? (item.icon + '?v=' + randomNum) : '/static/images/admin/siteMemberPage/default_icon1.png'" onerror="this.src='/static/images/admin/siteMemberPage/default_icon1.png'" alt="" :style="'border-radius:'+ (modCon.content.style.btnRadius ? (modCon.content.style.btnRadius / 2) : 0 ) +'px;'">
                            </div>
                            <p :style="'font-size:'+(modCon.content.style.fontSize / 2)+'px;color:'+modCon.content.style.color+';'">{{item.text ? item.text : '按钮' +( (modCon.content.column * modCon.content.layout ) * index + (ind + 1)) }}</p>
                        </li>
                    </ul>
                </div>
            </template>
            <template v-else>
                <ul :class="'flexItem grid' + modCon.content.column" >
                    <li v-for="(item,ind) in modCon.content.btnsArr">
                        <div class="icon" :style="'width:'+(modCon.content.style.btnSize / 2)+'px; height:'+(modCon.content.style.btnSize / 2)+'px;'">
                            <span class="lab" v-if="item.lab.show" :style="'background-color:' + item.lab.style.bgColor + '; color:' + item.lab.style.color + ';'">{{item.lab.text ? item.lab.text : '标签'}}</span>
                            <img crossOrigin='anonymous' :src="item.icon ? (item.icon + '?v=' + randomNum) : '/static/images/admin/siteMemberPage/default_icon1.png'" onerror="this.src='/static/images/admin/siteMemberPage/default_icon1.png'" alt="" :style="'border-radius:'+ (modCon.content.style.btnRadius ? (modCon.content.style.btnRadius / 2) : 0 ) +'px;'">
                        </div>
                        <p :style="'font-size:'+(modCon.content.style.fontSize / 2)+'px;color:'+modCon.content.style.color+';'">{{item.text ? item.text : '按钮' + (ind + 1)}}</p>
                    </li>
                    <li class="lastPad" :style="'width: '+(modCon.content.style.marginLeft / 2)+'px;'"></li>
                </ul>
            </template>
        </div>
        <div class="progressSlide" @click="onClick"  @mousedown="onMouseDown" @mousermove.stop="onMouseMover" @mouseup="onMouseUp" v-if="(arrToGroup(modCon.content.btnsArr,modCon.content.column * modCon.content.layout)).length > 1">
            <div class="currPageIn" :style="'background:'+modCon.content.style.dotColor+';'"></div>
        </div>
    </div>
</div>