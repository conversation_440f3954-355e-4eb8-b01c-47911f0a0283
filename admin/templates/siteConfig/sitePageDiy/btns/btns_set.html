<!-- 普通按钮组 s -->
<div :class="['rightCon',{'show':currEditPart == 27}] " v-cloak>
    <div class="modBox sNavContainer">
        <div class="title" v-if="currEditPart == 27">普通按钮组

            <el-popover popper-class="alertPop resetPop" :value="currEditPart === 27 && showResetPop" @show="showResetPop = true" v-if="checkHasIn('btns',27) && (!checkModelHasIn() || !checkCompInModel())">
                <div class="text_con">
                    <h4>确认重置该组件为默认样式？</h4>
                </div>
                <div class="alertBtn">
                    <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                    <a href="javascript:;" class="sure" @click="resetItem('btns')"><s></s><span>确定</span></a>
                </div>
                <span class="reset_btn" slot="reference" ><s></s>重置此项</span>
            </el-popover>
            <div class="reset_btn" v-else>
                <span class="btn"><s></s>重置此项</span>
                <div class="smPop">
                    <ul class="subtit_options">
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 27 && showResetPop" v-if="checkHasIn('btns',27)" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认恢复上次保存样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('btns',1)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">恢复上次保存</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 27 && showResetPop" v-if="checkModelHasIn() && checkCompInModel()" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复成模板样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="reChangeToModel('btns',27)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置模板样式</li> 
                        </el-popover>
                        <el-popover popper-class="alertPop resetPop maskPop" :value="currEditPart === 27 && showResetPop" @show="showResetPop = true" :visible-arrow="false" placement="top-end">
                            <div class="text_con">
                                <h4>确认将该组件恢复默认样式？</h4>
                            </div>
                            <div class="alertBtn">
                                <a href="javascript:;" class="cancel" @click="showResetPop = false"><s></s><span>取消</span></a>
                                <a href="javascript:;" class="sure" @click="resetItem('btns',0)"><s></s><span>确定</span></a>
                            </div>
                            <li class="text" slot="reference">重置默认样式</li> 
                        </el-popover>
                    </ul>
                </div>
            </div>
        </div>
        <div class="modInfoStyle">
            <div class="modstyleBox">
                <h2 class="stitle">组件设置</h2>
                <div class="sContent" >
                    <el-checkbox v-model="btnsFormData.title.show"@change="value => showMoreBtn(value,'btnsFormData.more.show','title')">显示标题</el-checkbox>
                    <el-checkbox v-model="btnsFormData.more.show" @change="value => showMoreBtn(value,'btnsFormData.title.show','more')">显示查看更多</el-checkbox>
                    <el-checkbox v-model="btnsFormData.cardStyle">卡片样式</el-checkbox>
                    <el-checkbox v-model="btnsFormData.btnRadius" @change="val => btnsFormData.style.btnRadius = (val ? 50 : 0)">按钮圆角</el-checkbox>
                </div>
                <div class="columbox">
                    <div class="column_chose">
                        <s :class="{'layout2': btnsFormData.layout === 1}"></s>
                        <span @click="btnsFormData.layout = 2">2行</span>
                        <span @click="btnsFormData.layout = 1">1行</span>
                    </div>
                    <div class="column_chose">
                        <s :class="{'layout2': btnsFormData.column === 4}"></s>
                        <span @click="btnsFormData.column = 5">每行5个</span>
                        <span @click="btnsFormData.column = 4">每行4个</span>
                    </div>
                </div>
                <div class="inpbox" v-show="btnsFormData.title.show">
                    <el-input class="bgColorInp" v-model="btnsFormData.title.text" placeholder="请输入标题"></el-input>
                </div>
            </div>
            <div class="modstyleBox contentSetBox">
                <h2 class="stitle">内容设置</h2>
                <div class="sContent">
                    <div  class="btnsBox btn_item btnsSort sortBox"  data-sort="btnsFormData.btnsArr"  data-drag=".item" data-filter=".inpbox">
                        <div class="option_item item" v-for="(btn,ind) in btnsFormData.btnsArr" :key="'btns_' + btn.id" :data-id="ind"> 
                            <s class="del_item" @click="delTopBtns('btnsFormData',ind)"></s>
                            <s class="left_icon"><img src="/static/images/admin/order_icon.png" alt=""></s>
                            <div class="item_con flex_r">
                                <div class="img_up fn-clear">
                                    <div :class="['upbtn',{'hasup':btn.icon}]">
                                        <input type="file" name="Filedata" accept=".png,.jpg,.jpeg" class="fileUp" :id="'btnsIcon_'+ind+'_filedata'" :data-type="'btnsIcon_'+ind" >
                                        <img v-if="btn.icon" :src="btn.icon"  alt="">
                                        <span>更换图片</span>
                                    </div>
                                    <div class="imgText">
                                        <div class="inpbox" >
                                            <el-input   show-word-limit maxlength="6" v-model="btn.text" placeholder="请输入文字内容" class="iconName" ></el-input>
                                        </div>
                                        <div :class="['inpbox linkbox linkChose']" @click="changeLink('btnsFormData.btnsArr.' + ind + '.link')">
                                            <s>
                                                <img v-if="btn.linkInfo.type == 4"  src="/static/images/admin/siteMemberPage/tel.png">
                                                <img v-else src="/static/images/admin/link.png">
                                            </s>
                                            <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="btn.linkInfo.linkText ? btn.linkInfo.linkText  : btn.link ">
                                        </div>
                                        
                                        
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <a href="javascript:;" class="add_more" @click="addNewTopBtns('btnsFormData')"  ><s></s>添加按钮</a>
                    </div>
                </div>
            </div>

            <div class="modstyleBox">
                <h2 class="stitle">标签设置</h2>
                <div class="sContent ">
                    <div>
                        <el-checkbox v-for="(item,ind) in btnsFormData.btnsArr" v-model="item.lab.show" :key="item.id" :label="item">{{item.text ? item.text : ('按钮' + (ind + 1)) }}</el-checkbox>
                    </div>

                    <div class="style_item module_item" v-for="(item,ind) in (btnsFormData.btnsArr)" v-show="item.lab.show">
                        <div class="style_options">
                            <label for="">{{item.text ? item.text : ('按钮' + (ind + 1))}}：</label>
                            <el-input placeholder="请输入标签" maxlength="8" v-model="item.lab.text"></el-input>
                        </div>
                        <div class="style_options">
                            <label for="">标签颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.btnsArr.'+ ind +'.lab.style.bgColor')"  v-model="item.lab.style.bgColor"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.btnsArr.'+ ind +'.lab.style.bgColor')"  :value="item.lab.style.bgColor.replace('#','')" spellcheck="false" maxlength="6">
                                <button>重置</button>
                            </div>
                        </div>
                        <div class="style_options">
                            <label for="">文本颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.btnsArr.'+ ind +'.lab.style.color')"  v-model="item.lab.style.color"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.btnsArr.'+ ind +'.lab.style.color')"  :value="item.lab.style.color.replace('#','')" spellcheck="false" maxlength="6">
                                <button>重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modstyleBox contentSetBox" v-show="btnsFormData.more.show">
                <h2 class="stitle">查看更多</h2>
                <div class="sContent">
                    <div class="item style_item ">
                        <div class="style_options" >
                            <label for="">显示文本：</label>
                            <div class="textSetBox">
                                <el-input placeholder="请输入文本" maxlength="8" show-word-limit v-model="btnsFormData.more.text"></el-input>
                            </div>
                            <span :class="['showArr',{'noColor':!btnsFormData.more.arr}] " @click="btnsFormData.more.arr = !btnsFormData.more.arr"></span>
                        </div>
                        <div class="style_options" @click="changeLink('btnsFormData.more.link')">
                            <label for="">链接：</label>
                            <div class="inpbox linkbox">
                                <s><img src="/static/images/admin/link.png" alt=""></s>
                                <input type="text" placeholder="请选择链接" class="iconLink" readonly :value="btnsFormData.more.linkInfo.linkText ? btnsFormData.more.linkInfo.linkText : btnsFormData.more.link ">
                            </div>
                        </div>

                        <div class="style_options" >
                            <label for="">文本颜色：</label>
                            <div class="colorpickerBox">
                                <el-color-picker  class="colorpicker" v-model="btnsFormData.more.style.color" @active-change="(value) => activeChangeColor(value,'btnsFormData.more.style.color')"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" :value="(btnsFormData.more.style.color).replace('#','')" @change="changeColor('btnsFormData.more.style.color')" maxlength="6">
                                <button @click="resetColor('btnsFormData.more.style.color')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modstyleBox">
                <h2 class="stitle">组件样式</h2>
                <div class="sContent ">
                    <div class="style_item">
                        <div class="style_options">
                            <label for="">交互效果：</label>
                            <div class="split_options radioBox">
                                <span :class="{'onChose':!btnsFormData.slide}" @click="btnsFormData.slide = 0"><s></s>平滑</span>
                                <span :class="{'onChose':btnsFormData.slide}" @click="btnsFormData.slide = 1"><s></s>分页</span>
                            </div>
                        </div>
                        <div class="style_options">
                            <label for="">文本大小：</label>
                            <div class="split_options radioBox">
                                <span :class="{'onChose':btnsFormData.style.fontSize == 24}" @click="btnsFormData.style.fontSize = 24"><s></s>常规</span>
                                <span :class="{'onChose':btnsFormData.style.fontSize != 24}" @click="btnsFormData.style.fontSize = 26"><s></s>加大</span>
                            </div>
                        </div>
                        <div class="style_options">
                            <label for="">按钮文本：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.style.color')"  v-model="btnsFormData.style.color"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.style.color')"  :value="btnsFormData.style.color.replace('#','')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('btnsFormData.style.color')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" v-if="btnsFormData.title.show">
                            <label for="">标题：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.title.style.color')"  v-model="btnsFormData.title.style.color"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.title.style.color')"  :value="btnsFormData.style.color.replace('#','')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('btnsFormData.title.style.color')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" v-if="btnsFormData.cardStyle">
                            <label for="">卡片：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.style.cardBg')"  v-model="btnsFormData.style.cardBg"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.style.cardBg')"  :value="btnsFormData.style.cardBg.replace('#','')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('btnsFormData.style.cardBg')">重置</button>
                            </div>
                        </div>
                        <div class="style_options" >
                            <label for="">分页指示：</label>
                            <div class="colorpickerBox">
                                <el-color-picker class="colorpicker" @active-change="(value) => activeChangeColor(value,'btnsFormData.style.dotColor')"  v-model="btnsFormData.style.dotColor"></el-color-picker>
                            </div>
                            <div class="colorShowBox">
                                <span class="single">#</span>
                                <input type="text" class="colorShow" @change="changeColor('btnsFormData.style.dotColor')"  :value="btnsFormData.style.dotColor.replace('#','')" spellcheck="false" maxlength="6">
                                <button @click="resetColor('btnsFormData.style.dotColor')">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 间距设置 -->
            <div class="marginSetBox modstyleBox" >
                <h2 class="stitle" >间距设置</h2>
                <el-collapse-transition>
                    <div class="style_item" >
                        <div class="sContent">
                            <div class="style_options" v-if="btnsFormData.cardStyle">
                                <label for="">卡片圆角：</label>
                                <el-slider tooltip-class="tooltip_show" :show-tooltip="showTooltip"   :min="0" :max="50" v-model="btnsFormData.style.cardRadius"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow" @input="checkMax(0,50,'btnsFormData.style.cardRadius')" :value="btnsFormData.style.cardRadius">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">按钮圆角：</label>
                                <el-slider tooltip-class="tooltip_show"  @input="val => checkRadius(val,btnsFormData)" v-model="btnsFormData.style.btnRadius" :show-tooltip="showTooltip"  :min="0" :max="55"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"   @input="checkMax(0,55,'btnsFormData.style.btnRadius')" :value="btnsFormData.style.btnRadius">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">按钮大小：</label>
                                <el-slider tooltip-class="tooltip_show"  v-model="btnsFormData.style.btnSize" :show-tooltip="showTooltip"  :min="64" :max="110"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"   @change="checkMax(64,110,'btnsFormData.style.btnSize')" :value="btnsFormData.style.btnSize">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            
                            <div class="style_options">
                                <label for="">上间距：</label>
                                <el-slider tooltip-class="tooltip_show" v-model="btnsFormData.style.marginTop" :show-tooltip="showTooltip"  :min="0" :max="40"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  @input="checkMax(0,40,'btnsFormData.style.marginTop')" :value="btnsFormData.style.marginTop">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                            <div class="style_options">
                                <label for="">左右间距：</label>
                                <el-slider tooltip-class="tooltip_show" v-model="btnsFormData.style.marginLeft" :show-tooltip="showTooltip"  :min="0" :max="40"></el-slider>
                                <div class="colorShowBox" style="background-color: #E8EAEC;">
                                    <input type="number" class="colorShow"  @input="checkMax(0,40,'btnsFormData.style.marginLeft')" :value="btnsFormData.style.marginLeft">
                                    <span class="single back">px</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-collapse-transition>
            </div>
        </div>
    </div>
</div>
<!-- 普通按钮组 e -->