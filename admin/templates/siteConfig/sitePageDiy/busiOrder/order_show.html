
<div class="orderConBox">
    <div class="orderBox" :style="`margin:${modCon.content.style.marginTop / 2}px ${modCon.content.style.marginLeft / 2}px 0; border-radius:${modCon.content.style.borderRadiusTop / 2}px ${modCon.content.style.borderRadiusTop / 2}px ${modCon.content.style.borderRadiusBottom / 2}px ${modCon.content.style.borderRadiusBottom / 2}px;  ${!modCon.content.style.marginLeft ? 'padding-left:'+ modCon.content.style.paddingLeft / 2 +'px; padding-right:'+ modCon.content.style.paddingLeft / 2 +'px;' : '' }`">
        <div class="con_header titlebox flexbox" v-if="modCon.content.orderInfo && modCon.content.orderInfo.code" :style="`padding-left:${modCon.content.style.marginLeft == 0  ? modCon.content.style.paddingLeft/2 : '16'}px;padding-right:${modCon.content.style.marginLeft == 0  ? modCon.content.style.paddingLeft/2 : '16'}px;`">
            <div class="h4" v-if="modCon.content.title.show" :style="`color:${modCon.content.title.style.color};`">
                {{modCon.content.title.text || '标题'}}
            </div>
            <div class="more flexbox" v-if="modCon.content.more.show && modCon.content.title.show" >
                <div class="txt" :style="`color:${modCon.content.more.style.color};`">{{modCon.content.more.text}}</div>
                <div class="s" v-if="modCon.content.more.arr"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11px" height="20px" viewBox="0 0 22 40"> <path fill-rule="evenodd"  opacity="0.6" :fill="modCon.content.more.style.color ? modCon.content.more.style.color : 'rgb(161, 164, 179)'" d="M10.977,9.976 C10.977,9.618 10.831,9.260 10.544,8.986 L3.484,0.385 C2.910,-0.161 1.972,-0.161 1.401,0.385 C0.822,0.935 0.822,1.820 1.401,2.368 L7.415,9.976 L1.401,17.583 C0.822,18.130 0.822,19.020 1.401,19.567 C1.972,20.113 2.910,20.113 3.484,19.567 L10.544,10.968 C10.831,10.694 10.977,10.336 10.977,9.976 Z"/> </svg></div>
            </div>
        </div>
        <div class="place_order" v-else>请选择订单数据来源</div>
        <div class="con_box" v-if="modCon.content.orderInfo&&modCon.content.orderInfo.showData && modCon.content.orderInfo.showData.length" style="padding-left: 8px; padding-right: 8px;">
            <div class="ulbox felx-box" :style="`grid-template-columns: repeat(${modCon.content.orderInfo.showData.length == 5 ? 5 : 4},1fr);`">
                <div class="li" v-for="item in modCon.content.orderInfo.showData">
                    <div class="icon" v-if="modCon.content.styletype != 2" :style="`width:${modCon.content.style.iconSize / 2}px; height:${modCon.content.style.iconSize / 2}px;`">
                        <img  :src="item.icon || btnDefault" alt="">
                        <div class="lab" v-if="item.numShow && modCon.content.styletype == 3" :style="`background-color:${modCon.content.style.labColor};`">{{item.numShow}}</div>
                    </div>
                    <div class="numShow"  v-else :style="`color:${item.style.color};`">{{item.numShow}}</div>
                    <div class="ptext" :style="`color:${modCon.content.style.textColor};`">{{item.text}}<span v-if="item.numShow && modCon.content.styletype == 1">({{item.numShow}})</span></div>
                </div>
            </div>
        </div>
    </div>
</div>