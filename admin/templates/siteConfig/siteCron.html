<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>计划任务管理</title>
{#$cssFile#}
<style>
    .setting, .setting label {display: inline-block;}
    .setting {margin-left: 50px;}
    .setting label {margin: 0 10px 0 0;}
    .setting label input[type=radio] {margin-right: 0;}
	.list td a {font-size: 14px;}
</style>
</head>

<body>
<div class="alert alert-success" style="margin:10px;"><button type="button" class="close" data-dismiss="alert">×</button>配置教程：<a href="https://help.kumanyun.com/help-70-753.html" target="_blank">https://help.kumanyun.com/help-70-753.html</a></div>
<div class="filter clearfix" style="padding-top: 10px;">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn btn-success hide" id="openBtn" data-type="开启">开启</button>
    <button class="btn btn-danger hide" id="closeBtn" data-type="停用">停用</button>
    <button class="btn btn-inverse hide" id="delBtn">删除</button>
    <div class="btn-group" id="moduleBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">频道<span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="">全部</a></li>
        {#foreach from=$moduleArr item=module#}
        <li><a href="javascript:;" data-id="{#$module.name#}">{#$module.title#}</a></li>
        {#/foreach#}
      </ul>
    </div>
    <div class="btn-group operBtn">
        <button class="btn dropdown-toggle" data-toggle="dropdown">更多操作<span class="caret"></span></button>
        <ul class="dropdown-menu">
          <li><a href="javascript:;" id="deleteRepeat" title="此操作可以将重复的计划任务自动合并为一条">一键删除重复的任务</a></li>
          <li><a href="javascript:;" id="deleteInvalid" title="此操作可以将不存在或已卸载的模块计划任务自动删除">一键删除无效的任务</a></li>
          <li><a href="javascript:;" id="deleteStop" title="此操作可以将已经停用的计划任务批量删除">一键删除停用的任务</a></li>
        </ul>
    </div>
    <button class="btn btn-primary" id="addNew">新增计划任务</button>
    <div class="setting">
        <label class="statusTips" data-toggle="tooltip" data-placement="bottom" title="" data-original-title="程序内置方式会影响系统性能，推荐使用服务器端Shell方式，体验更好！"><i class="icon-question-sign" style="margin-top: 4px;"></i>
        执行方式：</label><label><input type="radio" name="state" value="1"{#if $cfg_cronType#} checked{#/if#} /> 程序内置</label>
        <label><input type="radio" name="state" value="0"{#if !$cfg_cronType#} checked{#/if#} /> 服务器端Shell (<font color="#1dc11d">推荐</font>)</label><a href="https://help.kumanyun.com/help-70-753.html" target="_blank">配置教程</a>&nbsp;&nbsp;&nbsp;&nbsp;
        <button class="btn btn-small btn-success" id="save">保存</button>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row12 left">频道</li>
  <li class="row20 left">名称</li>
  <li class="row15 left">任务周期</li>
  <li class="row15 left">上次执行时间</li>
  <li class="row15 left">下次执行时间</li>
  <li class="row10">状态</li>
  <li class="row10">操作</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1">
  <table>
    <tbody>
      {#foreach from=$list item=l#}
      <tr data-id="{#$l.id#}" data-type="{#$l.moduleName#}">
        <td class="row3"><span class="check"></span></td>
        <td class="row12 left">{#$l.moduleTitle#}</td>
        <td class="row20 left">{#$l.title#}<br /><small>{#$l.file#}.php</small></td>
        <td class="row15 left">{#$l.cycle#}</td>
        <td class="row15 left">{#$l.ltime|date_format:"%Y-%m-%d %H:%M:%S"#}</td>
        <td class="row15 left">{#$l.ntime|date_format:"%Y-%m-%d %H:%M:%S"#}</td>
        <td class="row10 state">{#$l.state#}<span class="more"><s></s></span></td>
        <td class="row10"><a href="siteCron.php?action=edit&id={#$l.id#}" title="编辑{#$l.title#}计划任务" class="edit">编辑</a><a href="javascript:;" class="run" title="手动执行">执行</a><a href="javascript:;" title="删除" class="del">删除</a></td>
      </tr>
      {#/foreach#}
    </tbody>
  </table>
</div>

<script>var adminPath = "{#$adminPath#}", token = '{#$token#}';</script>
{#$jsFile#}
<script type="text/javascript">
    $(function(){
        $('.statusTips').tooltip();
    })
</script>
</body>
</html>
