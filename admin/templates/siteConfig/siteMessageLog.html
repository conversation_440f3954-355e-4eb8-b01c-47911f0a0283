<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>信息发送日志</title>
{#$cssFile#}
</head>

<body>
<div class="search">
  <label>搜索：<input class="input-xlarge" type="search" id="keyword" placeholder="请输入要搜索的关键字"></label>
  <div class="btn-group" id="stateBtn" data-id="">
    <button class="btn dropdown-toggle" data-toggle="dropdown">状态<span class="caret"></span></button>
    <ul class="dropdown-menu">
      <li><a href="javascript:;" data-id="">全部</a></li>
      <li><a href="javascript:;" data-id="0">成功</a></li>
      <li><a href="javascript:;" data-id="1">失败</a></li>
    </ul>
  </div>
  <button type="button" class="btn btn-success" id="searchBtn">立即搜索</button>
</div>

<div class="filter clearfix">
  <div class="f-left">
    <div class="btn-group" id="selectBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="check"></span><span class="caret"></span></button>
      <ul class="dropdown-menu">
        <li><a href="javascript:;" data-id="1">全选</a></li>
        <li><a href="javascript:;" data-id="0">不选</a></li>
      </ul>
    </div>
    <button class="btn" id="delBtn">删除</button>
    <button class="btn" id="delAll">清空日志</button>
  </div>
  <div class="f-right">
    <div class="btn-group" id="pageBtn" data-id="20">
      <button class="btn dropdown-toggle" data-toggle="dropdown">每页20条<span class="caret"></span></button>
      <ul class="dropdown-menu pull-right">
        <li><a href="javascript:;" data-id="10">每页10条</a></li>
        <li><a href="javascript:;" data-id="15">每页15条</a></li>
        <li><a href="javascript:;" data-id="20">每页20条</a></li>
        <li><a href="javascript:;" data-id="30">每页30条</a></li>
        <li><a href="javascript:;" data-id="50">每页50条</a></li>
        <li><a href="javascript:;" data-id="100">每页100条</a></li>
      </ul>
    </div>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="prevBtn">上一页</button>
    <button class="btn dropdown-toggle disabled" data-toggle="dropdown" id="nextBtn">下一页</button>
    <div class="btn-group" id="paginationBtn">
      <button class="btn dropdown-toggle" data-toggle="dropdown">1/1页<span class="caret"></span></button>
      <ul class="dropdown-menu" style="left:auto; right:0;">
        <li><a href="javascript:;" data-id="1">第1页</a></li>
      </ul>
    </div>
  </div>
</div>

<ul class="thead t100 clearfix">
  <li class="row3">&nbsp;</li>
  <li class="row15 left">收件人</li>
  <li class="row35 left">主题</li>
  <li class="row10">发送人</li>
  <li class="row15">发送时间</li>
  <li class="row12">状态</li>
  <li class="row10">操作</li>
</ul>

<div class="list common mt124" id="list" data-totalpage="1" data-atpage="1"><table><tbody></tbody></table><div id="loading" class="loading hide"></div></div>

<div id="pageInfo" class="pagination pagination-centered"></div>

<div class="hide">
  <span id="sKeyword"></span>
  <span id="sState"></span>
</div>

<script id="quickEdit" type="text/html">
  <form action="" class="quick-editForm" name="editForm">
    <dl class="clearfix">
      <dt>收件人 ：</dt>
      <dd id="user"></dd>
    </dl>
    <dl class="clearfix">
      <dt>发送时间：</dt>
      <dd id="time"></dd>
    </dl>
    <dl class="clearfix">
      <dt>发送IP：</dt>
      <dd id="ip"></dd>
    </dl>
    <dl class="clearfix">
      <dt>状态：</dt>
      <dd id="state"></dd>
    </dl>
	<dl class="clearfix">
      <dt>主题：</dt>
      <dd id="title"></dd>
    </dl>
	<dl class="clearfix">
      <dt>内容：</dt>
      <dd id="content" style="max-height:250px; overflow-y: auto;"></dd>
    </dl>
  </form>
</script>

<script>var action = '{#$action#}', adminPath = "{#$adminPath#}";</script>
{#$jsFile#}
</body>
</html>