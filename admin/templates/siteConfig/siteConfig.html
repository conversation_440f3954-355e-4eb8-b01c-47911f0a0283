<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>系统基本参数</title>
{#$cssFile#}
<style>
    div.tagsinput {height: auto!important;}
</style>
<script>
var thumbSize = {#$thumbSize#}, thumbType = "{#$thumbType#}", adminPath = "{#$adminPath#}", modelType = action = "siteConfig";
</script>
<style>
body {height: auto;}
.weixinQr img{width: 94px; height: 94px; margin-left: -4px;}
.editform dt{width: 180px;}
.editform dt label.sl{margin-top: -10px;}
.editform dt small{display: block; margin: -8px 12px 0 0;}
.editform dt small i, .editform dd small i{font-style: normal;}
.tagsobj{margin:0;}
.editform dd small {margin-left: 10px;}
.server_wxQr img {width:150px;height:150px;}
.comment .btn{margin:0 15px 10px 0;}
#listSection3 img, #listSection4 img {background-color: #d5d5d5;}
</style>
</head>

<body>
<div class="btn-group config-nav" data-toggle="buttons-radio">
  <button type="button" class="btn active" data-type="site">站点信息</button>
  <button type="button" class="btn" data-type="temp">风格管理</button>
  <button type="button" class="btn" data-type="upload">上传设置</button>
  <button type="button" class="btn" data-type="ftp">远程附件</button>
  <button type="button" class="btn" data-type="mark">水印设置</button>
  <button type="button" class="btn" data-type="unit">计量单位</button>
  <button type="button" class="btn" data-type="advMark">广告设置</button>
  <button type="button" class="btn" data-type="ucenterlinks">会员中心链接管理</button>
  <button type="button" class="btn" data-type="nlp">自然语言处理</button>
  <button type="button" class="btn" data-type="juhe">聚合数据接口</button>
  <button type="button" class="btn" data-type="im">即时通讯接口</button>
  <button type="button" class="btn" data-type="kefu">客服系统</button>
</div>

<div class="info-tips hide" id="infoTip"></div>

<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="configType" value="site" />
  <input type="hidden" name="token" id="token" value="{#$token#}" />
  <div class="item">
    <dl class="clearfix">
      <dt><label for="basehost" class="sl">网站域名：</label><small><i>{</i><i>#$</i>cfg_basehost<i>#}</i></small></dt>
      <dd>
        <div class="input-prepend input-append">
          <span class="add-on">{#$cfg_secureAccess#}</span>
          <input class="input-large" type="text" name="basehost" id="basehost" value="{#$basehost#}" data-regex=".*" />
          <span class="input-tips"><s></s>请输入网站域名</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="webname" class="sl">网站名称：</label><small><i>{</i><i>#$</i>cfg_webname<i>#}</i></small></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="webname" id="webname" value="{#$webname#}" data-regex=".*" />
        <span class="input-tips"><s></s>请输入网站名称</span>
        <div style="font-size: 12px; padding-top: 10px;">
          使用<code>$city</code>可自动替换为当前所在城市名称<br />
          如输入：<code>$city生活网</code>，前台会自动变成：<code>北京生活网</code>、<code>苏州生活网</code>等<br />
          此方法适用于以下：简称、关键字、描述、版权信息，以及系统各个模块的seo标题、关键字、描述等；
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="shortname" class="sl">简称：</label><small><i>{</i><i>#$</i>cfg_shortname<i>#}</i></small></dt>
      <dd>
        <input class="input-large" type="text" name="shortname" id="shortname" value="{#$shortname#}" data-regex=".*" maxlength="10" />
        <span class="input-tips" style="display: inline-block;"><s></s>建议6个字以内。</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label class="sl">网站LOGO：</label><small><i>{</i><i>#$</i>cfg_weblogo<i>#}</i></small></dt>
      <dd class="thumb fn-clear listImgBox fn-hide">
    		<div class="uploadinp filePicker thumbtn{#if $weblogo != ""#} hide{#/if#}" id="filePicker1" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
    		{#if $weblogo != ""#}
    		<ul id="listSection1" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_1"><a href='{#$cfg_attachment#}{#$weblogo#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$weblogo#}" data-val="{#$weblogo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
    		{#else#}
    		<ul id="listSection1" class="listSection thumblist fn-clear"></ul>
    		{#/if#}
    		<input type="hidden" name="litpic" value="{#$weblogo#}" class="imglist-hidden" id="litpic">
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label class="sl">后台LOGO：</label><small>尺寸：270*76</small></dt>
      <dd class="thumb fn-clear listImgBox fn-hide">
    		<div class="uploadinp filePicker thumbtn{#if $adminlogo != ""#} hide{#/if#}" id="filePicker3" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
    		{#if $adminlogo != ""#}
    		<ul id="listSection3" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_3"><a href='{#$cfg_attachment#}{#$adminlogo#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$adminlogo#}" data-val="{#$adminlogo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
    		{#else#}
    		<ul id="listSection3" class="listSection thumblist fn-clear"></ul>
    		{#/if#}
    		<input type="hidden" name="adminlogo" value="{#$adminlogo#}" class="imglist-hidden" id="adminlogo">
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label class="sl">商家中心LOGO：</label><small>尺寸：295*72</small></dt>
      <dd class="thumb fn-clear listImgBox fn-hide">
    		<div class="uploadinp filePicker thumbtn{#if $businesslogo != ""#} hide{#/if#}" id="filePicker4" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
    		{#if $businesslogo != ""#}
    		<ul id="listSection4" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_4"><a href='{#$cfg_attachment#}{#$businesslogo#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$businesslogo#}" data-val="{#$businesslogo#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
    		{#else#}
    		<ul id="listSection4" class="listSection thumblist fn-clear"></ul>
    		{#/if#}
    		<input type="hidden" name="businesslogo" value="{#$businesslogo#}" class="imglist-hidden" id="businesslogo">
    	</dd>
    </dl>
    <dl class="clearfix">
      <dt><label>后台页面水印：</label></dt>
      <dd class="radio">
        {#html_radios name="adminWaterMark" values=$adminWaterMark checked=$adminWaterMarkChecked output=$adminWaterMarkNames separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; padding-top: 10px;">
            启用后页面将增加当前登录人的真实姓名和当前时间的水印，用于信息数据泄漏时追踪来源，修改此配置需要退出重新登录后台生效！
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>后台背景色：</label></dt>
      <dd class="radio">
          <div class="color_pick adminBackgroundColor" style="margin:0; border:1px solid #ccc;"><em style="background:{#if $adminBackgroundColor#}{#$adminBackgroundColor#}{#else#}#3275FA{#/if#};"></em></div>
          <input type="hidden" name="adminBackgroundColor" id="adminBackgroundColor" value="{#if $adminBackgroundColor#}{#$adminBackgroundColor#}{#else#}#3275FA{#/if#}" />
          <div style="font-size: 12px; padding-top: 10px;">
            默认颜色：#3275FA，修改此配置需要退出重新登录后台生效！
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
        <dt><label class="sl">分享默认图片：</label><small>尺寸：200*200</small></dt>
        <dd class="thumb fn-clear listImgBox fn-hide">
            <div class="uploadinp filePicker thumbtn{#if $sharePic != ""#} hide{#/if#}" id="filePicker2" data-type="logo"  data-count="1" data-size="{#$thumbSize#}" data-imglist=""><div></div><span></span></div>
            {#if $sharePic != ""#}
            <ul id="listSection2" class="listSection thumblist fn-clear" style="display:inline-block;"><li id="WU_FILE_0_2"><a href='{#$cfg_attachment#}{#$sharePic#}' target="_blank" title=""><img alt="" src="{#$cfg_attachment#}{#$sharePic#}" data-val="{#$sharePic#}"/></a><a class="reupload li-rm" href="javascript:;">删除图片</a></li></ul>
            {#else#}
            <ul id="listSection2" class="listSection thumblist fn-clear"></ul>
            {#/if#}
            <input type="hidden" name="sharePic" value="{#$sharePic#}" class="imglist-hidden" id="sharePic">
        </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="shareTitle" class="sl">海报推广标题：</label><small><i>{</i><i>#$</i>cfg_shareTitle<i>#}</i></small></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="shareTitle" id="shareTitle" placeholder="一般不超过10个字" value="{#$shareTitle#}" />
        <span class="input-tips"><s></s>一般不超过10个字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="shareDesc" class="sl">海报推广描述：</label><small><i>{</i><i>#$</i>cfg_shareDesc<i>#}</i></small></dt>
      <dd>
        <textarea class="input-xxlarge" name="shareDesc" id="shareDesc" placeholder="一般不超过50个字">{#$shareDesc#}</textarea>
        <span class="input-tips"><s></s>一般不超过50个字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label class="sl" style="margin-top: 15px;">favicon图标自定义：</label><small><a href="https://tool.lu/favicon/" target="_blank">在线制作</a></small></dt>
      <dd class="favicon">
        <input name="favicon" type="hidden" id="favicon" value="" />
        <div class="spic">
          <div class="sholder"><img src="{#$cfg_basehost#}/favicon.ico?v={#math equation=rand(1,100000)#}" style="width: 48px; height: 48px;" /></div>
          <span class="input-tips" style="display: inline-block;">尺寸：48*48</span>
        </div>
        <iframe src ="/include/upfile.inc.php?mod=siteConfig&type=favicon&obj=favicon" style="width:100%; height:25px;" scrolling="no" frameborder="0" marginwidth="0" marginheight="0" id="faviconIframe"></iframe>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="keywords" class="sl">关键字：</label><small><i>{</i><i>#$</i>cfg_keywords<i>#}</i></small></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="keywords" id="keywords" maxlength="100" data-regex=".{0,100}" placeholder="一般不超过100个字符" value="{#$keywords#}" />
        <span class="input-tips"><s></s>一般不超过100个字符</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="description" class="sl">描述：</label><small><i>{</i><i>#$</i>cfg_description<i>#}</i></small></dt>
      <dd>
        <textarea name="description" id="description" placeholder="一般不超过200个字符" data-regex=".{0,200}">{#$description#}</textarea>
        <span class="input-tips"><s></s>一般不超过200个字符</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="beian" class="sl">备案信息：</label><small><i>{</i><i>#$</i>cfg_beian<i>#}</i></small></dt>
      <dd>
        <input class="input-large" type="text" name="beian" id="beian" value="{#$beian#}" data-regex=".*" />
        <span class="input-tips"><s></s>请输入网站备案信息</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="hotline" class="sl">咨询热线：</label><small><i>{</i><i>#$</i>cfg_hotline<i>#}</i></small></dt>
      <dd>
        <input class="input-large" type="text" name="hotline" id="hotline" value="{#$hotline#}" data-regex=".*" />
        <span class="input-tips"><s></s>请输入网站咨询热线</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="server_tel" class="sl">网站客服：</label><small></small></dt>
      <dd>
        <span class="input-tips" style="display:block;margin:0 0 10px;"><s></s>客服电话<small><i>{</i><i>#$</i>cfg_server_tel<i>#}</i></small></span>
        <textarea name="server_tel" id="server_tel" placeholder="客服电话" rows="3">{#$server_tel#}</textarea>
        <span class="input-tips" style="display:block;margin:15px 0 10px;"><s></s>客服QQ<small><i>{</i><i>#$</i>cfg_server_qq<i>#}</i></small></span>
        <textarea name="server_qq" id="server_qq" placeholder="客服QQ" rows="3">{#$server_qq#}</textarea>
        <div class="server_wx_box">
          <span class="input-tips" style="display:block;margin:15px 0 10px;"><s></s>客服微信<small><i>{</i><i>#$</i>cfg_server_wx<i>#}</i></small></span>
          {#*<textarea name="server_wx" id="server_wx" placeholder="客服微信" rows="3">{#$server_wx#}</textarea>*#}
          <div style="margin-bottom:10px;"><label for="server_wx" class="sl">微信号：</label><input class="input-large" type="text" name="server_wx" id="server_wx" value="{#$server_wx#}" data-regex=".*" /></div>
          <div style="margin-bottom:10px;"><label for="server_wx_ewm" class="sl">二维码：<small style="color: #999; margin-left: 0;"><i>{</i><i>#$</i>cfg_server_wxQr<i>#}</i></small></label></div>
          <div class="weixinQr server_wx">
            <input name="server_wxQr" type="hidden" id="server_wxQr" value="{#$server_wxQr#}" />
            <div class="spic{#if $server_wxQr == ''#} hide{#/if#}">
              <div class="sholder">{#if $server_wxQr#}<img src="/include/attachment.php?f={#$server_wxQr#}" />{#/if#}</div>
              <a href="javascript:;" class="reupload"{#if $server_wxQr#} style="display: inline-block;"{#/if#}>重新上传</a>
            </div>
            <iframe src ="/include/upfile.inc.php?mod=siteConfig&type=card&obj=server_wxQr" style="width:100%; height:25px;{#if $server_wxQr#} display:none;{#/if#}" scrolling="no" frameborder="0" marginwidth="0" marginheight="0" ></iframe>
          </div>
        </div>
      </dd>
    </dl>
    {#*
    <dl class="clearfix">
      <dt><label class="sl">二维码：</label><small><i>{</i><i>#$</i>cfg_weixinQr<i>#}</i></small></dt>
      <dd class="weixinQr">
        <input name="wechatQr" type="hidden" id="wechatQr" value="{#$wechatQr#}" />
        <div class="spic{#if $wechatQr == ''#} hide{#/if#}">
          <div class="sholder">{#if $wechatQr#}<img src="/include/attachment.php?f={#$wechatQr#}" />{#/if#}</div>
          <a href="javascript:;" class="reupload"{#if $wechatQr#} style="display: inline-block;"{#/if#}>重新上传</a>
        </div>
        <iframe src ="/include/upfile.inc.php?mod=siteConfig&type=card&obj=wechatQr" style="width:100%; height:25px;{#if $wechatQr#} display:none;{#/if#}" scrolling="no" frameborder="0" marginwidth="0" marginheight="0" ></iframe>
      </dd>
    </dl>
    *#}
    <dl class="clearfix">
      <dt><label class="sl">版权信息：</label><small><i>{</i><i>#$</i>cfg_powerby<i>#}</i></small></dt>
      <dd>
        <script id="powerby" name="powerby" type="text/plain" style="width:95.4%;height:200px"></script>
      </dd>
    </dl>
    <div id="powerbyHtml" class="hide">{#$powerby#}</div>
    <dl class="clearfix">
      <dt><label for="statisticscode" class="sl">统计代码：</label><small><i>{</i><i>#$</i>cfg_statisticscode<i>#}</i></small></dt>
      <dd>
        <textarea name="statisticscode" id="statisticscode" style="width: 90%; height: 150px;" placeholder="在第三方网站上注册并获得统计代码">{#$statisticscode#}</textarea>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>网站状态：</label></dt>
      <dd class="radio">
        {#html_radios name="visitState" values=$visitState checked=$visitStateChecked output=$visitStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    {#if $visitStateChecked == 0#}
    <dl class="clearfix hide">
    {#else#}
    <dl class="clearfix">
    {#/if#}
      <dt><label for="visitMessage">关闭原因：</label></dt>
      <dd>
        <textarea name="visitMessage" id="visitMessage" class="input-xxlarge" rows="5" placeholder="站点关闭时出现的提示信息" data-regex=".*">{#$visitMessage#}</textarea>
        <span class="input-tips"><s></s>请输入站点关闭时要显示的提示信息</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>PC端状态：</label></dt>
      <dd class="radio">
        {#html_radios name="pcState" values=$pcState checked=$pcStateChecked output=$pcStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="timeZone">默认时区：</label></dt>
      <dd>
        <select name="timeZone" id="timeZone" class="input-xxlarge">
          {#html_options options=$timeZoneList selected=$timeZone#}
        </select>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="mapCity" class="sl">地图默认城市：</label><small><i>{</i><i>#$</i>cfg_mapCity<i>#}</i></small></dt>
      <dd>
        <input class="input-small" type="text" name="mapCity" id="mapCity" value="{#$mapCity#}" data-regex=".*" />
        <span class="input-tips"><s></s>请输入您网站运营的城市</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="map">地图配置：</label></dt>
      <dd>
        <select name="map" id="map">
          {#html_options options=$mapList selected=$mapSelected#}
        </select>
        <span class="input-tips"><s></s>模块地图配置</span>
      </dd>
    </dl>
    {#$mapkey#}
    <dl class="clearfix hide">
      <dt><label for="weatherCity" class="sl">天气默认城市：</label><small><i>{</i><i>#$</i>cfg_weatherCity<i>#}</i></small></dt>
      <dd>
        <input class="input-medium" type="text" min="0" name="weatherCity" id="weatherCity" value="{#$weatherCity#}" />
        <span class="input-tips" style="display:inline-block;"><s></s>留空表示定位当前城市</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="onlinetime">在线用户时限：</label></dt>
      <dd>
        <input class="input-small" type="number" min="0" name="onlinetime" id="onlinetime" value="{#$onlinetime#}" data-regex="[0-9]\d*" />小时
        <span class="input-tips" style="display:inline-block;"><s></s>数字型，备注：一周=168小时， 一月=720小时， 一年=8760小时</span>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="cookiePath">Cookie 路径：</label></dt>
      <dd>
        <input class="input-large" type="text" name="cookiePath" id="cookiePath" value="{#$cookiePath#}" data-regex=".*" />
        <span class="input-tips"><s></s>默认为“/”。</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="cookieDomain">Cookie 作用域：</label></dt>
      <dd>
        <input class="input-large" type="text" name="cookieDomain" id="cookieDomain" value="{#$cookieDomain#}" data-regex=".*" style="margin-top: -5px;" />
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">默认为空，如果频道有子域名，这里需要填写不带www的网站域名；<br />例：网站域名为{#if strstr($basehost, 'www')#}{#$basehost#}{#else#}www.{#$basehost#}{#/if#}，新闻资讯域名为：news.{#$basehost|replace:'www.':''#}，那这里需要填写：{#$basehost|replace:'www.':''#}<br />如果网站无法正常退出，请检查此处配置，配置完成后，清除浏览器缓存后即可正常使用！！！</div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="cookiePre">Cookie 前缀：</label></dt>
      <dd>
        <input class="input-large" type="text" name="cookiePre" id="cookiePre" value="{#$cookiePre#}" data-regex=".*" />
        <span class="input-tips"><s></s>防止cookie混乱。</span>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="cache_lifetime">页面缓存时间：</label></dt>
      <dd>
        <input class="input-small" type="number" min="0" name="cache_lifetime" id="cache_lifetime" value="{#$cache_lifetime#}" data-regex="[0-9]\d*" />秒
        <span class="input-tips" style="display:inline-block;"><s></s>数字类型，0表示关闭页面缓存</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="lang">多语言：</label></dt>
      <dd>
        <select name="lang_" id="lang_">
          <option value="">请选择</option>
          {#html_options values=$langValue selected=$langSelected output=$langName#}
        </select>
        <span class="input-tips" style="display:inline-block;"><s></s><font color="#ff0000">修改此配置需要清除页面缓存。</font><br /><a href="https://help.kumanyun.com/help-7-610.html" target="_blank">查看二开/修改教程</a></span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="cookiePre">远程静态资源地址：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="remoteStatic" id="remoteStatic" value="{#$remoteStatic#}" />
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">静态资源包括：/static/和/templates/两个目录下的文件，如果需要将静态资源单独存放到一台服务器，须将这两个文件夹同时上传，然后将远程地址填写到这里<br />例如：http://static.ihuoniao.cn<br />如果没有将静态资源存放到远程服务器，这里请留空<br /><font color="#ff0000">注意，如果开启此选项，后台所有在线编辑css和js的功能将失效。</font></div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>搜索引擎抓取首页规则：</label></dt>
      <dd class="radio">
        {#html_radios name="spiderIndex" values=$spiderIndexState checked=$spiderIndexStateChecked output=$spiderIndexStateNames separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">如果只开通了一个城市分站，此项配置无需配置！<br />切换城市页：引导搜索引擎抓取所有城市分站 <font color="#ff0000">推荐</font><br />系统默认：引导搜索引擎抓取蜘蛛所在地的分站首页，如所在地未开通分站，则抓取默认城市分站，如果没有设置默认城市，则抓取切换城市页</div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label>开启伪静态：</label></dt>
      <dd class="radio">
        {#html_radios name="urlRewrite" values=$urlRewriteState checked=$urlRewriteStateChecked output=$urlRewriteStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>附件路径存储方式：</label></dt>
      <dd class="radio">
        {#html_radios name="hideUrl" values=$hideUrlState checked=$hideUrlStateChecked output=$hideUrlStateNames separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">如果远程附件平台已经确认不再更改，建议选择“性能优先”，选择“性能优先”前请为远程附件绑定独立域名，不要使用云平台提供的默认域名，以免附件转移后数据丢失。</div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>第三方登录必须绑定手机：</label></dt>
      <dd class="radio">
        {#html_radios name="bindMobile" values=$bindMobileState checked=$bindMobileStateChecked output=$bindMobileStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>会员所属分站更新规则：</label></dt>
      <dd class="radio">
        {#html_radios name="memberCityid" values=$memberCityidState checked=$memberCityidStateChecked output=$memberCityidStateNames separator="&nbsp;&nbsp;"#}
		<div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">手动更新：以个人基本资料页的所在区域为准；<br />自动更新：以会员当前所在分站为准，会员切换分站也会更新；<br />从自动更新修改为手动更新，系统不会自动更新会员所在分站，如果需要变更会员所在分站为区域所在分站<br />可以通过【数据库内容替换】，找到【member】网站会员表，将cityid字段，替换为0，然后执行【同步会员所在分站(member_updateCityid.php)】计划任务即可！</div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>个人会员中心功能卡片：</label></dt>
      <dd class="radio">
        {#html_radios name="member_card" values=$member_card_state checked=$member_card_state_checked output=$member_card_names separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">用于配置个人会员中心功能卡片显示状态。<br />如果系统开通了很多模块，新用户打开会员中心如果显示所有模块功能，会显得东西太多，增加用户学习成本；<br />关闭状态下，新用户打开会员中心将只显示基本功能，和模块相关的会自动隐藏，只有用户对模块信息有交互(发布信息)时，对应的卡片才会自动显示，或者用户手动开启要显示的功能卡片；<br /><font color="#ff0000">注意：该功能只对移动端，并且个人会员移动端首页模板为默认模式时生效！</font></div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>https安全访问：</label></dt>
      <dd class="radio">
        {#html_radios name="httpSecureAccess" values=$httpSecureAccessState checked=$httpSecureAccessStateChecked output=$httpSecureAccessStateNames separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">此功能需要服务器配置好域名证书。<br />开启后如果不能正常访问，请将/include/config/siteConfig.inc.php中的$cfg_httpSecureAccess值修改为0<br /><font color="#ff0000">修改此配置后，需要重新进入网站后台，或者刷新浏览器，网站后台才可以正常使用。</font></div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>负载均衡：</label></dt>
      <dd class="radio">
        {#html_radios name="slb" values=$slbState checked=$slbStateChecked output=$slbStateNames separator="&nbsp;&nbsp;"#}
        <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;"><font color="#ff0000">启用后，根据第三方平台要求，如果需要关闭WEB端的SSL，只停用宝塔或者CDN中配置的HTTPS服务即可，上方的https安全访问设置请保留开启状态！</font></div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>页面变灰：</label></dt>
      <dd class="radio">
        {#html_radios name="sitePageGray" values=$sitePageGray checked=$sitePageGrayChecked output=$sitePageGrayNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
      <dl class="clearfix">
        <dt><label>商家幻灯广告：</label></dt>
        <dd class="radio">
          {#html_radios name="vipAdvertising" values=$vipAdvertising checked=$vipAdvertisingChecked output=$vipAdvertisingNames separator="&nbsp;&nbsp;"#}
          <div style="font-size: 12px; color: rgb(153, 153, 153); padding-top: 10px;">开启此功能，需要同时修改信息详情的【商家幻灯广告】选项为显示，此功能会将详情页面中的幻灯图片展示为固定广告内容！<br />涉及到的页面有：分类信息商家/房产楼盘、二手房、租房、商铺、写字楼、厂房、车位/装修商家/团购商家/教育商家/家政商家/汽车商家/养老机构等<br /><a href="https://help.kumanyun.com/help-5-737.html" target="_blank">配置教程</a></div>
        </dd>
      </dl>
    <dl class="clearfix">
      <dt><label>Debug模式：</label></dt>
      <dd class="radio">
        {#html_radios name="siteDebug" values=$siteDebugState checked=$siteDebugStateChecked output=$siteDebugStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
  </div>
  <div class="item hide" id="tplList">
    <div class="tpl-list">
      <h5 class="stit"><span class="label label-info">电脑端：</span></h5>
      <ul class="clearfix">
      	{#foreach from=$tplList item=tplItem#}
      	<li{#if $template == $tplItem.directory#} class="current"{#/if#}>
          <a href="javascript:;" data-id="{#$tplItem.directory#}" data-title="{#$tplItem.tplname#}" class="img" title="模板名称：{#$tplItem.tplname#}&#10;版权所有：{#$tplItem.copyright#}"><img src="{#$adminPath#}../templates/siteConfig/{#$tplItem.directory#}/preview.jpg?v={#$cfg_staticVersion#}" /></a>
          <p>
            <span title="{#$tplItem.tplname#}">{#$tplItem.tplname#}({#$tplItem.directory#})</span><br />
            <a href="javascript:;" class="choose">选择</a><br />
            <a href="javascript:;" class="edit">编辑模板</a><br />
            <a href="javascript:;" class="del">卸载</a>
          </p>
        </li>
      	{#/foreach#}
      </ul>
      <input type="hidden" name="template" id="template" value="{#$template#}" />
    </div>
    <div class="tpl-list touch">
      <h5 class="stit"><span class="label label-warning">移动端：</span></h5>
      <ul class="clearfix">
      	{#foreach from=$touchTplList item=tplItem#}
      	<li{#if $touchTemplate == $tplItem.directory#} class="current"{#/if#}>
          <a href="javascript:;" {#if $tplItem.tplname == 'diy'#}style="cursor: default;"{#/if#} data-id="{#$tplItem.directory#}" data-title="{#$tplItem.tplname#}" class="img" title="模板名称：{#if $tplItem.tplname == 'diy'#}DIY模板{#else#}{#$tplItem.tplname#}{#/if#}&#10;版权所有：{#$tplItem.copyright#}"><img src="{#if $tplItem.tplname == 'diy'#}/static/images/admin/diy_template_icon.png?v={#$cfg_staticVersion#}{#else#}{#$adminPath#}../templates/siteConfig/touch/{#$tplItem.directory#}/preview.jpg?v={#$cfg_staticVersion#}{#/if#}" /></a>
          <p>
            {#if $tplItem.tplname != 'diy'#}
            <span title="{#$tplItem.tplname#}">{#$tplItem.tplname#}({#$tplItem.directory#})</span><br />
            {#else#}
            <span>DIY模板</span><br />
            {#/if#}
            <a href="javascript:;" class="choose">选择</a><br />
            <a {#if $tplItem.tplname == 'diy'#}href="sitePageDiy.php" target="_blank"{#else#}href="javascript:;"{#/if#} class="edit">{#if $tplItem.tplname == 'diy'#}装修页面{#else#}编辑模板{#/if#}</a><br />
            {#if $tplItem.tplname != 'diy'#}
            <a href="javascript:;" class="del">卸载</a>
            {#/if#}
          </p>
        </li>
      	{#/foreach#}
      </ul>
      <input type="hidden" name="touchTemplate" id="touchTemplate" value="{#$touchTemplate#}" />
    </div>
    <div class="tpl-list comment">
      <h5 class="stit"><span class="label label-success">公共资源：</span></h5>
      <p>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="siteConfig" href="javascript:;">公共模板</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="about" href="javascript:;">关于我们-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="about" href="javascript:;">关于我们-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="protocol" href="javascript:;">网站协议-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="protocol" href="javascript:;">网站协议-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="help" href="javascript:;">帮助中心-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="help" href="javascript:;">帮助中心-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="notice" href="javascript:;">网站公告-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="notice" href="javascript:;">网站公告-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="feedback" href="javascript:;">意见反馈-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="feedback" href="javascript:;">意见反馈-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="middlejump" href="javascript:;">安全跳转-电脑端</a>
          <a class="btn btn-small" data-type="public" data-touch="touch" data-action="middlejump" href="javascript:;">安全跳转-移动端</a>
          <a class="btn btn-small" data-type="public" data-touch="" data-action="courier" href="javascript:;">骑手端</a>
      </p>
      <p><br /></p>
    </div>
    <div class="tpl-list comment">
      <h5 class="stit"><span class="label label-success">会员中心：</span></h5>
      <dl class="clearfix" style="margin-bottom: 10px;">
        <dt><label>个人会员移动端首页模板：</label></dt>
        <dd class="radio">
          <label><input type="radio" name="userCenterTouchTemplateType" value="0" {#if !$userCenterTouchTemplateType#}checked{#/if#}/>默认</label>
          <label><input type="radio" name="userCenterTouchTemplateType" value="1" {#if $userCenterTouchTemplateType#}checked{#/if#}/>DIY模式</label>
          <div style="font-size: 12px; padding-top: 10px;">
            启用DIY模式后，APP端需要设置【<a href="siteConfig/siteFooterBtn.php" id="bottomButton">手机底部导航</a>】我的原生页面为<code>用户中心</code> 时生效！小程序端我的页面跳转链接为<code>/pages/member/index/index</code>
          </div>
        </dd>
      </dl>
      <dl class="clearfix" style="margin-bottom: 10px;">
        <dt><label>商家会员移动端首页模板：</label></dt>
        <dd class="radio">
          <label><input type="radio" name="busiCenterTouchTemplateType" value="0" {#if !$busiCenterTouchTemplateType#}checked{#/if#}/>默认</label>
          <label><input type="radio" name="busiCenterTouchTemplateType" value="1" {#if $busiCenterTouchTemplateType#}checked{#/if#}/>DIY模式</label>
          <div style="font-size: 12px; padding-top: 10px;">&nbsp;</div>
        </dd>
      </dl>
      <p>
          <a class="btn btn-small" data-type="member" data-touch="" data-action="member" href="javascript:;">个人会员-电脑端</a>
          <a class="btn btn-small" data-type="member" data-touch="touch" data-action="member" href="javascript:;">个人会员-移动端</a>
          <a class="btn btn-small" data-type="diy" data-touch="touch" data-action="member" href="javascript:;">个人会员-首页DIY</a>
          <a class="btn btn-small" data-type="company" data-touch="" data-action="member" href="javascript:;">企业会员-电脑端</a>
          <a class="btn btn-small" data-type="company" data-touch="touch" data-action="member" href="javascript:;">企业会员-移动端</a>
      </p>
      <p><br /></p>
    </div>
  </div>
  <div class="item hide">
    <dl class="clearfix">
      <dt><label for="uploadDir">上传目录：</label></dt>
      <dd>
        <input class="input-large" type="text" name="uploadDir" id="uploadDir" value="{#$uploadDir#}" data-regex=".*" />
        <span class="input-tips" style="display: inline-block;"><s></s>默认为：/uploads&nbsp;&nbsp;<font color="#ff0000">注意：不要以 / 结尾！</font></span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="softSize">附件上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="softSize" id="softSize" data-regex="[0-9]\d*" min="0" value="{#$softSize#}" />kb
        <span class="input-tips"><s></s>上传附件限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="softType">附件类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="softType" id="softType" placeholder="多个用“|”分开" value="{#$softType#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="thumbSize">缩略图上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="thumbSize" id="thumbSize" data-regex="[0-9]\d*" min="0" value="{#$thumbSize#}" />kb
        <span class="input-tips"><s></s>上传图片限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="thumbType">缩略图类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="thumbType" id="thumbType" placeholder="多个用“|”分开" value="{#$thumbType_#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="atlasSize">图集上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="atlasSize" id="atlasSize" data-regex="[0-9]\d*" min="0" value="{#$atlasSize#}" />kb
        <span class="input-tips"><s></s>上传图片限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="atlasType">图集类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="atlasType" id="atlasType" placeholder="多个用“|”分开" value="{#$atlasType#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="editorSize">编辑器图片限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="editorSize" id="editorSize" data-regex="[0-9]\d*" min="0" value="{#$editorSize#}" />kb
        <span class="input-tips"><s></s>上传图片限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="editorType">编辑器图片类型：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="editorType" id="editorType" placeholder="多个用“|”分开" value="{#$editorType#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="photoSize">照片上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="photoSize" id="photoSize" data-regex="[0-9]\d*" min="0" value="{#$photoSize#}" />kb
        <span class="input-tips"><s></s>上传照片限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="photoType">照片类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="photoType" id="photoType" placeholder="多个用“|”分开" value="{#$photoType_#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="flashSize">flash上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="flashSize" id="flashSize" data-regex="[0-9]\d*" min="0" value="{#$flashSize#}" />kb
        <span class="input-tips"><s></s>上传Flash限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="audioSize">音频上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="audioSize" id="audioSize" data-regex="[0-9]\d*" min="0" value="{#$audioSize#}" />kb
        <span class="input-tips"><s></s>上传音频限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="audioType">音频类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="audioType" id="audioType" placeholder="多个用“|”分开" value="{#$audioType_#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="audioSize">视频上传限制：</label></dt>
      <dd>
        <input class="input-small" type="number" name="videoSize" id="videoSize" data-regex="[0-9]\d*" min="0" value="{#$videoSize#}" />kb
        <span class="input-tips"><s></s>上传视频限制大小，如果超过这个大小将不能上传，只能填写数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="audioType">视频类型限制：</label></dt>
      <dd>
        <input class="input-xxlarge" type="text" name="videoType" id="videoType" placeholder="多个用“|”分开" value="{#$videoType_#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>极速上传：</label></dt>
      <dd class="radio">
        <label><input type="radio" name="fastUpload" value="1" {#if $fastUpload#}checked{#/if#}/>开启</label>
        <label><input type="radio" name="fastUpload" value="0" {#if !$fastUpload#}checked{#/if#}/>关闭</label>
        <div style="font-size: 12px; padding-top: 10px;">
          开启后，相同文件重复上传会触发该功能，可以有效减少垃圾文件。
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>图片上传压缩：</label></dt>
      <dd class="radio">
        <label><input type="radio" name="imageCompress" value="1" {#if $imageCompress#}checked{#/if#}/>开启</label>
        <label><input type="radio" name="imageCompress" value="0" {#if !$imageCompress#}checked{#/if#}/>关闭</label>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>视频上传开关：</label></dt>
      <dd class="radio">
        <label><input type="radio" name="videoUploadState" value="1" {#if $videoUploadState#}checked{#/if#}/>开启</label>
        <label><input type="radio" name="videoUploadState" value="0" {#if !$videoUploadState#}checked{#/if#}/>关闭</label>
        <div style="font-size: 12px; padding-top: 10px;">
          关闭后，{#getModuleTitle name='tieba'#}和{#getModuleTitle name='circle'#}模块在发布信息时将不支持上传视频
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>视频上传压缩：</label></dt>
      <dd class="radio">
        <label><input type="radio" name="videoCompress" value="1" {#if $videoCompress#}checked{#/if#}/>开启</label>
        <label><input type="radio" name="videoCompress" value="0" {#if !$videoCompress#}checked{#/if#}/>关闭</label>
        <div style="font-size: 12px; padding-top: 10px;">
          使用此功能还需要到远程附件设置中开启<code>FFmpeg</code>，并且新增执行文件为<code>system_videoCompress.php</code>的计划任务！
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label>缩略图缩图：</label></dt>
      <dd>
        小图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="thumbSmallWidth" name="thumbSmallWidth" type="text" value="{#$thumbSmallWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="thumbSmallHeight" name="thumbSmallHeight" type="text" value="{#$thumbSmallHeight#}" />
          <span class="add-on">px</span>
        </div><br />
        中图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="thumbMiddleWidth" name="thumbMiddleWidth" type="text" value="{#$thumbMiddleWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="thumbMiddleHeight" name="thumbMiddleHeight" type="text" value="{#$thumbMiddleHeight#}" />
          <span class="add-on">px</span>
        </div><br >
        大图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="thumbLargeWidth" name="thumbLargeWidth" type="text" value="{#$thumbLargeWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="thumbLargeHeight" name="thumbLargeHeight" type="text" value="{#$thumbLargeHeight#}" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label>图集缩图：</label></dt>
      <dd>
        小图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="atlasSmallWidth" name="atlasSmallWidth" type="text" value="{#$atlasSmallWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="atlasSmallHeight" name="atlasSmallHeight" type="text" value="{#$atlasSmallHeight#}" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>照片头像缩图：</label></dt>
      <dd>
        小图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="photoSmallWidth" name="photoSmallWidth" type="text" value="{#$photoSmallWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="photoSmallHeight" name="photoSmallHeight" type="text" value="{#$photoSmallHeight#}" />
          <span class="add-on">px</span>
        </div><br />
        中图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="photoMiddleWidth" name="photoMiddleWidth" type="text" value="{#$photoMiddleWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="photoMiddleHeight" name="photoMiddleHeight" type="text" value="{#$photoMiddleHeight#}" />
          <span class="add-on">px</span>
        </div><br >
        大图：
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="photoLargeWidth" name="photoLargeWidth" type="text" value="{#$photoLargeWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="photoLargeHeight" name="photoLargeHeight" type="text" value="{#$photoLargeHeight#}" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="meditorPicWidth">移动端内容图片宽：</label></dt>
      <dd>
        <input class="input-small" type="number" name="meditorPicWidth" id="meditorPicWidth" data-regex="[0-9]\d*" min="0" value="{#$meditorPicWidth#}" />
        <span class="input-tips"><s></s>移动端内容图片的宽度将以此值进行裁切！</span>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label>图片裁剪方法：</label></dt>
      <dd>
        <label style="display:block;"><input type="radio" name="photoCutType" value="force"{#if $photoCutType == "force"#} checked{#/if#} />按规定尺寸强制变形</label>
        <label style="display:block;"><input type="radio" name="photoCutType" value="scale"{#if $photoCutType == "scale"#} checked{#/if#} />等比例裁剪（此方法不会导致图片变形，但是输出的图像大小不完全等于设置的尺寸）</label>
        <label style="display:block;"><input type="radio" name="photoCutType" value="scale_fill"{#if $photoCutType == "scale_fill"#} checked{#/if#} />按比例在规定尺寸内缩放，空白处将以白色填充</label>
        <label style="display:block;"><input type="radio" name="photoCutType" value="position"{#if $photoCutType == "position"#} checked{#/if#} />从指定位置截取</label>
        {#if $photoCutType == "position"#}
        <div id="photoCutPosition">
        {#else#}
        <div id="photoCutPosition" class="hide">
        {#/if#}
          <ul class="clearfix watermarkpostion">
            <li{#if $photoCutPostion == "north_west"#} class="current"{#/if#} data-id="north_west"><a href="javascript:;">左上</a></li>
            <li{#if $photoCutPostion == "north"#} class="current"{#/if#} data-id="north"><a href="javascript:;">中上</a></li>
            <li{#if $photoCutPostion == "north_east"#} class="current"{#/if#} data-id="north_east"><a href="javascript:;">右上</a></li>
            <li{#if $photoCutPostion == "west"#} class="current"{#/if#} data-id="west"><a href="javascript:;">左中</a></li>
            <li{#if $photoCutPostion == "center"#} class="current"{#/if#} data-id="center"><a href="javascript:;">中心</a></li>
            <li{#if $photoCutPostion == "east"#} class="current"{#/if#} data-id="east"><a href="javascript:;">右中</a></li>
            <li{#if $photoCutPostion == "south_west"#} class="current"{#/if#} data-id="south_west"><a href="javascript:;">左下</a></li>
            <li{#if $photoCutPostion == "south"#} class="current"{#/if#} data-id="south"><a href="javascript:;">中下</a></li>
            <li{#if $photoCutPostion == "south_east"#} class="current"{#/if#} data-id="south_east"><a href="javascript:;">右下</a></li>
          </ul>
          <input type="hidden" name="photoCutPostion" value="{#$photoCutPostion#}" />
        </div>
      </dd>
    </dl>
    <dl class="clearfix hide">
      <dt><label for="quality">图片质量：</label></dt>
      <dd>
        <input class="input-small" type="number" name="quality" id="quality" data-regex="(([0-9]\d?)|(100)|(0))" min="0" max="100" value="{#$quality#}" />
        <span class="input-tips"><s></s>数字越大越清晰，最高100，建议设置85</span>
      </dd>
    </dl>
  </div>
  <div class="item hide">
      <div class="alert alert-success" style="margin:10px 10px 15px 50px;"><button type="button" class="close" data-dismiss="alert">×</button>阿里云OSS配置教程：<a href="https://help.kumanyun.com/help-225-653.html" target="_blank">https://help.kumanyun.com/help-225-653.html</a><br />华为云OBS配置教程：<a href="https://help.kumanyun.com/help-68-703.html" target="_blank">https://help.kumanyun.com/help-68-703.html</a><br />腾讯云COS配置教程：<a href="https://help.kumanyun.com/help-226-716.html" target="_blank">https://help.kumanyun.com/help-226-716.html</a><br /><span class="text-error">注意：远程附件最好选择和服务器同一个厂商的同一区域，如：用了阿里云上海一区域的服务器，也用阿里云上海一区域的OSS。<br />尽可能避免这类情况：使用华为云的服务器，远程附件用阿里云的OSS，这将可能出现大文件上传超时的情况。</span><br />附件分离配置教程：<a href="https://help.kumanyun.com/help-68-758.html" target="_blank">https://help.kumanyun.com/help-68-758.html</a></div>
    <dl class="clearfix">
      <dt><label for="ftpType">远程服务器类型：</label></dt>
      <dd>
        {#html_radios name="ftpType" values=$ftpType checked=$ftpTypeChecked output=$ftpTypeNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <div id="ftpType0" class="hide ftpType">
      <dl class="clearfix">
        <dt><label for="ftpStateType">启用远程附件：</label></dt>
        <dd>
          {#html_radios name="ftpStateType" values=$ftpStateType checked=$ftpStateChecked output=$ftpStateNames separator="&nbsp;&nbsp;"#}
        </dd>
      </dl>
      <div id="ftpConfig">
        <dl class="clearfix">
          <dt><label for="ftpSSL">启用SSL连接：</label></dt>
          <dd>
            {#html_radios name="ftpSSL" values=$ftpSSL checked=$ftpSSLChecked output=$ftpSSLNames separator="&nbsp;&nbsp;"#}
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpPasv">被动模式连接：</label></dt>
          <dd>
            {#html_radios name="ftpPasv" values=$ftpPasv checked=$ftpPasvChecked output=$ftpPasvNames separator="&nbsp;&nbsp;"#}
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpUrl">远程附件地址：</label></dt>
          <dd>
            <input class="input-large" type="text" name="ftpUrl" id="ftpUrl" value="{#$ftpUrl#}" data-regex=".*" />
            <span class="input-tips"><s></s>&nbsp;</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpServer">FTP服务器地址：</label></dt>
          <dd>
            <input class="input-large" type="text" name="ftpServer" id="ftpServer" value="{#$ftpServer#}" data-regex=".*" />
            <span class="input-tips"><s></s>&nbsp;</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpPort">FTP服务器端口：</label></dt>
          <dd>
            <input class="input-mini" type="number" name="ftpPort" id="ftpPort" value="{#$ftpPort#}" data-regex="[0-9]\d*" />
            <span class="input-tips"><s></s>请正确输入，类型为数字</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpDir">FTP上传目录：</label></dt>
          <dd>
            <input class="input-large" type="text" name="ftpDir" id="ftpDir" value="{#$ftpDir#}" data-regex=".*" />
            <span class="input-tips"><s></s>&nbsp;</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpUser">FTP帐号：</label></dt>
          <dd>
            <input class="input-large" type="text" name="ftpUser" id="ftpUser" value="{#$ftpUser#}" data-regex=".*" />
            <span class="input-tips"><s></s>&nbsp;</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpPwd">FTP密码：</label></dt>
          <dd>
            <input class="input-large" type="text" name="ftpPwd" id="ftpPwd" value="{#$ftpPwd#}" data-regex=".*" />
            <span class="input-tips"><s></s>&nbsp;</span>
          </dd>
        </dl>
        <dl class="clearfix">
          <dt><label for="ftpTimeout">FTP超时：</label></dt>
          <dd>
            <input class="input-mini" type="number" name="ftpTimeout" id="ftpTimeout" value="{#$ftpTimeout#}" data-regex="[0-9]\d*" />秒
            <span class="input-tips"><s></s>请正确输入，类型为数字</span>
          </dd>
        </dl>
      </div>
    </div>
    <div id="ftpType1" class="hide ftpType">
      <dl class="clearfix">
        <dt><label for="OSSUrl">Bucket 域名：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="OSSUrl" id="OSSUrl" value="{#$OSSUrl#}" data-regex=".*" />
          <span class="input-tips" style="display:inline-block;"><s></s>&nbsp;完整的Http地址。如果没有绑定域名地址为：（Bucket name）.oss.aliyuncs.com，<font color="#ff0000">不要以 / 结尾！</font></span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OSSBucket">Bucket名称：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OSSBucket" id="OSSBucket" value="{#$OSSBucket#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="EndPoint">EndPoint：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="EndPoint" id="EndPoint" value="{#$EndPoint#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OSSKeyID">Access Key ID：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OSSKeyID" id="OSSKeyID" value="{#$OSSKeyID#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OSSKeySecret">Access Key Secret：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OSSKeySecret" id="OSSKeySecret" value="{#$OSSKeySecret#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
    </div>
    <div id="ftpType2" class="hide ftpType">
        <dl class="clearfix">
            <dt><label for="AccessKey">AccessKey：</label></dt>
            <dd>
                <input class="input-xxlarge" type="text" name="access_key" id="access_key" value="{#$access_key#}" />
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="SecretKey">SecretKey：</label></dt>
            <dd>
                <input class="input-xxlarge" type="text" name="secret_key" id="secret_key" value="{#$secret_key#}" />
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="bucket">存储空间（bucket）：</label></dt>
            <dd>
                <input class="input-xlarge" type="text" name="bucket" id="bucket" value="{#$bucket#}" />
            </dd>
        </dl>
        <dl class="clearfix">
            <dt><label for="domain">外链域名：</label></dt>
            <dd>
                <input class="input-xlarge" type="text" name="domain" id="domain" value="{#$domain#}" data-regex=".*" />
                <span class="input-tips" style="display:inline-block;"><s></s>&nbsp;完整的Http地址。如果没有绑定域名地址填写测试域名，<font color="#ff0000">以 / 结尾！</font></span>
            </dd>
        </dl>
    </div>
    <div id="ftpType3" class="hide ftpType">
      <dl class="clearfix">
        <dt><label for="OBSUrl">访问域名：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="OBSUrl" id="OBSUrl" value="{#$OBSUrl#}" data-regex=".*" />
          <span class="input-tips" style="display:inline-block;"><s></s>&nbsp;完整的Http地址，域名在华为云OBS对象存储的基本信息中，<font color="#ff0000">以 / 结尾！</font>。</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OBSBucket">桶名称：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OBSBucket" id="OBSBucket" value="{#$OBSBucket#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OBSEndpoint">Endpoint：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OBSEndpoint" id="OBSEndpoint" value="{#$OBSEndpoint#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OBSKeyID">Access Key Id：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="OBSKeyID" id="OBSKeyID" value="{#$OBSKeyID#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="OBSKeySecret">Secret Access Key：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="OBSKeySecret" id="OBSKeySecret" value="{#$OBSKeySecret#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
    </div>
    <div id="ftpType4" class="hide ftpType">
      <dl class="clearfix">
        <dt><label for="COSUrl">访问域名：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="COSUrl" id="COSUrl" value="{#$COSUrl#}" data-regex=".*" />
          <span class="input-tips" style="display:inline-block;"><s></s>&nbsp;完整的Http地址，域名在腾讯云COS存储桶概览的域名信息中。</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="COSBucket">存储桶名称：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="COSBucket" id="COSBucket" value="{#$COSBucket#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="COSRegion">所属地域：</label></dt>
        <dd>
          <input class="input-xlarge" type="text" name="COSRegion" id="COSRegion" value="{#$COSRegion#}" data-regex=".*" placeholder="ap-" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="COSSecretid">密钥SecretId：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="COSSecretid" id="COSSecretid" value="{#$COSSecretid#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="COSSecretkey">密钥SecretKey：</label></dt>
        <dd>
          <input class="input-xxlarge" type="text" name="COSSecretkey" id="COSSecretkey" value="{#$COSSecretkey#}" data-regex=".*" />
          <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
    </div>
    <dl class="clearfix">
      <dt>状态：</dt>
      <dd class="singel-line">
        <a href="javascript:;" id="checkFtpConn">点击检测是否可用</a>
      </dd>
    </dl>
    <dl class="clearfix">
        <dt><label for="remoteFtpUnify">统一管理：</label></dt>
        <dd>
            {#html_radios name="remoteFtpUnify" values=$remoteFtpUnify checked=$remoteFtpUnifyChecked output=$remoteFtpUnifyNames separator="&nbsp;&nbsp;"#}
            <div style="font-size: 12px; padding-top: 10px; color: #666;">
              如果其他模块设置中没有自定义远程附件服务器，请选择“开启”，这将会减轻服务器压力
            </div>
        </dd>
    </dl>
    <dl class="clearfix">
        <dt><label for="ffmpeg">FFmpeg：</label></dt>
        <dd>
            {#html_radios name="ffmpeg" values=$ffmpeg checked=$ffmpegChecked output=$ffmpegNames separator="&nbsp;&nbsp;"#}
            <div style="font-size: 12px; padding-top: 10px; color: #666;">
              主要用于截取视频封面、视频转码和视频压缩，此功能需要在服务器上安装好FFmpeg软件，<a href="https://help.kumanyun.com/help-55-771.html" target="_blank">安装教程>></a>，开启此功能会影响服务器性能，请谨慎使用！
            </div>
        </dd>
    </dl>
  </div>
  <div class="item hide">
    <dl class="clearfix">
      <dt><label for="thumbMarkState">缩略图水印：</label></dt>
      <dd>
        {#html_radios name="thumbMarkState" values=$thumbMarkState checked=$thumbMarkStateChecked output=$thumbMarkStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="atlasMarkState">图集水印：</label></dt>
      <dd>
        {#html_radios name="atlasMarkState" values=$atlasMarkState checked=$atlasMarkStateChecked output=$atlasMarkStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="editorMarkState">编辑器水印：</label></dt>
      <dd>
        {#html_radios name="editorMarkState" values=$editorMarkState checked=$editorMarkStateChecked output=$editorMarkStateNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>水印尺寸限制：</label></dt>
      <dd>
        <div class="input-prepend input-append">
          <span class="add-on">宽</span>
          <input class="span1" id="waterMarkWidth" name="waterMarkWidth" type="text" value="{#$waterMarkWidth#}" />
          <span class="add-on">px</span>
        </div>
        &times;
        <div class="input-prepend input-append">
          <span class="add-on">高</span>
          <input class="span1" id="waterMarkHeight" name="waterMarkHeight" type="text" value="{#$waterMarkHeight#}" />
          <span class="add-on">px</span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>水印位置：</label></dt>
      <dd>
        <ul class="clearfix watermarkpostion">
          <li{#if $waterMarkPostion == 1#} class="current"{#/if#} data-id="1"><a href="javascript:;">左上</a></li>
          <li{#if $waterMarkPostion == 2#} class="current"{#/if#} data-id="2"><a href="javascript:;">中上</a></li>
          <li{#if $waterMarkPostion == 3#} class="current"{#/if#} data-id="3"><a href="javascript:;">右上</a></li>
          <li{#if $waterMarkPostion == 4#} class="current"{#/if#} data-id="4"><a href="javascript:;">左中</a></li>
          <li{#if $waterMarkPostion == 5#} class="current"{#/if#} data-id="5"><a href="javascript:;">中心</a></li>
          <li{#if $waterMarkPostion == 6#} class="current"{#/if#} data-id="6"><a href="javascript:;">右中</a></li>
          <li{#if $waterMarkPostion == 7#} class="current"{#/if#} data-id="7"><a href="javascript:;">左下</a></li>
          <li{#if $waterMarkPostion == 8#} class="current"{#/if#} data-id="8"><a href="javascript:;">中下</a></li>
          <li{#if $waterMarkPostion == 9#} class="current"{#/if#} data-id="9"><a href="javascript:;">右下</a></li>
          <li{#if $waterMarkPostion == 10#} class="current"{#/if#} data-id="10" style="width: 105px;"><a href="javascript:;">平铺</a></li>
          <li{#if !$waterMarkPostion#} class="current"{#/if#} data-id="0" style="width: 103px;"><a href="javascript:;">随机</a></li>
        </ul>
        <input type="hidden" name="waterMarkPostion" id="waterMarkPostion" value="{#$waterMarkPostion#}" />
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label>水印类型：</label></dt>
      <dd>
        {#html_radios name="waterMarkType" values=$waterMarkType checked=$waterMarkTypeChecked output=$waterMarkTypeNames separator="&nbsp;&nbsp;"#}
      </dd>
    </dl>
    {#if $waterMarkTypeChecked == 1#}
    <div id="markType1">
    {#else#}
    <div id="markType1" class="hide">
    {#/if#}
      <dl class="clearfix">
        <dt><label for="markText">水印文字：</label></dt>
        <dd>
          <input class="input-large" type="text" name="markText" id="markText" value="{#$markText#}" data-regex=".*" />
        <span class="input-tips"><s></s>&nbsp;</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="markFontfamily">水印字体：</label></dt>
        <dd>
          <select name="markFontfamily" id="markFontfamily">
            {#html_options values=$markFontfamily selected=$markFontfamilySelected output=$markFontfamily#}
          </select>
          <span class="input-tips" style="display:inline-block;"><s></s>水印文件存放在{#$HUONIAOINC#}/data/fonts下<br />如果是汉字水印，请选择“simhei.ttf”，否则水印不显示！</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label for="markFontsize">水印文字大小：</label></dt>
        <dd>
          <input class="input-small" type="number" min="0" name="markFontsize" id="markFontsize" data-regex="[0-9]\d*" value="{#$markFontsize#}" />
          <span class="input-tips"><s></s>水印文字大小，类型为数字</span>
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label>文字颜色：</label></dt>
        <dd>
          <div class="color_pick markFontColor" style="margin:0; border:1px solid #ccc;"><em style="background:{#$markFontColor#};"></em></div>
          <input type="hidden" name="markFontColor" id="markFontColor" value="{#$markFontColor#}" />
        </dd>
      </dl>
    </div>
    {#if $waterMarkTypeChecked == 1#}
    <div id="markType2" class="hide">
    {#else#}
    <div id="markType2">
    {#/if#}
      <dl class="clearfix">
        <dt><label for="markFile">水印文件：</label></dt>
        <dd>
          <select name="markFile" id="markFile">
            {#html_options values=$markFile selected=$markFileSelected output=$markFile#}
          </select>
          <span class="input-tips" style="display:inline-block;"><s></s>水印文件存放在{#$HUONIAOINC#}/data/mark下</span>
        </dd>
      </dl>
    </div>
    <dl class="clearfix">
      <dt><label for="markPadding">水印边距：</label></dt>
      <dd>
        <input class="input-small" type="number" min="0" name="markPadding" id="markPadding" data-regex="[0-9]\d*" value="{#$markPadding#}" />
        <span class="input-tips"><s></s>水印位置与周边距离</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="transparent">水印透明度：</label></dt>
      <dd>
        <input class="input-small" type="number" name="transparent" id="transparent" data-regex="(([0-9]\d?)|(100)|(0))" min="0" max="100" maxlength="3" value="{#$transparent#}" />
        <span class="input-tips" style="display: inline-block;"><s></s>数值越大越清晰，范围0到100，注意：PNG图片水印需要调整水印自身透明度，此处设置无效！</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="markQuality">水印质量：</label></dt>
      <dd>
        <input class="input-small" type="number" name="markQuality" id="markQuality" data-regex="(([0-9]\d?)|(100)|(0))" min="0" max="100" maxlength="3" value="{#$markQuality#}" />
        <span class="input-tips"><s></s>数字越大越清晰，最高100，建议设置85</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="zizhiText">资质水印文字：</label></dt>
      <dd>
        <input class="input-large" type="text" name="zizhiText" id="zizhiText" value="{#$zizhiText#}" />
        <span class="input-tips" style="display: inline-block;"><s></s>资质类图片水印文字【固定为文本平铺样式，留空时不加水印】</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="zizhiTextFontsize">资质水印文字大小：</label></dt>
      <dd>
        <input class="input-small" type="number" min="0" name="zizhiTextFontsize" id="zizhiTextFontsize" data-regex="[0-9]\d*" value="{#$zizhiTextFontsize#}" />
        <span class="input-tips"><s></s>水印文字大小，类型为数字</span>
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="zizhiTextColor">资质水印文字颜色：</label></dt>
      <dd>
        <div class="color_pick zizhiTextColor" style="margin:0; border:1px solid #ccc;"><em style="background:{#$zizhiTextColor#};"></em></div>
        <input type="hidden" name="zizhiTextColor" id="zizhiTextColor" value="{#$zizhiTextColor#}" />
      </dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="zizhiTextTransParent">资质水印文字透明度：</label></dt>
      <dd>
        <input class="input-small" type="number" name="zizhiTextTransparent" id="zizhiTextTransParent" data-regex="(([0-9]\d?)|(100)|(0))" min="0" max="100" maxlength="3" value="{#$zizhiTextTransparent#}" />
        <span class="input-tips" style="display: inline-block;"><s></s>数值越大越清晰，范围0到100。</span>
      </dd>
    </dl>
  </div>
  <div class="item hide">
      <dl class="clearfix">
        <dt><label>货币：</label></dt>
        <dd>
          <div class="input-prepend">
            <span class="add-on">名称</span>
            <input style="width: 80px;" id="currency_name" name="currency_name" type="text" value="{#$currency_name#}" />
          </div>
          <div class="input-prepend">
            <span class="add-on">简称</span>
            <input class="span1" id="currency_short" name="currency_short" type="text" value="{#$currency_short#}" />
          </div>
          <div class="input-prepend">
            <span class="add-on">符号</span>
            <input class="span1" id="currency_symbol" name="currency_symbol" type="text" value="{#$currency_symbol#}" />
          </div>
          <div class="input-prepend">
            <span class="add-on">代码</span>
            <input class="span1" id="currency_code" name="currency_code" type="text" value="{#$currency_code#}" />
          </div><br />
          <div class="input-prepend input-append">
            <span class="add-on">汇率</span>
            <input class="span1" id="currency_rate" name="currency_rate" type="text" value="{#$currency_rate#}" />
            <span class="add-on">1元人民币可以兑换的值</span>
          </div>
        </dd>
      </dl>
    <dt><label>面积：</label></dt>
    <dd>
      <div class="input-prepend">
        <span class="add-on">名称</span>
        <input style="width: 80px;" id="currency_areaname" name="currency_areaname" type="text" value="{#$currency_areaname#}" />
      </div>

      <div class="input-prepend">
        <span class="add-on">符号</span>
        <input class="span1" id="currency_areasymbol" name="currency_areasymbol" type="text" value="{#$currency_areasymbol#}" />
      </div>
    </dd>
  </div>
  <div class="item hide">
      <dl class="clearfix">
        <dt><label>广告标识：</label></dt>
        <dd>
          {#html_radios name="advMarkState" values=$advMarkState checked=$advMarkStateChecked output=$advMarkStateNames separator="&nbsp;&nbsp;"#}
        </dd>
      </dl>
      <dl class="clearfix">
        <dt><label>标识位置：</label></dt>
        <dd>
          <ul class="clearfix watermarkpostion" style="width: 140px;">
            <li{#if $advMarkPostion == 0#} class="current"{#/if#} data-id="0"><a href="javascript:;">左上</a></li>
            <li{#if $advMarkPostion == 1#} class="current"{#/if#} data-id="1"><a href="javascript:;">右上</a></li>
            <li{#if $advMarkPostion == 2#} class="current"{#/if#} data-id="2"><a href="javascript:;">左下</a></li>
            <li{#if $advMarkPostion == 3#} class="current"{#/if#} data-id="3"><a href="javascript:;">右下</a></li>
          </ul>
          <input type="hidden" name="advMarkPostion" id="advMarkPostion" value="{#$advMarkPostion#}" />
        </dd>
      </dl>
  </div>
  <div class="item hide">
      <dl class="clearfix">
        <dd style="width: 900px; padding-left: 45px;">
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="deposit"{#if 'deposit'|in_array:$ucenterLinks#} checked{#/if#} />充值</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="withdraw"{#if 'withdraw'|in_array:$ucenterLinks#} checked{#/if#} />提现</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="convert"{#if 'convert'|in_array:$ucenterLinks#} checked{#/if#} />现金与积分兑换</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="record"{#if 'record'|in_array:$ucenterLinks#} checked{#/if#} />交易记录明细</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="transfer"{#if 'transfer'|in_array:$ucenterLinks#} checked{#/if#} />积分转赠</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="point"{#if 'point'|in_array:$ucenterLinks#} checked{#/if#} />积分记录明细</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="reward"{#if 'reward'|in_array:$ucenterLinks#} checked{#/if#} />打赏相关</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="balance"{#if 'balance'|in_array:$ucenterLinks#} checked{#/if#} />余额相关</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="food"{#if 'food'|in_array:$ucenterLinks#} checked{#/if#} />餐饮相关</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="checkout"{#if 'checkout'|in_array:$ucenterLinks#} checked{#/if#} />收银结算</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="business"{#if 'business'|in_array:$ucenterLinks#} checked{#/if#} />商家服务</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="order"{#if 'order'|in_array:$ucenterLinks#} checked{#/if#} />订单相关</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="fenxiao"{#if 'fenxiao'|in_array:$ucenterLinks#} checked{#/if#} />分销</label>
          <!-- <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="game"{#if 'game'|in_array:$ucenterLinks#} checked{#/if#} />游戏</label> -->
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="yaoqing"{#if 'yaoqing'|in_array:$ucenterLinks#} checked{#/if#} />邀请有礼</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="quan"{#if 'quan'|in_array:$ucenterLinks#} checked{#/if#} />优惠券</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="address"{#if 'address'|in_array:$ucenterLinks#} checked{#/if#} />收货地址</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="bonus"{#if 'bonus'|in_array:$ucenterLinks#} checked{#/if#} />消费金</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="kefu"{#if 'kefu'|in_array:$ucenterLinks#} checked{#/if#} />网站客服</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="businessManage"{#if 'businessManage'|in_array:$ucenterLinks#} checked{#/if#} />商家管理快捷入口</label>
          <label style="width: 200px;"><input type="checkbox" name="ucenterLinks[]" value="vip"{#if 'vip'|in_array:$ucenterLinks#} checked{#/if#} />vip会员</label>
        </dd>
      </dl>
  </div>
  <div class="item hide">
    <div class="alert alert-success" style="margin:10px 10px 0 50px;"><button type="button" class="close" data-dismiss="alert">×</button>目前系统用到此功能的地方主要是获取文章关键词，便于搜索引擎抓取以及后续针对会员进行个性化推荐和话题聚合；<br />配置教程：<a href="https://help.kumanyun.com/help-68-615.html" target="_blank">https://help.kumanyun.com/help-68-615.html</a></div>
    <div class="tips" style="margin: 15px 0 10px 50px;"><span class="label label-info">注意：此功能为非必须配置项，如果不想使用此功能，可以忽略，不影响系统正常使用，提取关键词的服务将由系统默认提供，只不过不够智能而已。</span></div>
    <dl class="clearfix">
      <dt><label for="nlp_AppID">AppID：</label></dt>
      <dd><input class="input-xlarge" type="text" name="nlp_AppID" id="nlp_AppID" value="{#$nlp_AppID#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="nlp_APIKey">API Key：</label></dt>
      <dd><input class="input-xlarge" type="text" name="nlp_APIKey" id="nlp_APIKey" value="{#$nlp_APIKey#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="nlp_Secret">Secret Key：</label></dt>
      <dd><input class="input-xlarge" type="text" name="nlp_Secret" id="nlp_Secret" value="{#$nlp_Secret#}" /></dd>
    </dl>
  </div>
  <div class="item hide">
    <div class="alert alert-success" style="margin:10px 10px 15px 50px;"><button type="button" class="close" data-dismiss="alert">×</button>使用聚合数据接口将更快更准确的获取手机号码归属地、IP地址信息<small>（如未配置，系统将使用默认接口，不影响正常使用，但不保证速度和准确性；）</small><br />配置常用快递信息后，商城订单发货后，可以查看快递运送记录！</div>
    <dl class="clearfix hide">
      <dt style="width: 250px;"><label for="juhe_express">聚合数据接口：</label></dt>
      <dd>
        <div class="input-prepend input-append">
          <span class="add-on">快递查询：</span>
          <input class="input-xlarge" type="text" name="juhe[express]" id="express" value="{#$juhe.express#}" />
          <span class="add-on"><a href="https://www.juhe.cn/docs/api/id/43" target="_blank">去申请</a></span>
        </div><br />
        <div class="input-prepend input-append">
          <span class="add-on">手机号码归属地：</span>
          <input class="input-xlarge" type="text" name="juhe[mobileAddr]" id="mobileAddr" value="{#$juhe.mobileAddr#}" />
          <span class="add-on"><a href="https://www.juhe.cn/docs/api/id/11" target="_blank">去申请</a></span>
        </div><br />
        <div class="input-prepend input-append">
          <span class="add-on">IP地址：</span>
          <input class="input-xlarge" type="text" name="juhe[ipAddr]" id="ipAddr" value="{#$juhe.ipAddr#}" />
          <span class="add-on"><a href="https://www.juhe.cn/docs/api/id/1" target="_blank">去申请</a></span>
        </div>
      </dd>
    </dl>
    <dl class="clearfix" style="margin-top: 20px;">
      <dt style="width: 250px;"><label for="juhe_aliyun">阿里云市场AppCode：</label></dt>
      <dd>
          <input class="input-xlarge" type="text" name="juhe[aliyun]" id="aliyun" value="{#$juhe.aliyun#}" />
          <a href="https://market.console.aliyun.com/imageconsole/index.htm" target="_blank"><small>AppCode查询地址</small></a>
          <div style="font-size: 12px; padding-top: 10px;">
            系统支持聚合数据和阿里云市场两个平台的接口，请根据实际情况选择使用！<br />
            使用以下服务时，请点击功能链接到阿里云市场购买相应接口！<br />
            <div style="margin-top: 10px;">
                <a href="https://market.aliyun.com/products/57126001/cmapi023201.html" target="_blank">快递查询</a><br />
                <a href="https://market.aliyun.com/products/57126001/cmapi022206.html" target="_blank">手机号码归属地查询</a><br />
                <a href="https://market.aliyun.com/products/57002003/cmapi021970.html" target="_blank">IP地址</a><br />
                <dl class="clearfix" >
                    <a href="https://market.aliyun.com/products/57000002/cmapi00037883.html" target="_blank" style="float: left;margin-top: 8px;">身份证识别</a>
                    <dd>
                    {#html_radios name="cardState" values=$cardState checked=$cardStateChecked output=$cardStateNames separator="&nbsp;&nbsp;"#}
                    </dd>
                </dl>
                <span class="help-inline" style="padding-left: 0; margin-top: -20px; font-size: 12px;">此功能可以识别用户实名认证时上传的身份证信息，以及验证姓名和身份证号码是否真实有效，帮助平台快速审核！</span>
                <dl class="clearfix" >
                  <a href="https://market.aliyun.com/products/57000002/cmapi029998.html" target="_blank" style="float: left;margin-top: 8px;">企业工商数据</a>
                  <dd>
                    {#html_radios name="enterpriseBusinessDataState" values=$enterpriseBusinessDataState checked=$getEnterpriseBusinessDataChecked output=$getEnterpriseBusinessDataNames separator="&nbsp;&nbsp;"#}
                </dl>
                <span class="help-inline" style="padding-left: 0; margin-top: -20px; font-size: 12px;">此功能可以识别企业工商信息！</span>
            </div>
          </div>
      </dd>
    </dl>
    <dl class="clearfix" style="margin-top: 20px;">
      <dt style="width: 250px;"><label for="juhe_exp_time">快递数据缓存时间：</label></dt>
      <dd>
          <input class="input-mini" type="number" name="juhe[exp_time]" id="exp_time" value="{#$juhe.exp_time#}" /> 分钟
          <span class="help-inline" style="font-size: 12px;">接口存在请求次数扣费问题, 设置缓存时间后在指定时间内只读取缓存并不调用接口(数据可能会延迟)，节省开支！</span>
      </dd>
    </dl>
    <dl class="clearfix" style=" margin-left: 70px; margin-top: 20px;">
      <dt><label>收货地址自动识别：</label></dt>
      <dd>
        <div class="input-prepend">
          <span class="add-on">AppID：</span>
          <input class="input-xlarge" type="text" name="juhe[addressAppid]" id="juhe_addressAppid" value="{#$juhe.addressAppid#}" />
        </div><br />
        <div class="input-prepend">
          <span class="add-on">API Key：</span>
          <input class="input-xlarge" type="text" name="juhe[addressApikey]" id="addressApikey" value="{#$juhe.addressApikey#}" />
        </div><br />
        <div class="input-prepend">
          <span class="add-on">Secret Key：</span>
          <input class="input-xlarge" type="text" name="juhe[addressSecretkey]" id="addressSecretkey" value="{#$juhe.addressSecretkey#}" />
        </div>
        <a href="https://help.kumanyun.com/help-68-751.html" target="_blank">配置教程</a>
      </dd>
    </dl>
  </div>
  <div class="item hide">
    <div class="alert alert-success" style="margin:10px 10px 15px 50px;"><button type="button" class="close" data-dismiss="alert">×</button>功能点：会员好友聊天、直播室聊天。<br />密钥获取地址：<a href="https://www.kumanyun.com/my/accesskey.html" target="_blank">https://www.kumanyun.com/my/accesskey.html</a></div>
    <dl class="clearfix">
      <dt><label for="km_accesskey_id">AccessKey ID：</label></dt>
      <dd><input class="input-xlarge" type="text" name="km_accesskey_id" id="km_accesskey_id" value="{#$km_accesskey_id#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label for="km_accesskey_secret">AccessKey Secret：</label></dt>
      <dd><input class="input-xlarge" type="text" name="km_accesskey_secret" id="km_accesskey_secret" value="{#$km_accesskey_secret#}" /></dd>
    </dl>
  </div>
  <div class="item hide">
    <div class="alert alert-success" style="margin:10px 10px 15px 50px;"><button type="button" class="close" data-dismiss="alert">×</button>此处为平台客服功能，可以接入第三方客服系统使用，如果不需要此功能，链接地址留空即可，前台页面将不显示客服系统按钮！<br />如需使用和官网演示站一样的客服系统，请联系酷曼云官方客服咨询购买！</div>
    <dl class="clearfix">
      <dt><label for="kefu_pc_url">电脑端链接地址：</label></dt>
      <dd><input class="input-xxlarge" type="text" name="kefu_pc_url" id="kefu_pc_url" value="{#$kefu_pc_url#}" /></dd>
    </dl>
	<dl class="clearfix">
      <dt><label for="kefu_touch_url">移动端链接地址：</label></dt>
      <dd><input class="input-xxlarge" type="text" name="kefu_touch_url" id="kefu_touch_url" value="{#$kefu_touch_url#}" /></dd>
    </dl>
    <dl class="clearfix">
      <dt><label>是否支持小程序端打开：</label></dt>
      <dd>
		  {#html_radios name="kefuMiniProgram" values=$kefuMiniProgram checked=$kefuMiniProgramChecked output=$kefuMiniProgramNames separator="&nbsp;&nbsp;"#}
		  <span class="input-tips" style="display: inline-block;"><s></s>需要在小程序开发设置中，添加业务域名，此操作需要第三方客服系统配合，操作错误将导致小程序端无法正常使用客服系统！</span>
	  </dd>
    </dl>
  </div>
  <dl class="clearfix formbtn">
    <dt>&nbsp;</dt>
    <dd><input class="btn btn-large btn-success" type="submit" name="submit" id="btnSubmit" value="确认提交" /></dd>
  </dl>
</form>

{#$editorFile#}
{#$jsFile#}
</body>
</html>
