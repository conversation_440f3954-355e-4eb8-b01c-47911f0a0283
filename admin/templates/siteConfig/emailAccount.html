<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset={#$cfg_soft_lang#}" />
<title>邮箱账号管理</title>
{#$cssFile#}
<script>
var adminPath = "{#$adminPath#}";
</script>
</head>

<body>
<form action="" method="post" name="editform" id="editform" class="editform">
  <input type="hidden" name="token" id="token" value="{#$token#}" />

  <div class="mail-list clearfix">
    {#$mailItem#}
  </div>
  <input class="btn btn-large btn-success" type="button" name="button" id="addMail" style="margin-left:20px;" value="新增一个帐户">
  <a href="javascript:;" id="checkMail" style="margin-left:20px;{#if $mailItem == ""#} display: none;{#/if#}">测试启用帐号是否可用</a>
  <script id="mailForm" type="text/html">
    <form action="" class="quick-editForm" name="mailForm">
      <dl class="clearfix">
        <dt>SMTP 服务器：</dt>
        <dd><input class="input-xlarge" type="text" name="mailServer" id="mailServer" value="smtp." /></dd>
      </dl>
      <dl class="clearfix">
        <dt>服务器端口：</dt>
        <dd><input style="width: 30px;" class="input-mini" type="text" name="mailPort" id="mailPort" value="465" />&nbsp;<code>一般使用465端口,配置错误将导致系统卡死！</code></dd>
      </dl>
      <dl class="clearfix">
        <dt>发信人地址：</dt>
        <dd><input class="input-xlarge" type="text" name="mailFrom" id="mailFrom" value="" /></dd>
      </dl>
      <dl class="clearfix">
        <dt>用户名：</dt>
        <dd><input class="input-large" type="text" name="mailUser" id="mailUser" value="" /></dd>
      </dl>
      <dl class="clearfix">
        <dt>密码/授权码：</dt>
        <dd><input class="input-large" type="password" name="mailPass" id="mailPass" value="" /></dd>
      </dl>
    </form>
  </script>
</form>

{#$jsFile#}
</body>
</html>
