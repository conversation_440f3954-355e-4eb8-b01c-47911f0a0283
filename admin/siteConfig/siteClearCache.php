<?php
/**
 * 清除页面缓存
 *
 * @version        $Id: siteClearCache.php 2014-3-19 上午10:23:13 $
 * @package        HuoNiao.Config
 * @copyright      Copyright (c) 2013 - 2018, HuoNiao, Inc.
 * @link           https://www.ihuoniao.cn/
 */
define('HUONIAOADMIN', "..");
require_once(dirname(__FILE__)."/../inc/config.inc.php");
checkPurview("siteClearCache");
$dsql = new dsql($dbo);
$tpl  = dirname(__FILE__)."/../templates/siteConfig";
$huoniaoTag->template_dir = $tpl; //设置后台模板目录
$templates = "siteClearCache.html";

if($action == "do"){

    if(empty($module)){
        $module = array();
    }
    //模板缓存
	if(count($module) > 0){
		foreach($module as $m){
			$admin = $type ? 1 : 0;
			clear_all_files($m, $admin);
		}
	}

	//生成新的静态资源版本号为当前时间
    if($static) {
        $m_file = HUONIAODATA . "/admin/staticVersion.txt";
        $fp = fopen($m_file, "w");
        fwrite($fp, time());
        fclose($fp);

        array_unshift($module, '静态资源文件');
    }

    //删除操作日志
    if($staticlog){
        $filelog = HUONIAOROOT . "/log";
        $fplog = opendir($filelog);
        while ($file=readdir($fplog)) {
            if($file!="." && $file!="..") {
                $fullpath=$filelog."/".$file;
                if(is_dir($fullpath)) {
                    deldir($fullpath);
                }
            }
        }

        array_unshift($module, '系统日志');
    }

	//纯静态页面
    if($staticPath) {
        $dir = HUONIAOROOT . '/templates_c/html/';
        unlinkDir($dir);

        array_unshift($module, '纯静态页面');
    }


    //内存缓存
    if($memory == 'redis' && $HN_memory->enable){
        $HN_memory->clear();
        
        array_unshift($module, 'redis');
    }


    //生成多语言静态文件
    $lang_dir = HUONIAOINC . '/lang/';
    $floders = listDir($lang_dir);
    if(!empty($floders)){
        $i = 0;
        $landataname = '';
        foreach($floders as $key => $floder){

            $config = $lang_dir.'/'.$floder.'/config.xml';
            if(file_exists($config)){

                $langDataCache = array();
                $lang_path = $lang_dir.'/'.$floder.'/';
                $lang_dir_ = opendir($lang_path);
                while (($file = readdir($lang_dir_)) !== false) {
                    $sName = str_replace(".inc.php", "", $file);
                    if ($file == '.' || $file == '..' || $file == 'config.xml') {
                        continue;
                    } else {

                        if($cfg_lang==$floder){
                            $landataname .= $file.',';
                        }

                        $sub_dir = $lang_path . $file;
                        if (file_exists($sub_dir)) {
                            include($sub_dir);
                            $langDataCache[$sName] = $lang;
                        }
                    }
                }

                $content = 'var langData =  ' . json_encode($langDataCache);


                PutFile($lang_dir.$floder . '.js', $content);
                PutFile($lang_dir.'siteConfiglangname' . '.txt', $landataname);
            }

        }
    }


    //APP多语言
    $lang_dir = HUONIAOINC . '/lang/app/';
    $lang_dir_ = opendir($lang_dir);
    while (($file = readdir($lang_dir_)) !== false) {
        $langDataCache = array();
        $sName = str_replace(".php", "", $file);
        if ($file == '.' || $file == '..' || $file == 'config.xml' || strstr($file, '.js')) {
            continue;
        } else {
            $sub_dir = $lang_dir . $file;
            if (file_exists($sub_dir)) {
                include($sub_dir);
                $langDataCache = $lang;
            }
        }

        $content = json_encode($langDataCache);
        PutFile($lang_dir.$sName . '.js', $content);
    }

    updateAppConfig();  //更新APP配置文件


	adminLog("清除页面缓存", join(",", $module));
	ShowMsg("页面缓存已经清除成功。", "siteClearCache.php");
	die;
}

//验证模板文件
if(file_exists($tpl."/".$templates)){

	//js
	$jsFile = array(
		'admin/siteConfig/siteClearCache.js'
	);
	$huoniaoTag->assign('jsFile', includeFile('js', $jsFile));

	$huoniaoTag->assign('action', $action);

	if($HN_memory->enable){
        $huoniaoTag->assign('redis', 1);
    }

	$huoniaoTag->assign('moduleList', getModuleList());
	$huoniaoTag->compile_dir = HUONIAOROOT."/templates_c/admin/siteConfig";  //设置编译目录
	$huoniaoTag->display($templates);
}else{
	echo $templates."模板文件未找到！";
}
