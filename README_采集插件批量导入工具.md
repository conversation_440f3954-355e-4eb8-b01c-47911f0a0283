# 🔥 火鸟门户系统采集插件批量导入工具

> **基于官方文档的完整批量导入解决方案** 📊  
> **版本**: 1.0  
> **更新时间**: 2025-06-30  
> **测试状态**: ✅ 已验证可用

---

## 📋 **工具概览**

本工具包提供了三种不同复杂度的批量导入方案，满足不同场景的需求：

### 🚀 **方案一：SQL直接批量插入**
- **文件**: `batch_insert_collection.sql`
- **适用场景**: 快速测试、简单部署
- **优点**: 执行速度快，无需编程知识
- **缺点**: 缺少错误处理和验证
- **⚠️ 注意**: 需要手动修改表前缀 (`hn_` 或 `huoniao_`)

### ⚡ **方案二：PHP脚本批量导入**
- **文件**: `batch_import_collection_rules.php`
- **适用场景**: 生产环境推荐使用
- **优点**: 完整的错误处理、事务支持、重复检查、自动检测表前缀和插件ID
- **缺点**: 需要PHP环境

### 🎛️ **方案三：JSON配置文件管理**
- **配置文件**: `collection_rules_config.json`
- **导入脚本**: `import_from_json.php`
- **适用场景**: 企业级应用、配置管理
- **优点**: 配置与代码分离、易于维护和扩展
- **缺点**: 相对复杂

### 🕐 **方案四：独立定时任务脚本**
- **文件**: `collection_cron_runner.php`
- **适用场景**: 宝塔面板计划任务、独立cron调用
- **优点**: 独立执行、支持指定节点、完整日志输出
- **用法**: `php collection_cron_runner.php [节点ID]`

---

## 🎯 **快速开始**

### 📋 **前置条件**
1. ✅ 火鸟门户系统已安装
2. ✅ 信息资讯采集插件已安装并启用 (ID通常为4，但可能被修改)
3. ✅ 数据库访问权限
4. ✅ PHP环境 (方案二、三、四需要)
5. ✅ 数据库字符集设置为 `utf8mb4` (推荐)

### ⚙️ **重要配置检查**

#### 🔍 **1. 表前缀检查**
```sql
-- 检查您的实际表前缀
SHOW TABLES LIKE '%site_plugins';
-- 常见前缀: hn_ 或 huoniao_
```

#### 🔌 **2. 插件ID检查**
```sql
-- 查找采集插件的真实ID
SELECT pid, title, state FROM `hn_site_plugins` WHERE `title` LIKE '%采集%';
-- 或者 (如果使用huoniao_前缀)
SELECT pid, title, state FROM `huoniao_site_plugins` WHERE `title` LIKE '%采集%';
```

#### 📊 **3. 字符集检查**
```sql
-- 检查数据库字符集
SHOW VARIABLES LIKE 'character_set_database';
-- 推荐设置为 utf8mb4
```

#### 🎛️ **4. JSON批量导入按钮检查**
- **2021年后定制版**: 后台自带JSON批量导入按钮
- **标准版本**: 需要使用PHP脚本解析JSON文件
- **检查方法**: 登录后台 → 插件管理 → 采集插件 → 查看是否有"批量导入"按钮

### 🚀 **方案一：SQL直接导入**

```bash
# 1. 备份数据库
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行SQL脚本
mysql -u用户名 -p密码 数据库名 < batch_insert_collection.sql

# 3. 验证结果
mysql -u用户名 -p密码 数据库名 -e "SELECT COUNT(*) FROM hn_collection_nodes;"
```

### ⚡ **方案二：PHP脚本导入**

```bash
# 1. 上传脚本到网站根目录
cp batch_import_collection_rules.php /path/to/website/

# 2. 执行脚本
cd /path/to/website/
php batch_import_collection_rules.php

# 3. 查看执行结果
# 脚本会显示详细的导入统计信息
```

### 🎛️ **方案三：JSON配置导入**

```bash
# 1. 上传配置文件和脚本
cp collection_rules_config.json /path/to/website/
cp import_from_json.php /path/to/website/

# 2. 执行导入
cd /path/to/website/
php import_from_json.php

# 3. 自定义配置
# 编辑 collection_rules_config.json 添加更多采集节点
```

### 🕐 **方案四：独立定时任务**

```bash
# 1. 上传定时任务脚本
cp collection_cron_runner.php /path/to/website/

# 2. 手动执行测试
cd /path/to/website/
php collection_cron_runner.php

# 3. 指定节点执行
php collection_cron_runner.php 1  # 执行节点ID为1的采集任务

# 4. 宝塔面板计划任务设置
# 任务类型: Shell脚本
# 执行周期: */30 * * * * (每30分钟)
# 脚本内容: cd /www/wwwroot/your_domain && php collection_cron_runner.php
```

---

## 📊 **预设采集节点**

工具包预设了以下采集节点，基于官方文档格式设计：

| 节点名称 | 类型 | 目标网站 | 说明 |
|---------|------|---------|------|
| 新浪新闻科技 | html | tech.sina.com.cn | 新浪科技频道 |
| 网易新闻科技 | html | tech.163.com | 网易科技频道 |
| 腾讯新闻科技 | interface | news.qq.com | API接口采集 |
| 搜狐新闻科技 | multiple | it.sohu.com | 多页面采集 |
| 凤凰新闻科技 | html | tech.ifeng.com | 凤凰科技频道 |
| 火鸟门户测试 | html | ihuoniao.cn | 基于官方文档示例 |

每个节点包含完整的采集规则：
- ✅ 标题提取规则
- ✅ 正文提取规则  
- ✅ 作者提取规则
- ✅ 来源提取规则
- ✅ 时间提取规则

---

## 🔧 **自定义配置**

### 📝 **添加新的采集节点**

#### 方案一：直接修改SQL
```sql
INSERT INTO `hn_collection_nodes` 
(`name`, `target_type`, `list_url`, `url_pattern`, `start_mark`, `end_mark`, `status`, `created_time`) 
VALUES ('您的网站名称', 'html', '列表页URL', 'URL匹配规则', '开始标记', '结束标记', 1, UNIX_TIMESTAMP());
```

#### 方案二：修改PHP数组
```php
$collection_configs[] = [
    'name' => '您的网站名称',
    'target_type' => 'html',
    'list_url' => '列表页URL',
    'url_pattern' => 'URL匹配规则(*)',
    'start_mark' => '开始标记',
    'end_mark' => '结束标记',
    'rules' => [
        // 添加提取规则
    ]
];
```

#### 方案三：修改JSON配置
```json
{
  "name": "您的网站名称",
  "target_type": "html",
  "list_url": "列表页URL",
  "url_pattern": "URL匹配规则(*)",
  "rules": [
    {
      "field_type": "title",
      "start_mark": "标题开始标记",
      "end_mark": "标题结束标记",
      "required": true
    }
  ]
}
```

---

## 🔍 **验证和测试**

### 📊 **检查导入结果**

```sql
-- 查看采集节点
SELECT id, name, target_type, list_url, status FROM `hn_collection_nodes` ORDER BY `id` DESC;

-- 查看采集规则
SELECT r.id, r.node_id, n.name as node_name, r.field_type, r.is_required 
FROM `hn_collection_rules` r 
LEFT JOIN `hn_collection_nodes` n ON r.node_id = n.id 
ORDER BY r.node_id, r.sort_order;

-- 统计信息
SELECT 
    (SELECT COUNT(*) FROM `hn_collection_nodes`) as total_nodes,
    (SELECT COUNT(*) FROM `hn_collection_rules`) as total_rules,
    (SELECT COUNT(*) FROM `hn_collection_nodes` WHERE status = 1) as active_nodes;
```

### 🎯 **后台管理测试**

1. 登录后台管理界面
2. 进入 **插件管理** → **火鸟采集**
3. 查看新增的采集节点
4. 选择一个节点执行测试采集
5. 检查采集结果和日志

---

## ⚠️ **注意事项**

### 🔒 **安全建议**
- ✅ 导入前务必备份数据库
- ✅ 先在测试环境验证
- ✅ 检查数据库表前缀是否正确
- ✅ 确保有足够的数据库权限

### 🎛️ **配置建议**
- ✅ 根据目标网站调整CSS选择器
- ✅ 设置合理的采集间隔时间 (建议30分钟以上)
- ✅ 配置URL过滤规则避免重复采集
- ✅ 定期检查采集成功率
- ✅ 读取后台真实插件ID，不要硬编码为4
- ✅ 检查是否有JSON批量导入按钮 (2021年后定制版才有)

### 🚀 **性能优化**
- ✅ 批量导入时使用事务
- ✅ 设置合理的采集频率，避免过于频繁
- ✅ 监控服务器资源使用
- ✅ 定期清理无效的采集规则
- ✅ 使用宝塔计划任务直接调用 `cron.php`
- ✅ 节点较多时按节点ID轮巡，减少并发

### 🔧 **定时任务优化**
- ✅ **推荐方式**: 宝塔计划任务直接调用
  ```bash
  cd /www/wwwroot/your_domain && php /include/plugins/4/cron.php
  ```
- ✅ **独立脚本**: 使用 `collection_cron_runner.php` 按节点轮巡
- ✅ **执行频率**: 建议每30分钟执行一次
- ✅ **并发控制**: 避免多个采集任务同时运行

---

## 📚 **相关文档**

- **[火鸟门户系统-采集插件批量写入指南](./docs/火鸟门户系统-采集插件批量写入指南.md)** - 完整技术指南
- **[火鸟门户系统-采集插件实际批量写入方案](./docs/火鸟门户系统-采集插件实际批量写入方案.md)** - 实用方案说明
- **[📚 文档总索引](./docs/📚%20文档总索引.md)** - 所有文档索引

---

## 🆘 **故障排除**

### ❌ **常见错误**

1. **插件未安装**
   ```
   错误: 信息资讯采集插件未安装
   解决: 先安装并启用采集插件，检查真实插件ID
   ```

2. **数据表不存在**
   ```
   错误: Table 'hn_collection_nodes' doesn't exist
   解决: 检查表前缀 (hn_ vs huoniao_)，或运行SQL创建表语句
   ```

3. **权限不足**
   ```
   错误: Access denied for user
   解决: 检查数据库用户权限，确保有CREATE、INSERT权限
   ```

4. **字符集错误**
   ```
   错误: Incorrect string value for emoji
   解决: 设置数据库字符集为utf8mb4，脚本头部设置charset=utf-8
   ```

5. **插件ID错误**
   ```
   错误: 插件目录不存在 /include/plugins/4/
   解决: 查询数据库获取真实插件ID，不要硬编码为4
   ```

6. **JSON导入按钮缺失**
   ```
   问题: 后台没有JSON批量导入按钮
   解决: 只有2021年后定制版才有，标准版需要用PHP脚本解析JSON
   ```

7. **重复节点**
   ```
   警告: 节点已存在，跳过
   解决: 正常现象，脚本会自动跳过重复节点
   ```

### 🔧 **调试方法**

1. **启用PHP错误显示**
   ```php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

2. **检查数据库连接**
   ```php
   var_dump($dsql->isConnected());
   ```

3. **查看详细日志**
   ```bash
   tail -f /var/log/apache2/error.log
   ```

---

**🔄 最后更新**: 2025-06-30  
**📧 技术支持**: 参考官方文档或提交Issue
