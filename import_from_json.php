<?php
/**
 * 火鸟门户系统采集插件JSON配置导入脚本
 * 基于官方文档和JSON配置文件设计
 * 
 * @version 1.0
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */

// 引入系统核心文件
define('HUONIAOINC', dirname(__FILE__) . '/include');
require_once(HUONIAOINC . '/common.inc.php');

// 初始化数据库连接
$dsql = new dsql($dbo);

echo "🔥 火鸟门户系统采集插件JSON配置导入工具\n";
echo "==========================================\n\n";

// 检查JSON配置文件
$json_file = dirname(__FILE__) . '/collection_rules_config.json';
if (!file_exists($json_file)) {
    die("❌ 错误: JSON配置文件不存在: $json_file\n");
}

// 读取JSON配置
$json_content = file_get_contents($json_file);
$config_data = json_decode($json_content, true);

if (!$config_data) {
    die("❌ 错误: JSON配置文件格式错误或无法解析\n");
}

if (!isset($config_data['collection_batch_import']['nodes'])) {
    die("❌ 错误: JSON配置文件缺少nodes节点\n");
}

$batch_config = $config_data['collection_batch_import'];
echo "📋 配置信息:\n";
echo "   版本: {$batch_config['version']}\n";
echo "   描述: {$batch_config['description']}\n";
echo "   节点数量: {$batch_config['total_nodes']}\n";
echo "   创建时间: {$batch_config['created_time']}\n\n";

// 检查插件是否安装
$plugin_check = $dsql->SetQuery("SELECT * FROM `#@__site_plugins` WHERE `pid` = 4");
$plugin_result = $dsql->dsqlOper($plugin_check, "results");

if (!$plugin_result) {
    die("❌ 错误: 信息资讯采集插件(ID:4)未安装，请先安装插件！\n");
}

if ($plugin_result[0]['state'] != 1) {
    die("❌ 错误: 信息资讯采集插件未启用，请先启用插件！\n");
}

echo "✅ 插件检查通过: {$plugin_result[0]['title']}\n\n";

// 执行批量导入
$nodes = $batch_config['nodes'];
$success_count = 0;
$error_count = 0;
$skip_count = 0;

echo "🚀 开始从JSON配置导入采集节点...\n\n";

foreach ($nodes as $index => $node) {
    echo "📝 正在处理: {$node['name']} (" . ($index + 1) . "/" . count($nodes) . ")\n";
    echo "   类型: {$node['target_type']} | 规则数: " . count($node['rules']) . "\n";
    
    try {
        // 开始事务
        $dsql->ExecuteNoneQuery("START TRANSACTION");
        
        // 检查是否已存在同名节点
        $check_sql = "SELECT id FROM `#@__collection_nodes` WHERE `name` = '" . addslashes($node['name']) . "'";
        $check_result = $dsql->dsqlOper($dsql->SetQuery($check_sql), "results");
        
        if ($check_result) {
            echo "⚠️  节点已存在，跳过: {$node['name']}\n\n";
            $dsql->ExecuteNoneQuery("ROLLBACK");
            $skip_count++;
            continue;
        }
        
        // 准备节点数据
        $node_data = [
            'name' => addslashes($node['name']),
            'description' => addslashes($node['description'] ?? ''),
            'target_type' => $node['target_type'],
            'list_url' => addslashes($node['list_url']),
            'url_pattern' => addslashes($node['url_pattern']),
            'url_include' => addslashes($node['url_include'] ?? ''),
            'url_exclude' => addslashes($node['url_exclude'] ?? ''),
            'start_mark' => addslashes($node['start_mark']),
            'end_mark' => addslashes($node['end_mark']),
            'encoding' => $node['encoding'] ?? 'utf-8',
            'interval' => $node['interval'] ?? 60,
            'max_pages' => $node['max_pages'] ?? 10
        ];
        
        // 插入采集节点
        $node_sql = "INSERT INTO `#@__collection_nodes` 
                     (`name`, `description`, `target_type`, `list_url`, `url_pattern`, `url_include`, `url_exclude`, 
                      `start_mark`, `end_mark`, `encoding`, `interval_time`, `max_pages`, `status`, `created_time`) 
                     VALUES ('{$node_data['name']}', '{$node_data['description']}', '{$node_data['target_type']}', 
                            '{$node_data['list_url']}', '{$node_data['url_pattern']}', '{$node_data['url_include']}', 
                            '{$node_data['url_exclude']}', '{$node_data['start_mark']}', '{$node_data['end_mark']}', 
                            '{$node_data['encoding']}', {$node_data['interval']}, {$node_data['max_pages']}, 1, " . time() . ")";
        
        $dsql->ExecuteNoneQuery($node_sql);
        $node_id = $dsql->GetLastID();
        
        if (!$node_id) {
            throw new Exception("插入采集节点失败");
        }
        
        // 插入采集规则
        $rule_count = 0;
        foreach ($node['rules'] as $rule) {
            $is_required = $rule['required'] ? 1 : 0;
            
            $rule_sql = "INSERT INTO `#@__collection_rules` 
                         (`node_id`, `field_type`, `field_label`, `start_mark`, `end_mark`, `is_required`, `sort_order`) 
                         VALUES ($node_id, '{$rule['field_type']}', '" . addslashes($rule['field_label'] ?? '') . "',
                                '" . addslashes($rule['start_mark']) . "', '" . addslashes($rule['end_mark']) . "', 
                                $is_required, {$rule['sort_order']})";
            
            $dsql->ExecuteNoneQuery($rule_sql);
            $rule_count++;
        }
        
        // 提交事务
        $dsql->ExecuteNoneQuery("COMMIT");
        
        echo "✅ 成功导入: {$node['name']}\n";
        echo "   节点ID: $node_id | 规则数: $rule_count\n";
        echo "   URL: {$node['list_url']}\n\n";
        $success_count++;
        
    } catch (Exception $e) {
        // 回滚事务
        $dsql->ExecuteNoneQuery("ROLLBACK");
        echo "❌ 导入失败: {$node['name']} - {$e->getMessage()}\n\n";
        $error_count++;
    }
}

echo "==========================================\n";
echo "📊 JSON配置导入完成统计:\n";
echo "✅ 成功导入: $success_count 个采集节点\n";
echo "⚠️  跳过重复: $skip_count 个采集节点\n";
echo "❌ 导入失败: $error_count 个采集节点\n";
echo "🎯 总计处理: " . ($success_count + $skip_count + $error_count) . " 个采集节点\n\n";

if ($success_count > 0) {
    echo "🎉 导入成功！您现在可以:\n";
    echo "1. 登录后台管理界面\n";
    echo "2. 进入 插件管理 → 火鸟采集\n";
    echo "3. 查看新增的采集节点\n";
    echo "4. 配置采集参数并执行测试\n\n";
    
    echo "📋 验证SQL命令:\n";
    echo "-- 查看导入的采集节点\n";
    echo "SELECT id, name, target_type, list_url FROM `#@__collection_nodes` ORDER BY `id` DESC LIMIT 10;\n\n";
    echo "-- 查看导入的采集规则\n";
    echo "SELECT r.*, n.name as node_name FROM `#@__collection_rules` r \n";
    echo "LEFT JOIN `#@__collection_nodes` n ON r.node_id = n.id \n";
    echo "ORDER BY r.id DESC LIMIT 20;\n\n";
    
    echo "🔧 后台访问路径:\n";
    echo "管理后台 → 插件管理 → 信息资讯采集插件 → 采集节点管理\n";
}

echo "🔄 JSON配置导入脚本执行完成！\n";
?>
