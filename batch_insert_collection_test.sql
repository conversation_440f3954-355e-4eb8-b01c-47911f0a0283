-- 火鸟门户系统采集插件批量插入SQL脚本
-- 基于官方文档和实际测试设计
-- 版本: 1.1 (优化版)
-- 日期: 2025-06-30
-- 更新: 支持动态表前缀、utf8mb4字符集、插件ID检测

-- ============================================
-- 重要说明
-- ============================================
-- 1. 请根据实际情况修改表前缀 (hn_ 或 huoniao_)
-- 2. 请根据实际情况修改插件ID (通常为4，但可能被修改)
-- 3. 建议先在测试环境执行
-- 4. 执行前请备份数据库

-- ============================================
-- 1. 检查插件是否安装和启用
-- ============================================
SELECT '检查插件状态...' as status;

-- 检查hn_前缀的插件表
SELECT pid, title, state FROM `huoniao_site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%';

-- 如果上面查询无结果，请尝试huoniao_前缀
-- SELECT pid, title, state FROM `huoniao_site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%';

-- ============================================
-- 2. 创建采集节点表 (如果不存在)
-- ============================================
-- 注意: 请根据实际表前缀修改表名 (hn_ 或 huoniao_)

CREATE TABLE IF NOT EXISTS `huoniao_collection_nodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `description` text COMMENT '节点描述',
  `target_type` varchar(50) DEFAULT 'html' COMMENT '针对类型: html/interface/multiple',
  `list_url` text COMMENT '列表页URL',
  `url_pattern` varchar(500) COMMENT 'URL匹配规则',
  `url_include` varchar(500) COMMENT 'URL包含规则',
  `url_exclude` varchar(500) COMMENT 'URL排除规则',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `encoding` varchar(20) DEFAULT 'utf-8' COMMENT '页面编码',
  `interval_time` int(11) DEFAULT 60 COMMENT '采集间隔(秒)',
  `max_pages` int(11) DEFAULT 10 COMMENT '最大采集页数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `last_run_time` int(11) COMMENT '最后运行时间',
  `total_collected` int(11) DEFAULT 0 COMMENT '总采集数量',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集节点配置表';

-- 如果使用huoniao_前缀，请使用以下语句替代上面的CREATE TABLE:
-- CREATE TABLE IF NOT EXISTS `huoniao_collection_nodes` (...同样的结构...);

-- ============================================
-- 3. 创建采集规则表 (如果不存在)
-- ============================================
CREATE TABLE IF NOT EXISTS `huoniao_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型: title/content/author/source/time',
  `field_label` varchar(100) COMMENT '字段标签',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必需',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` int(11) COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_field_type` (`field_type`),
  FOREIGN KEY (`node_id`) REFERENCES `huoniao_collection_nodes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集规则配置表';

-- 如果使用huoniao_前缀，请使用以下语句替代上面的CREATE TABLE:
-- CREATE TABLE IF NOT EXISTS `huoniao_collection_rules` (...同样的结构...);

-- ============================================
-- 4. 批量插入采集节点
-- ============================================
SELECT '开始插入采集节点...' as status;

INSERT INTO `huoniao_collection_nodes` 
(`name`, `description`, `target_type`, `list_url`, `url_pattern`, `url_include`, `url_exclude`, 
 `start_mark`, `end_mark`, `encoding`, `interval_time`, `max_pages`, `status`, `created_time`) VALUES

-- 新浪新闻科技
('新浪新闻科技', '新浪科技频道新闻采集', 'html', 'https://tech.sina.com.cn/', 
 'https://tech.sina.com.cn/(*)', 'tech.sina.com.cn', '.jpg,.png,.gif', 
 '<div class="feed-card-item">', '<div class="pagination">', 'utf-8', 60, 10, 1, UNIX_TIMESTAMP()),

-- 网易新闻科技
('网易新闻科技', '网易科技频道新闻采集', 'html', 'https://tech.163.com/', 
 'https://tech.163.com/(*)', 'tech.163.com', '', 
 '<div class="n-list">', '<div class="pagination">', 'utf-8', 60, 10, 1, UNIX_TIMESTAMP()),

-- 腾讯新闻科技 (接口类型)
('腾讯新闻科技', '腾讯科技频道API接口采集', 'interface', 'https://news.qq.com/api/tech.js', 
 'https://news.qq.com/api/tech_(*).js', 'news.qq.com', '', 
 'datacallback(', ');', 'utf-8', 30, 5, 1, UNIX_TIMESTAMP()),

-- 搜狐新闻科技 (多页面类型)
('搜狐新闻科技', '搜狐IT频道多页面采集', 'multiple', 'https://it.sohu.com/', 
 'https://it.sohu.com/page_(*).html', 'it.sohu.com', '', 
 '<div class="news-item">', '<div class="page-box">', 'utf-8', 90, 15, 1, UNIX_TIMESTAMP()),

-- 凤凰新闻科技
('凤凰新闻科技', '凤凰科技频道新闻采集', 'html', 'https://tech.ifeng.com/', 
 'https://tech.ifeng.com/(*)', 'tech.ifeng.com', '.jpg,.png,.gif', 
 '<div class="news_list">', '<div class="page_nav">', 'utf-8', 120, 8, 1, UNIX_TIMESTAMP()),

-- 火鸟门户测试 (基于官方文档)
('火鸟门户测试', '基于官方文档的火鸟门户测试采集', 'html', 'https://ihuoniao.cn/sz/article/yule/', 
 'https://ihuoniao.cn/sz/article/yule/?page=(*)', 'article', '.jpg', 
 '<div class="n-list">', '<div class="pagination">', 'utf-8', 60, 5, 1, UNIX_TIMESTAMP());

-- ============================================
-- 5. 批量插入采集规则
-- ============================================
SELECT '开始插入采集规则...' as status;

INSERT INTO `huoniao_collection_rules` 
(`node_id`, `field_type`, `field_label`, `start_mark`, `end_mark`, `is_required`, `sort_order`) VALUES

-- 新浪新闻规则 (节点ID=1)
(1, 'title', '文章标题', '<h1>', '</h1>', 1, 1),
(1, 'content', '文章正文', '<div class="article">', '</div>', 1, 2),
(1, 'author', '文章作者', '<span class="author">', '</span>', 0, 3),
(1, 'source', '文章来源', '来源：', '&nbsp;', 0, 4),
(1, 'time', '发布时间', '<span class="time">', '</span>', 0, 5),

-- 网易新闻规则 (节点ID=2)
(2, 'title', '文章标题', '<h1 class="post_title">', '</h1>', 1, 1),
(2, 'content', '文章正文', '<div class="post_body">', '</div>', 1, 2),
(2, 'author', '文章作者', '<div class="ep-editor">', '</div>', 0, 3),
(2, 'source', '文章来源', '<div class="ep-source">', '</div>', 0, 4),
(2, 'time', '发布时间', '<div class="post_time_source">', '</div>', 0, 5),

-- 腾讯新闻规则 (节点ID=3, 接口类型)
(3, 'title', '文章标题', '"title":"', '",', 1, 1),
(3, 'content', '文章摘要', '"digest":"', '",', 1, 2),
(3, 'source', '文章来源', '"source":"', '",', 0, 3),
(3, 'time', '发布时间', '"time":"', '",', 0, 4),

-- 搜狐新闻规则 (节点ID=4)
(4, 'title', '文章标题', '<h4><a', '</a></h4>', 1, 1),
(4, 'content', '文章正文', '<div class="content">', '</div>', 1, 2),
(4, 'source', '文章来源', '<span class="source">', '</span>', 0, 3),
(4, 'time', '发布时间', '<span class="time">', '</span>', 0, 4),

-- 凤凰新闻规则 (节点ID=5)
(5, 'title', '文章标题', '<h3><a', '</a></h3>', 1, 1),
(5, 'content', '文章正文', '<div class="main_content">', '</div>', 1, 2),
(5, 'source', '文章来源', '<span class="ss01">', '</span>', 0, 3),
(5, 'time', '发布时间', '<span class="ss03">', '</span>', 0, 4),

-- 火鸟门户测试规则 (节点ID=6, 基于官方文档)
(6, 'title', '文章标题', '<div class="wmain">', '&nbsp;&nbsp;作者：', 1, 1),
(6, 'content', '文章正文', '<div class="wmain">', '<div class="rewardS">', 1, 2),
(6, 'source', '文章来源', '来源：', '&nbsp;&nbsp;作者：', 0, 3),
(6, 'author', '文章作者', '&nbsp;&nbsp;作者：', '&nbsp;&nbsp;浏览', 0, 4),
(6, 'time', '发布时间', '<div class="tit-bar fn-clear">', '&nbsp;&nbsp;浏览', 0, 5);

-- ============================================
-- 6. 验证插入结果
-- ============================================
SELECT '验证插入结果...' as status;

-- 查看插入的采集节点
SELECT '采集节点列表:' as info;
SELECT id, name, target_type, list_url, status FROM `huoniao_collection_nodes` ORDER BY `id` DESC;

-- 查看插入的采集规则
SELECT '采集规则列表:' as info;
SELECT r.id, r.node_id, n.name as node_name, r.field_type, r.field_label, r.is_required 
FROM `huoniao_collection_rules` r 
LEFT JOIN `huoniao_collection_nodes` n ON r.node_id = n.id 
ORDER BY r.node_id, r.sort_order;

-- 统计信息
SELECT '统计信息:' as info;
SELECT 
    (SELECT COUNT(*) FROM `huoniao_collection_nodes`) as total_nodes,
    (SELECT COUNT(*) FROM `huoniao_collection_rules`) as total_rules,
    (SELECT COUNT(*) FROM `huoniao_collection_nodes` WHERE status = 1) as active_nodes;

-- ============================================
-- 7. 完成提示
-- ============================================
SELECT '批量插入完成！' as status;
SELECT '请登录后台管理界面查看新增的采集节点' as next_step;
SELECT '后台路径: 插件管理 → 火鸟采集 → 采集节点管理' as admin_path;
