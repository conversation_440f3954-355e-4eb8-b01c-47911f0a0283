-- 测试环境初始化SQL脚本
-- 创建必要的表结构用于测试

-- 创建插件管理表
CREATE TABLE IF NOT EXISTS `huoniao_site_plugins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL COMMENT '插件ID',
  `title` varchar(255) NOT NULL COMMENT '插件名称',
  `version` varchar(50) COMMENT '版本号',
  `author` varchar(100) COMMENT '作者',
  `description` text COMMENT '描述',
  `state` tinyint(1) DEFAULT 1 COMMENT '状态',
  `pubdate` int(11) COMMENT '发布时间',
  `uptime` int(11) COMMENT '更新时间',
  `delsql` text COMMENT '卸载SQL',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入测试插件数据
INSERT INTO `huoniao_site_plugins` (`pid`, `title`, `version`, `author`, `description`, `state`, `pubdate`, `uptime`) 
VALUES (4, '信息资讯采集插件', 'v2.1', '火鸟团队', '自动采集新闻资讯内容', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 创建采集节点表
CREATE TABLE IF NOT EXISTS `huoniao_collection_nodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `description` text COMMENT '节点描述',
  `target_type` varchar(50) DEFAULT 'html' COMMENT '针对类型: html/interface/multiple',
  `list_url` text COMMENT '列表页URL',
  `url_pattern` varchar(500) COMMENT 'URL匹配规则',
  `url_include` varchar(500) COMMENT 'URL包含规则',
  `url_exclude` varchar(500) COMMENT 'URL排除规则',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `encoding` varchar(20) DEFAULT 'utf-8' COMMENT '页面编码',
  `interval_time` int(11) DEFAULT 60 COMMENT '采集间隔(秒)',
  `max_pages` int(11) DEFAULT 10 COMMENT '最大采集页数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `last_run_time` int(11) COMMENT '最后运行时间',
  `total_collected` int(11) DEFAULT 0 COMMENT '总采集数量',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集节点配置表';

-- 创建采集规则表
CREATE TABLE IF NOT EXISTS `huoniao_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型: title/content/author/source/time',
  `field_label` varchar(100) COMMENT '字段标签',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必需',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` int(11) COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_field_type` (`field_type`),
  FOREIGN KEY (`node_id`) REFERENCES `huoniao_collection_nodes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集规则配置表';

-- 插入测试采集节点
INSERT INTO `huoniao_collection_nodes` 
(`name`, `target_type`, `list_url`, `url_pattern`, `start_mark`, `end_mark`, `status`, `created_time`) 
VALUES 
('测试新闻节点', 'html', 'https://example.com/news/', 'https://example.com/news/(*)', '<div class="news-list">', '</div>', 1, UNIX_TIMESTAMP()),
('测试科技节点', 'html', 'https://tech.example.com/', 'https://tech.example.com/article/(*)', '<div class="article-list">', '</div>', 1, UNIX_TIMESTAMP());

-- 插入测试采集规则
INSERT INTO `huoniao_collection_rules` 
(`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`, `created_time`) 
VALUES 
(1, 'title', '<h1>', '</h1>', 1, 1, UNIX_TIMESTAMP()),
(1, 'content', '<div class="content">', '</div>', 1, 2, UNIX_TIMESTAMP()),
(1, 'author', '<span class="author">', '</span>', 0, 3, UNIX_TIMESTAMP()),
(2, 'title', '<h2>', '</h2>', 1, 1, UNIX_TIMESTAMP()),
(2, 'content', '<article>', '</article>', 1, 2, UNIX_TIMESTAMP());

SELECT '✅ 测试数据库初始化完成' as status;
SELECT COUNT(*) as node_count FROM `huoniao_collection_nodes`;
SELECT COUNT(*) as rule_count FROM `huoniao_collection_rules`;
