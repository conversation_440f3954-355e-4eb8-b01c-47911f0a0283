{"siteConfig": {"type": {"params": [], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/siteConfig.controller.php"}}, "member": {"record": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "point": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "bill": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "recordDetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "billDetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "consumeDetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "consumebill": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "publish": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "consume": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "connect": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter_contrast": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter_single": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "join_renew": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "join_upgrade": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "servicemeal": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter_pay": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "login": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "login_popup": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "sso": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "ssoUserRedirect": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "ssoUser": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "loginCheck": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "logout": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "register": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fpwd": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "registerCheck": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "registerCheck_v1": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "registerSuccess": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "registerVerifyEmail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "memberVerifyEmail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "getUserInfo": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "message": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "message_detail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "config_marry_hotel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabusuccess": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car_entrust": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car_receive_broker": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car-broker": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "manage": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "order": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "team": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "teamAdd": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "albums": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "albumsAdd": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "case": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "caseAdd": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "booking": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "post": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "collections": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "invitation": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "resume": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "house-broker": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "statistics": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "house_receive_broker": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "house_entrust": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "education-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "education-yuyue": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "logoff": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "info": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_worker_seek": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_post_seek": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_job_seek": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "config": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "category": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_modtype": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "logistic": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "memberList": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "addAccount": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "complain": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "refunddetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "refunddetail_shop_express": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "platformjoin": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "returngoods": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "refund": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "orderdetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "refunddetail_shop": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_changeprice": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "write-comment": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "job": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "index_job": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "post_detail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "manage_worker": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "renovation": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "waimai-menus": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "waimai-albums": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "waimai-albums-add": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "huodong-reg": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "dressup-website": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "huodong-join": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "bindMobile": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "version": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "upgrade": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "myhui": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "opendetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "upgrade-pay": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter-upload": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-config": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-config_custom": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-custom_menu": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter-review": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "promotion": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "extract": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "payment": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "module": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-service": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-service-setting": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-service-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-service-list": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-diancan-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-dingzuo-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-paidui-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-maidan-order": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "business-maidan-orderdetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_fans": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_follow": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_visitor": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_message": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_fabu": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "user_info": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "qiandao": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "verify-tuan": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "verify-shop": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "livedetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "livedetail_vurl": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "livedetail_lx": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "livedetail_menu": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_imgtext": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_prolist": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_userlist": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_comment": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_hongbao": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_gift": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_income": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_reward": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "live_charts": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "put_forward": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "joinPay": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "joinPayreturn": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "choose_address": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "appmanage": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "website-custom_nav": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter_house": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "pay": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "housem": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "enter_car": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car_enter": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "carappoint": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "adviser_car_add": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "car-config": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-nanny": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-personal": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-cancelservice": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-cancel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-service": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-repair": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-dispatch": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "talkhistory": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "refuserefund": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-courier": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-courierorder": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "homemaking-count": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao_join": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao_charts": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao_user": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao_commission": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fenxiao_commission_detail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "marry": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "marry-planmeal": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu-tarvel-strategy": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-strategy": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-fabu-travel-hotel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-ticket": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-video": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-rentcar": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-visa": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-agency": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_live_imgtext": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-cancelhotel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-cancelticket": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "travel-canceldetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "verify-travel": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "education": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "pension-award": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "pension-invitation": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_circle": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "commentdetail_shop": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "dating-album-add": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "renovation_zb_detail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "invite": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "withdraw": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "inviteWithdraw": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "inviteRegister": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "inviteGzh": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_shop_bargain": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_shop_tuan": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_shop_secKill": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabu_shop_qianggou": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "quanDetail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "fabuquan": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_huodong": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_hasjoin": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_type": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "daipay": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "daipay_return": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "supplier": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "addStaff": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "workPlatform": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop_branch_detail": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "index_shop": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "reward_fabu": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "logistics": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "shop": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "type": {"params": ["id", "userid", "preview", "adsid", "addressid", "from", "cardnum", "topicid", "topicname", "type", "tab"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.controller.php"}, "company": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "nanny-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "hotel-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "agency-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "strategy-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "rentcar-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "video-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "visa-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "company-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "designer-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "waimai": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "planmeal-detail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "business": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}, "blogdetail": {"params": ["type", "id", "state", "base64", "userData", "phoneData", "format"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/member.class.php"}}, "handlers": {}, "business": {"list": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "search-list2": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "storeDetail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "detail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "info": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "intro": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "news": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "newsd": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "albums": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "albumsd": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "panor": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "panord": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "video": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "videod": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "live": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "tieba": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "vote": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "custom": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "huodong": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "tuan": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "shop": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "circle": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "house-sale": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "house-zu": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "job": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "waimai": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "comment": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "commtlist": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "fabu": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "qj": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "diancan": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "diancan-detail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "diancan-cart": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "diancan-table": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "dingzuo": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "dingzuo-online": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "dingzuo-time_choice": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "dingzuo-results": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "paidui": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "paidui-results": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "maidan": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "maidan-explain": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "pay": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "payreturn": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "comdetail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "allComment": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "notices": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "noticesdetail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "discovery_detail": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "type": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "addr": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}, "alist": {"params": ["addrid", "typeid", "desk"], "needLogin": true, "file": "火鸟门户初始化安装包/install/module/system/api/handlers/business.controller.php"}}}