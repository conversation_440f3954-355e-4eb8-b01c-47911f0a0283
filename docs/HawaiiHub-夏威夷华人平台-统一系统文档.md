# 🏝️ HawaiiHub 夏威夷华人平台 - 统一系统文档

> **项目名称**: HawaiiHub 夏威夷华人平台 (底层为 火鸟门户系统 - HuoNiaoCMS)  
> **开发商**: 苏州酷曼软件技术有限公司 (https://www.kumanyun.com)  
> **系统类型**: 基于自研MVC框架的PHP门户系统  
> **应用场景**: 面向夏威夷华人群体的综合性生活服务平台  
> **文档版本**: v3.0 - 统一整合版  
> **更新时间**: 2025-06-30

---

## 📋 项目概述

### 🎯 核心定位
**夏威夷华人平台 (hawaiihub.net)** 是一个综合性华人生活服务平台，致力于为夏威夷华人社区提供全方位的信息服务和社交功能。

### 🏗️ 系统特点
- **多端同步**：支持APP、微信、小程序、H5、电脑
- **多城市分站**：可根据不同城市建立子站点
- **核心文件加密**：使用 Swoole Loader 加密核心业务逻辑
- **模块化设计**：功能模块独立，便于维护和扩展
- **企业级架构**：自研MVC框架，支持大型门户网站

---

## 📊 系统规模统计

| 项目 | 数量/大小 | 说明 |
|------|-----------|------|
| **总文件数** | 21,100+ | 包含完整系统的所有文件 |
| **PHP文件数** | 3,720+ | 核心业务逻辑文件 |
| **数据库表数** | 490张 | `hn_`前缀命名规范 |
| **核心安装包** | 3.3MB | 启动器和基础文件 |
| **完整系统包** | 268MB | `system.zip`解压后内容 |
| **API接口数** | 200+ | 完整的API接口体系 |
| **插件数量** | 26+ | 功能扩展插件 |

---

## 🏗️ 技术架构

### 技术栈
- **后端**: PHP 7.4+, 自研MVC框架, MySQL 5.7+, Redis (可选)
- **前端**: HTML5, CSS3, JavaScript (jQuery, Layui, Vue.js)
- **核心加密**: `swoole_loader` 扩展授权
- **部署**: Apache/Nginx + PHP-FPM

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Web浏览器      │   微信小程序      │      移动APP              │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       负载均衡层                               │
│                    Nginx / Apache                            │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       应用层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    前端展示层     │     API接口层     │      管理后台层           │
│   Templates      │       API        │       Admin             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       业务逻辑层                               │
│                     Include 核心库                           │
├─────────────────┬─────────────────┬─────────────────────────┤
│   业务处理核心   │    工具类库      │      第三方集成           │
│  Common.func     │     Class       │      Vendor             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       数据层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    MySQL数据库   │    文件存储      │      缓存系统             │
│                 │    Uploads       │      Redis              │
└─────────────────┴─────────────────┴─────────────────────────┘
```

---

## 📁 完整目录结构

### 根目录 (`hawaiihub.net/`)
```
hawaiihub.net/
├── admin/                  # 管理后台 (40个功能模块)
├── api/                    # API接口层 (11个服务模块)
├── data/                   # 数据文件存储
├── design/                 # 设计资源文件
├── include/                # 核心功能库 (14个子目录)
├── log/                    # 系统日志文件
├── static/                 # 静态资源 (9个资源类型)
├── templates/              # 前端模板 (50个模板模块)
├── templates_c/            # 编译模板缓存
├── uploads/                # 用户上传文件
├── wmsj/                   # 系统功能目录 (外卖商家)
├── index.php               # 网站入口文件
├── huoniao.php             # 火鸟核心配置
├── huoniao.dll / huoniao.so # 核心加密库
└── .htaccess               # Apache重写规则
```

### 核心安装包结构
```
火鸟门户初始化安装包/
├── huoniao                     # 🚀 主程序入口（1.2KB，Linux/Mac）
├── huoniao.dll                 # 🚀 Windows平台动态库（1.0MB）
├── huoniao.so                  # 🚀 Linux/Mac平台动态库（1.4MB）
├── index.php                   # 🌐 Web入口文件（766B）
├── include/                    # 🏗️ 核心类库目录
│   └── class/
│       ├── file.class.php      # 文件操作类（10.6KB）
│       └── httpdown.class.php  # HTTP下载类（14.5KB）
└── install/                    # 🛠️ 安装程序目录
    ├── check.txt               # 安装检查文件
    ├── db_default.txt          # 默认数据库配置
    ├── db_structure.txt        # 数据库结构文件（490张表）
    ├── dbinfo.inc.php          # 数据库信息配置
    ├── index.php               # 安装程序主文件（27.8KB）
    ├── module/                 # 📦 核心模块包
    │   └── system/
    │       └── system.zip      # 🔥 完整系统包（268MB）
    └── templates/              # 🎨 安装界面模板
```

---

## 🔧 核心功能模块

### 1. 管理后台模块 (`admin/`)
- **系统管理**: 网站配置、权限管理、数据备份
- **内容管理**: 文章、论坛、评论等
- **用户管理**: 会员、管理员、等级、用户组
- **商业功能**: 商家、商城、订单、支付
- **生活服务**: 房产、招聘、二手车、婚恋
- **社区功能**: 圈子、直播、视频
- **营销推广**: 活动、优惠券、广告
- **数据统计**: 概览、用户、内容、流量、收入分析

### 2. API接口模块 (`api/`)
- **Base URL**: `https://hawaiihub.net/api/`
- **认证**: Token认证 (`Authorization: Bearer {access_token}`)
- **核心服务**:
  - `login.php`: 用户认证
  - `handlers/`: 核心业务处理器 (siteConfig, member, business)
  - `payment/`: 支付集成 (Alipay, WxPay, PayPal等)
  - `upload/`: 文件上传 (Qiniu, Aliyun, Tencent Cloud等)
  - `login/`: 第三方登录 (WeChat, QQ, Facebook等)

### 3. 核心类库 (`include/`)
- `common.inc.php`: 系统核心配置
- `dsql.class.php`: 数据库操作类 (PDO)
- `userLogin.class.php`: 用户认证体系
- `file.class.php`: 文件操作类
- `payment.class.php`: 支付处理类
- **云服务集成**: Aliyun, Huawei, Tencent, Qiniu SDKs

---

## 🖥️ 服务器环境

### 🔧 基础配置
- **服务器IP**: *************
- **管理面板**: 宝塔面板 (Linux)
- **SSH连接**: `ssh baota` (已配置免密登录)
- **PHP版本**: 7.4
- **数据库**: MySQL 5.7+
- **Web服务器**: Nginx + Apache

### 📁 服务器目录结构
```
/www/wwwroot/hawaiihub.net/
├── api/                    # API接口层
│   └── handlers/          # 控制器目录
├── include/               # 核心类库
├── plugins/               # 插件系统
├── templates/             # 模板系统
├── uploads/               # 上传文件
├── cache/                 # 缓存目录
└── config/                # 配置文件
```

---

## 🚀 快速开始

### 常见查询场景
| 查询类型 | 推荐路径 | 说明 |
|---------|---------|------|
| **系统架构问题** | 本文档 | 完整的平台架构说明 |
| **核心功能查询** | `core-system/include/` | 核心库，包含主要业务逻辑 |
| **数据库结构** | `core-system/install/db_structure.txt` | 490张数据表的完整结构 |
| **API操作** | `api-integration/hawaiihub-api-agent.py` | API代理脚本示例 |
| **服务器运维** | `server-access/*************_id_ed25519` | SSH密钥，可用ssh baota连接 |

### 开发流程
1. **了解平台架构** → 阅读本统一文档
2. **查看核心功能** → 进入 `core-system/include/`
3. **理解数据结构** → 查看 `core-system/install/db_structure.txt`
4. **API集成开发** → 参考 `api-integration/hawaiihub-api-agent.py`
5. **服务器运维** → 使用 `ssh baota` 连接服务器

---

## 🔌 插件系统详解

### 📊 插件统计
- **插件总数**: 26+
- **核心插件**: 信息采集、AI智能、一键转载
- **扩展插件**: 支付、社交、内容管理

### 🎯 重点插件分析

#### 插件 #4: 信息资讯采集插件
```php
// 核心功能
- 自动采集新闻资讯
- 智能内容分类
- 重复内容检测
- 定时任务调度

// 文件结构
plugins/4/
├── index.php           # 插件入口 (12.2KB)
├── config.inc.php      # 配置文件
├── service/            # 业务逻辑
│   ├── crawler.php     # 采集服务
│   └── parser.php      # 内容解析
└── tpl/                # 模板文件
```

#### 插件 #26: AI智能插件
```php
// 核心功能
- AI内容生成
- 智能推荐算法
- 用户行为分析
- 个性化服务

// 技术特点
- 支持多种AI模型
- 实时数据处理
- 机器学习优化
```

---

## 🗄️ 数据库架构

### 📊 数据库统计
- **表总数**: 490张
- **表前缀**: `hn_`
- **核心表**: 用户、内容、分类、插件
- **扩展表**: 统计、日志、缓存

### 🔑 关键数据表
```sql
-- 用户系统
hn_users              # 用户基础信息
hn_user_profiles      # 用户详细资料
hn_user_permissions   # 用户权限管理

-- 内容系统
hn_articles           # 文章内容
hn_categories         # 分类管理
hn_tags               # 标签系统

-- 插件系统
hn_plugins            # 插件注册
hn_plugin_configs     # 插件配置
hn_plugin_data        # 插件数据

-- AI扩展表 (新增)
hn_ai_crawl_tasks     # AI采集任务
hn_ai_crawl_results   # AI采集结果
hn_ai_crawl_logs      # AI采集日志
```

---

## 💼 商业价值与应用场景

### 适用行业
- **本地生活服务**: 餐饮、娱乐、购物
- **多城市门户**: 分站管理、本地化服务
- **商家平台**: 入驻管理、订单处理
- **社区平台**: 用户互动、内容管理

### 核心优势
- ✅ **功能完整**: 涵盖门户网站所需的全部功能
- ✅ **技术先进**: 基于现代PHP技术栈
- ✅ **扩展性强**: 模块化设计，易于扩展
- ✅ **多端支持**: 一套系统，多端同步
- ✅ **商业化**: 支持多种盈利模式

---

## 🛠️ 部署与运维

### 基础环境要求
- **PHP版本**: 7.4+
- **数据库**: MySQL 5.7+
- **Web服务器**: Apache/Nginx
- **PHP扩展**: Swoole Loader扩展（必需）

### 推荐配置
- **操作系统**: Linux/Windows/Mac
- **内存**: 4GB+
- **存储**: 10GB+
- **网络**: 稳定的互联网连接

---

> 💡 **提示**：本文档整合了多个分析报告的核心内容，涵盖了火鸟门户系统的完整技术架构、功能特性、部署指南和使用说明。当遇到平台相关问题时，优先查看本文档，必要时可以远程登录服务器查看线上环境。

*本文档由多个分析报告合并整理而成，为夏威夷华人社区提供了完整的数字化解决方案。*
