# 🔥 火鸟门户系统采集插件批量写入指南

> **基于官方文档的完整采集规则批量导入解决方案** 📊
> **更新时间**: 2025-06-30
> **适用版本**: 火鸟门户系统 v2.0+

---

## 🎯 **重要说明**

本指南基于您提供的**官方采集插件文档**和系统实际测试，提供三种不同复杂度的批量写入方案：

1. **🚀 快速方案**: 直接SQL批量插入 (适合简单场景)
2. **⚡ 标准方案**: PHP脚本批量导入 (推荐使用)
3. **🎛️ 高级方案**: JSON配置文件管理 (企业级应用)

---

## 📋 目录

- [📋 目录](#-目录)
- [🎯 采集插件概述](#-采集插件概述)
- [🗂️ 插件文件结构](#️-插件文件结构)
- [💾 数据库结构](#-数据库结构)
- [🚀 批量写入方法](#-批量写入方法)
- [📝 实际操作步骤](#-实际操作步骤)
- [💡 最佳实践](#-最佳实践)
- [🔧 故障排查](#-故障排查)

---

## 🎯 采集插件概述

### 核心采集插件

火鸟门户系统包含以下主要采集插件：

| 插件名称 | 插件ID | 功能描述 | 文件路径 |
|---------|--------|---------|----------|
| 信息资讯采集插件 | 4 | 新闻文章自动采集 | `/include/plugins/4/` |
| 商品采集插件 | - | 商品信息采集 | `/include/plugins/商品采集/` |
| 一键转载插件 | 5 | 快速转载外部内容 | `/include/plugins/5/` |

### 采集工作原理

1. **定义采集节点** - 为目标网站创建独立采集任务
2. **指定列表页URL** - 提供文章列表页URL，支持 `(*)` 通配符代表页码
3. **定义文章链接规则** - 通过CSS选择器定位文章链接
4. **定义内容提取规则** - 为标题、作者、时间、正文等字段配置CSS选择器
5. **自动执行** - 定时任务调用 `cron.php` 自动执行采集入库

---

## 🗂️ 插件文件结构

### 信息资讯采集插件 (ID: 4)

```
/include/plugins/4/
├── index.php                    # 插件主入口 (12.2KB)
├── common.php                   # 公共函数库 (12.2KB)
├── getNews.php                  # 新闻采集核心 (10.5KB)
├── getUrl.php                   # URL处理模块 (7KB)
├── insertBodyRules.php          # 内容规则插入 (4.6KB)
├── insertNode.php               # 节点插入处理 (3.9KB)
├── cron.php                     # 定时任务脚本
├── service/
│   ├── HttpDownService.php      # HTTP下载服务
│   └── ContentParseService.php  # 内容解析服务
├── static/
│   ├── css/ui/                  # UI样式文件
│   ├── img/                     # 图片资源
│   └── js/                      # JavaScript文件
├── tpl/
│   ├── index.html               # 主界面模板
│   ├── config.html              # 配置界面模板
│   └── rules.html               # 规则配置模板
├── cache/
│   ├── nodes/                   # 采集节点缓存
│   └── content/                 # 内容缓存
└── config/
    ├── nodes.json               # 采集节点配置
    └── rules.json               # 采集规则配置
```

### 核心特性

- ✅ 支持多站点新闻采集
- ✅ 智能内容提取和清洗
- ✅ 自定义采集规则配置
- ✅ 定时任务自动执行
- ✅ 内容去重和质量过滤

---

## 💾 数据库结构

### 采集节点表结构

```sql
-- 采集节点配置表
CREATE TABLE `hn_collection_nodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `url` text NOT NULL COMMENT '采集URL',
  `list_selector` varchar(500) COMMENT '列表选择器',
  `link_selector` varchar(500) COMMENT '链接选择器',
  `encoding` varchar(20) DEFAULT 'utf-8' COMMENT '页面编码',
  `config` text COMMENT '节点配置JSON',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集节点配置表';
```

### 采集规则表结构

```sql
-- 采集规则配置表
CREATE TABLE `hn_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名',
  `selector` varchar(500) NOT NULL COMMENT 'CSS选择器',
  `rule_type` varchar(50) DEFAULT 'text' COMMENT '规则类型',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必需',
  `filter_config` text COMMENT '过滤配置JSON',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`),
  FOREIGN KEY (`node_id`) REFERENCES `hn_collection_nodes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集规则配置表';
```

---

## 🚀 批量写入方法

### 方法一：SQL直接批量插入

#### 批量插入采集节点

```sql
-- 批量插入采集节点
INSERT INTO `hn_collection_nodes` 
(`name`, `url`, `list_selector`, `link_selector`, `encoding`, `status`, `created_time`) VALUES
('新浪新闻科技', 'https://tech.sina.com.cn/roll/index.shtml', '.feed-card-item', '.feed-card-item h2 a', 'utf-8', 1, UNIX_TIMESTAMP()),
('网易新闻科技', 'https://tech.163.com/', '.news_item', '.news_item h3 a', 'utf-8', 1, UNIX_TIMESTAMP()),
('腾讯新闻科技', 'https://new.qq.com/ch/tech/', '.item', '.item h3 a', 'utf-8', 1, UNIX_TIMESTAMP()),
('搜狐新闻科技', 'https://it.sohu.com/', '.news-item', '.news-item h4 a', 'utf-8', 1, UNIX_TIMESTAMP()),
('凤凰新闻科技', 'https://tech.ifeng.com/', '.news_list li', '.news_list li a', 'utf-8', 1, UNIX_TIMESTAMP());
```

#### 批量插入采集规则

```sql
-- 批量插入采集规则 (假设节点ID从1开始)
INSERT INTO `hn_collection_rules` 
(`node_id`, `field_name`, `selector`, `rule_type`, `is_required`, `sort_order`) VALUES
-- 新浪新闻规则
(1, 'title', 'h1.main-title', 'text', 1, 1),
(1, 'content', '.article-content', 'html', 1, 2),
(1, 'author', '.author-name', 'text', 0, 3),
(1, 'publish_time', '.publish-time', 'text', 0, 4),
(1, 'tags', '.tag-list a', 'array', 0, 5),
-- 网易新闻规则
(2, 'title', '.post_title h1', 'text', 1, 1),
(2, 'content', '.post_body', 'html', 1, 2),
(2, 'author', '.ep-editor', 'text', 0, 3),
(2, 'publish_time', '.post_time_source', 'text', 0, 4),
-- 腾讯新闻规则
(3, 'title', '.LEFT h1', 'text', 1, 1),
(3, 'content', '.Cnt-Main-Article-QQ', 'html', 1, 2),
(3, 'author', '.author', 'text', 0, 3),
(3, 'publish_time', '.pubTime', 'text', 0, 4);
```

### 方法二：PHP脚本批量导入

#### 创建批量导入脚本

```php
<?php
/**
 * 火鸟门户系统采集规则批量导入脚本
 * 文件名: batch_import_rules.php
 */

require_once '../include/common.inc.php';

// 批量采集规则配置
$batch_rules = [
    [
        'name' => '新浪新闻科技',
        'url' => 'https://tech.sina.com.cn/roll/index.shtml',
        'list_selector' => '.feed-card-item',
        'link_selector' => '.feed-card-item h2 a',
        'encoding' => 'utf-8',
        'rules' => [
            'title' => ['selector' => 'h1.main-title', 'type' => 'text', 'required' => true],
            'content' => ['selector' => '.article-content', 'type' => 'html', 'required' => true],
            'author' => ['selector' => '.author-name', 'type' => 'text', 'required' => false],
            'publish_time' => ['selector' => '.publish-time', 'type' => 'text', 'required' => false],
            'tags' => ['selector' => '.tag-list a', 'type' => 'array', 'required' => false]
        ]
    ],
    [
        'name' => '网易新闻科技',
        'url' => 'https://tech.163.com/',
        'list_selector' => '.news_item',
        'link_selector' => '.news_item h3 a',
        'encoding' => 'utf-8',
        'rules' => [
            'title' => ['selector' => '.post_title h1', 'type' => 'text', 'required' => true],
            'content' => ['selector' => '.post_body', 'type' => 'html', 'required' => true],
            'author' => ['selector' => '.ep-editor', 'type' => 'text', 'required' => false],
            'publish_time' => ['selector' => '.post_time_source', 'type' => 'text', 'required' => false]
        ]
    ]
];

/**
 * 批量插入采集规则
 * @param array $rules 规则配置数组
 * @return bool 是否成功
 */
function batchInsertRules($rules) {
    global $dsql;
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($rules as $rule) {
        try {
            // 开始事务
            $dsql->ExecuteNoneQuery("START TRANSACTION");
            
            // 插入采集节点
            $node_sql = "INSERT INTO `#@__collection_nodes` 
                         (`name`, `url`, `list_selector`, `link_selector`, `encoding`, `status`, `created_time`) 
                         VALUES ('{$rule['name']}', '{$rule['url']}', '{$rule['list_selector']}', 
                                '{$rule['link_selector']}', '{$rule['encoding']}', 1, " . time() . ")";
            
            $dsql->ExecuteNoneQuery($node_sql);
            $node_id = $dsql->GetLastID();
            
            if (!$node_id) {
                throw new Exception("插入采集节点失败: {$rule['name']}");
            }
            
            // 插入采集规则
            $sort_order = 1;
            foreach ($rule['rules'] as $field => $rule_config) {
                $filter_config = json_encode($rule_config);
                $is_required = $rule_config['required'] ? 1 : 0;
                
                $rule_sql = "INSERT INTO `#@__collection_rules` 
                             (`node_id`, `field_name`, `selector`, `rule_type`, `is_required`, `filter_config`, `sort_order`) 
                             VALUES ($node_id, '$field', '{$rule_config['selector']}', '{$rule_config['type']}', 
                                    $is_required, '$filter_config', $sort_order)";
                
                $dsql->ExecuteNoneQuery($rule_sql);
                $sort_order++;
            }
            
            // 提交事务
            $dsql->ExecuteNoneQuery("COMMIT");
            
            echo "✅ 成功导入采集节点: {$rule['name']} (ID: $node_id)\n";
            $success_count++;
            
        } catch (Exception $e) {
            // 回滚事务
            $dsql->ExecuteNoneQuery("ROLLBACK");
            echo "❌ 导入失败: {$rule['name']} - {$e->getMessage()}\n";
            $error_count++;
        }
    }
    
    echo "\n📊 导入统计:\n";
    echo "成功: $success_count 个\n";
    echo "失败: $error_count 个\n";
    
    return $error_count === 0;
}

// 执行批量导入
echo "🚀 开始批量导入采集规则...\n\n";
$result = batchInsertRules($batch_rules);

if ($result) {
    echo "\n🎉 批量导入完成！所有规则导入成功。\n";
} else {
    echo "\n⚠️ 批量导入完成，但有部分规则导入失败，请检查错误信息。\n";
}
?>
```

### 方法三：JSON配置文件导入

#### JSON配置文件示例

```json
{
  "version": "1.0",
  "description": "火鸟门户系统采集规则配置",
  "rules": [
    {
      "name": "新浪新闻科技",
      "url": "https://tech.sina.com.cn/roll/index.shtml",
      "encoding": "utf-8",
      "list_config": {
        "list_selector": ".feed-card-item",
        "link_selector": ".feed-card-item h2 a",
        "link_attr": "href"
      },
      "content_rules": {
        "title": {
          "selector": "h1.main-title",
          "type": "text",
          "required": true,
          "filter": {
            "min_length": 5,
            "max_length": 200
          }
        },
        "content": {
          "selector": ".article-content",
          "type": "html",
          "required": true,
          "filter": {
            "remove_tags": ["script", "style", ".ad", ".advertisement"],
            "min_length": 100
          }
        },
        "author": {
          "selector": ".author-name",
          "type": "text",
          "required": false
        },
        "publish_time": {
          "selector": ".publish-time",
          "type": "text",
          "required": false,
          "format": "Y-m-d H:i:s"
        },
        "tags": {
          "selector": ".tag-list a",
          "type": "array",
          "required": false
        }
      },
      "filters": {
        "title_min_length": 10,
        "content_min_length": 100,
        "exclude_keywords": ["广告", "推广", "赞助"],
        "duplicate_check": true
      }
    }
  ]
}
```

---

## 📝 实际操作步骤

### 第1步：准备工作

1. **备份数据库**
   ```bash
   mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **确认插件状态**
   - 访问：`/admin/siteConfig/plugins.php`
   - 确认信息资讯采集插件已安装并启用

3. **准备规则数据**
   - 整理要批量导入的网站列表
   - 分析目标网站的页面结构
   - 编写对应的CSS选择器

### 第2步：选择导入方式

| 场景 | 推荐方法 | 优势 |
|------|---------|------|
| 简单规则，数量较少 | 方法一 (SQL) | 直接快速 |
| 复杂规则，需要验证 | 方法二 (PHP) | 可控性强，支持事务 |
| 标准化管理，团队协作 | 方法三 (JSON) | 配置标准化，易维护 |

### 第3步：执行批量导入

#### 通过管理后台执行
```
1. 访问: /admin/siteConfig/plugins.php
2. 选择: 信息资讯采集插件
3. 点击: 批量导入功能
4. 上传: 配置文件或粘贴规则
```

#### 通过命令行执行
```bash
# 进入网站根目录
cd /path/to/your/website

# 执行PHP脚本
php batch_import_rules.php

# 或通过浏览器访问
# http://yourdomain.com/batch_import_rules.php
```

### 第4步：验证和测试

1. **检查导入结果**
   ```sql
   -- 查看导入的节点
   SELECT * FROM hn_collection_nodes ORDER BY created_time DESC;
   
   -- 查看导入的规则
   SELECT n.name, r.field_name, r.selector 
   FROM hn_collection_nodes n 
   LEFT JOIN hn_collection_rules r ON n.id = r.node_id 
   ORDER BY n.id, r.sort_order;
   ```

2. **测试采集功能**
   - 手动触发单个节点采集
   - 检查采集到的内容质量
   - 验证字段映射是否正确

3. **设置定时任务**
   ```bash
   # 添加到crontab
   # 每小时执行一次采集
   0 * * * * /usr/bin/php /path/to/website/include/plugins/4/cron.php
   ```

---

## 💡 最佳实践

### 规则设计原则

1. **选择器精确性**
   - 使用稳定的CSS选择器
   - 避免使用易变的class名
   - 优先使用结构化选择器

2. **容错处理**
   ```php
   // 设置备用选择器
   $selectors = [
       'title' => ['h1.main-title', 'h1.title', '.article-title h1'],
       'content' => ['.article-content', '.post-content', '.main-content']
   ];
   ```

3. **内容过滤**
   ```php
   // 配置质量过滤规则
   $filters = [
       'min_title_length' => 10,
       'min_content_length' => 100,
       'exclude_keywords' => ['广告', '推广'],
       'remove_tags' => ['script', 'style', '.ad']
   ];
   ```

### 性能优化

1. **批量处理**
   - 一次处理多个规则
   - 使用事务确保数据一致性
   - 合理设置批次大小

2. **缓存机制**
   ```php
   // 缓存已采集的URL
   $cache_key = md5($url);
   if (!$cache->get($cache_key)) {
       // 执行采集
       $cache->set($cache_key, true, 3600);
   }
   ```

3. **限流控制**
   ```php
   // 控制采集频率
   sleep(1); // 每次请求间隔1秒
   ```

### 监控和维护

1. **日志记录**
   ```php
   // 记录采集日志
   error_log("[采集] 成功采集: $url - 标题: $title");
   ```

2. **错误处理**
   ```php
   try {
       // 采集逻辑
   } catch (Exception $e) {
       error_log("[采集错误] $url: " . $e->getMessage());
   }
   ```

3. **定期检查**
   - 监控采集成功率
   - 检查规则有效性
   - 更新失效的选择器

---

## 🔧 故障排查

### 常见问题及解决方案

1. **选择器失效**
   ```
   问题: CSS选择器无法匹配内容
   解决: 检查目标网站是否更新了页面结构，更新选择器
   ```

2. **编码问题**
   ```
   问题: 采集到的内容出现乱码
   解决: 检查并设置正确的页面编码
   ```

3. **反爬虫限制**
   ```
   问题: 目标网站返回403或验证码
   解决: 设置User-Agent，增加请求间隔，使用代理
   ```

4. **内存溢出**
   ```
   问题: 批量导入时内存不足
   解决: 分批处理，增加内存限制
   ```

### 调试技巧

1. **开启调试模式**
   ```php
   // 在脚本开头添加
   ini_set('display_errors', 1);
   error_reporting(E_ALL);
   ```

2. **输出详细信息**
   ```php
   echo "正在处理: $url\n";
   echo "使用选择器: $selector\n";
   echo "匹配结果: " . count($matches) . " 个\n";
   ```

3. **测试单个规则**
   ```php
   // 先测试单个网站的采集规则
   $test_url = 'https://example.com/article/123';
   $result = testSingleRule($test_url, $rules);
   var_dump($result);
   ```

---

---

## 📚 附录

### A. 常用CSS选择器参考

| 选择器类型 | 语法 | 示例 | 说明 |
|-----------|------|------|------|
| 标签选择器 | `tag` | `h1`, `p`, `div` | 选择指定标签 |
| 类选择器 | `.class` | `.title`, `.content` | 选择指定class |
| ID选择器 | `#id` | `#main`, `#header` | 选择指定id |
| 属性选择器 | `[attr]` | `[href]`, `[data-id]` | 选择有指定属性的元素 |
| 后代选择器 | `parent child` | `.article p` | 选择父元素内的子元素 |
| 直接子选择器 | `parent > child` | `.list > li` | 选择直接子元素 |
| 相邻兄弟选择器 | `prev + next` | `h1 + p` | 选择相邻的下一个元素 |
| 通用兄弟选择器 | `prev ~ siblings` | `h1 ~ p` | 选择后面所有兄弟元素 |
| 伪类选择器 | `:pseudo` | `:first-child`, `:last-child` | 选择特定状态的元素 |
| 伪元素选择器 | `::pseudo` | `::before`, `::after` | 选择元素的特定部分 |

### B. 主流新闻网站选择器模板

#### 新浪新闻

```css
/* 列表页 */
.feed-card-item                    /* 文章列表项 */
.feed-card-item h2 a               /* 文章链接 */

/* 详情页 */
h1.main-title                      /* 文章标题 */
.article-content                   /* 文章内容 */
.author-name                       /* 作者 */
.publish-time                      /* 发布时间 */
.tag-list a                        /* 标签 */
```

#### 网易新闻

```css
/* 列表页 */
.news_item                         /* 文章列表项 */
.news_item h3 a                    /* 文章链接 */

/* 详情页 */
.post_title h1                     /* 文章标题 */
.post_body                         /* 文章内容 */
.ep-editor                         /* 作者 */
.post_time_source                  /* 发布时间 */
```

#### 腾讯新闻

```css
/* 列表页 */
.item                              /* 文章列表项 */
.item h3 a                         /* 文章链接 */

/* 详情页 */
.LEFT h1                           /* 文章标题 */
.Cnt-Main-Article-QQ               /* 文章内容 */
.author                            /* 作者 */
.pubTime                           /* 发布时间 */
```

### C. 数据库表完整结构

#### 采集节点表 (hn_collection_nodes)

```sql
CREATE TABLE `hn_collection_nodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `url` text NOT NULL COMMENT '采集URL',
  `list_selector` varchar(500) COMMENT '列表选择器',
  `link_selector` varchar(500) COMMENT '链接选择器',
  `link_attr` varchar(50) DEFAULT 'href' COMMENT '链接属性',
  `encoding` varchar(20) DEFAULT 'utf-8' COMMENT '页面编码',
  `user_agent` varchar(500) COMMENT '用户代理',
  `headers` text COMMENT '请求头JSON',
  `proxy` varchar(255) COMMENT '代理设置',
  `timeout` int(11) DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_times` int(11) DEFAULT 3 COMMENT '重试次数',
  `interval_time` int(11) DEFAULT 60 COMMENT '采集间隔(秒)',
  `max_pages` int(11) DEFAULT 10 COMMENT '最大采集页数',
  `config` text COMMENT '节点配置JSON',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `last_run_time` int(11) COMMENT '最后运行时间',
  `total_collected` int(11) DEFAULT 0 COMMENT '总采集数量',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_run` (`last_run_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集节点配置表';
```

#### 采集规则表 (hn_collection_rules)

```sql
CREATE TABLE `hn_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名',
  `field_label` varchar(100) COMMENT '字段标签',
  `selector` varchar(500) NOT NULL COMMENT 'CSS选择器',
  `backup_selectors` text COMMENT '备用选择器JSON数组',
  `rule_type` varchar(50) DEFAULT 'text' COMMENT '规则类型: text,html,attr,array',
  `attr_name` varchar(100) COMMENT '属性名(当type=attr时)',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必需',
  `default_value` text COMMENT '默认值',
  `filter_config` text COMMENT '过滤配置JSON',
  `transform_config` text COMMENT '转换配置JSON',
  `validation_config` text COMMENT '验证配置JSON',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_field_name` (`field_name`),
  FOREIGN KEY (`node_id`) REFERENCES `hn_collection_nodes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集规则配置表';
```

#### 采集日志表 (hn_collection_logs)

```sql
CREATE TABLE `hn_collection_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `url` text COMMENT '采集URL',
  `status` varchar(20) COMMENT '状态: success,failed,skipped',
  `collected_count` int(11) DEFAULT 0 COMMENT '采集数量',
  `error_message` text COMMENT '错误信息',
  `execution_time` decimal(10,3) COMMENT '执行时间(秒)',
  `memory_usage` int(11) COMMENT '内存使用(字节)',
  `created_time` int(11) COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集日志表';
```

### D. 高级配置示例

#### 完整JSON配置模板

```json
{
  "version": "2.0",
  "description": "火鸟门户系统高级采集规则配置",
  "global_config": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "timeout": 30,
    "retry_times": 3,
    "interval_time": 60,
    "max_pages": 10,
    "enable_cache": true,
    "cache_duration": 3600
  },
  "rules": [
    {
      "name": "新浪新闻科技频道",
      "url": "https://tech.sina.com.cn/roll/index.shtml",
      "encoding": "utf-8",
      "list_config": {
        "list_selector": ".feed-card-item",
        "link_selector": ".feed-card-item h2 a",
        "link_attr": "href",
        "pagination": {
          "enabled": true,
          "url_pattern": "https://tech.sina.com.cn/roll/index_{page}.shtml",
          "start_page": 1,
          "max_pages": 5
        }
      },
      "content_rules": {
        "title": {
          "selector": "h1.main-title",
          "backup_selectors": ["h1.title", ".article-title h1"],
          "type": "text",
          "required": true,
          "filter": {
            "trim": true,
            "min_length": 5,
            "max_length": 200,
            "remove_patterns": ["\\[.*?\\]", "【.*?】"]
          },
          "validation": {
            "not_empty": true,
            "exclude_keywords": ["测试", "广告"]
          }
        },
        "content": {
          "selector": ".article-content",
          "backup_selectors": [".post-content", ".main-content"],
          "type": "html",
          "required": true,
          "filter": {
            "remove_tags": ["script", "style", "iframe", ".ad", ".advertisement"],
            "remove_attributes": ["onclick", "onload", "style"],
            "min_length": 100,
            "clean_html": true
          },
          "transform": {
            "relative_to_absolute_urls": true,
            "download_images": false,
            "compress_whitespace": true
          }
        },
        "author": {
          "selector": ".author-name",
          "backup_selectors": [".author", ".writer"],
          "type": "text",
          "required": false,
          "filter": {
            "trim": true,
            "default_value": "佚名"
          }
        },
        "publish_time": {
          "selector": ".publish-time",
          "backup_selectors": [".time", ".date"],
          "type": "text",
          "required": false,
          "transform": {
            "date_format": "Y-m-d H:i:s",
            "timezone": "Asia/Shanghai"
          },
          "validation": {
            "is_valid_date": true
          }
        },
        "tags": {
          "selector": ".tag-list a",
          "type": "array",
          "required": false,
          "filter": {
            "max_items": 10,
            "unique": true
          }
        },
        "images": {
          "selector": ".article-content img",
          "type": "array",
          "attr_name": "src",
          "required": false,
          "filter": {
            "valid_urls_only": true,
            "min_size": "100x100"
          }
        }
      },
      "filters": {
        "global": {
          "title_min_length": 10,
          "content_min_length": 100,
          "exclude_keywords": ["广告", "推广", "赞助", "测试"],
          "duplicate_check": {
            "enabled": true,
            "check_fields": ["title", "url"],
            "similarity_threshold": 0.8
          }
        },
        "quality": {
          "min_word_count": 50,
          "max_ad_ratio": 0.3,
          "require_images": false
        }
      },
      "post_process": {
        "auto_categorize": true,
        "extract_keywords": true,
        "generate_summary": true,
        "seo_optimize": true
      }
    }
  ]
}
```

### E. 命令行工具

#### 采集管理脚本

```bash
#!/bin/bash
# 文件名: collection_manager.sh

SCRIPT_DIR="/path/to/website/include/plugins/4"
LOG_DIR="/path/to/logs"

case "$1" in
    "start")
        echo "启动采集任务..."
        php $SCRIPT_DIR/cron.php >> $LOG_DIR/collection.log 2>&1
        ;;
    "stop")
        echo "停止采集任务..."
        pkill -f "cron.php"
        ;;
    "status")
        echo "检查采集状态..."
        ps aux | grep cron.php | grep -v grep
        ;;
    "test")
        echo "测试采集规则..."
        php $SCRIPT_DIR/test_rules.php
        ;;
    "import")
        echo "导入采集规则..."
        php $SCRIPT_DIR/batch_import_rules.php
        ;;
    *)
        echo "用法: $0 {start|stop|status|test|import}"
        exit 1
        ;;
esac
```

---

**📞 技术支持**: 如遇到问题，请参考官方文档或联系技术支持团队。

**🔄 文档更新**: 本文档会根据系统更新持续维护，请关注最新版本。

**📖 相关文档**:

- [火鸟门户系统-统一完整分析文档](./火鸟门户系统-统一完整分析文档.md)
- [火鸟门户系统-官方文档索引](./火鸟门户系统-官方文档索引.md)
- [火鸟门户系统-AI学习完整指南](./火鸟门户系统-AI学习完整指南.md)
