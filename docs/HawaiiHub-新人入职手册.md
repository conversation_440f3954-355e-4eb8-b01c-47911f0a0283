# 🏝️ HawaiiHub 新人入职手册

> **欢迎加入夏威夷华人平台团队！**  
> 这份手册将帮助你快速了解系统架构、掌握操作流程、熟悉维护规范

---

## 📋 目录
- [系统概览](#系统概览)
- [技术架构](#技术架构)
- [开发环境](#开发环境)
- [日常操作](#日常操作)
- [故障处理](#故障处理)
- [最佳实践](#最佳实践)

---

## 🌟 系统概览

### 项目背景
**HawaiiHub 夏威夷华人平台** 是一个基于火鸟门户系统的综合性华人生活服务平台，专为夏威夷华人社区打造。

### 核心信息
- **网站域名**: hawaiihub.net
- **服务器IP**: *************
- **管理面板**: https://*************:8889 (宝塔面板)
- **技术架构**: PHP 7.4 + MySQL + Nginx + 火鸟门户框架
- **部署环境**: Debian 12 + 宝塔面板

### 主要功能模块
```
📰 新闻资讯    🏢 商家服务    🏠 房产租售    💼 招聘求职
🛍️ 优惠团购    👥 社区互动    📱 移动应用    💳 在线支付
🎯 广告投放    📊 数据统计    🔧 系统管理    🔒 安全防护
```

---

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────┐
│                      前端展示层                           │
├─────────────────────────────────────────────────────────┤
│  PC端(skin10)  │  移动端(skin4)  │  APP  │  小程序      │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                             │
├─────────────────────────────────────────────────────────┤
│  用户模块  │  商家模块  │  内容模块  │  支付模块  │  ......│
├─────────────────────────────────────────────────────────┤
│                    框架核心层                             │
├─────────────────────────────────────────────────────────┤
│         火鸟门户框架 (MVC架构 + 模板引擎)                  │
├─────────────────────────────────────────────────────────┤
│                    基础设施层                             │
├─────────────────────────────────────────────────────────┤
│  Nginx  │  PHP 7.4-FPM  │  MySQL 5.7+  │  Redis(可选)  │
├─────────────────────────────────────────────────────────┤
│                 Debian 12 + 宝塔面板                     │
└─────────────────────────────────────────────────────────┘
```

### 目录结构总览
```
hawaiihub.net/                    # 网站根目录
├── admin/                        # 🔧 管理后台 (40个功能模块)
├── api/                          # 🔌 API接口 (11个服务模块)  
├── include/                      # 🧠 核心功能库 (系统内核)
├── templates/                    # 🎨 前端模板 (50个模板)
├── static/                       # 📦 静态资源 (CSS/JS/图片)
├── uploads/                      # 📁 用户上传文件
├── templates_c/                  # ⚡ 模板编译缓存
├── data/                         # 💾 数据文件
├── log/                          # 📝 系统日志
├── index.php                     # 🚪 网站入口
├── .htaccess                     # ⚙️ URL重写规则
└── 配置文件...                   # 🔐 各种配置
```

### 数据库结构
- **数据库**: hawaiihub_net
- **表总数**: 490张表
- **表前缀**: hn_
- **编码**: utf8
- **核心表**:
  - `hn_member`: 会员信息
  - `hn_article`: 文章内容
  - `hn_business`: 商家信息
  - `hn_payment`: 支付记录
  - `hn_config`: 系统配置

---

## 💻 开发环境

### 本地环境要求
```bash
操作系统: macOS/Linux/Windows
PHP版本: >= 7.4
MySQL版本: >= 5.7
Web服务器: Nginx/Apache
开发工具: Trae AI / Cursor / VS Code
版本控制: Git
终端工具: SSH客户端
```

### 服务器连接方式

#### SSH连接
```bash
# 使用SSH密钥连接
ssh -i ~/.ssh/server_key root@*************

# 或使用用户名密码
ssh root@*************
```

#### 宝塔面板访问
```
URL: https://*************:8889
用户名: [管理员提供]
密码: [管理员提供]
```

### 重要文件路径
```bash
# 网站根目录
/www/wwwroot/hawaiihub.net/

# 配置文件目录
/www/wwwroot/hawaiihub.net/include/config/

# 日志文件目录
/www/wwwlogs/

# Nginx配置
/www/server/panel/vhost/nginx/hawaiihub.net.conf

# SSL证书
/www/server/panel/vhost/cert/hawaiihub.net/
```

---

## 🔧 日常操作

### 1. 系统监控

#### 检查服务状态
```bash
# 检查Web服务
systemctl status nginx
systemctl status php7.4-fpm

# 检查数据库
systemctl status mysql

# 检查网站可访问性
curl -I https://hawaiihub.net
```

#### 查看系统资源
```bash
# CPU和内存使用情况
top
htop

# 磁盘使用情况
df -h
du -sh /www/wwwroot/hawaiihub.net/

# 网络连接状态
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

### 2. 日志管理

#### 访问日志分析
```bash
# 实时查看访问日志
tail -f /www/wwwlogs/hawaiihub.net.log

# 分析访问统计
awk '{print $1}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -10

# 查看访问量最高的页面
awk '{print $7}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -10
```

#### 错误日志检查
```bash
# 查看Nginx错误日志
tail -f /www/wwwlogs/hawaiihub.net.error.log

# 查看PHP错误日志
tail -f /var/log/php7.4-fpm.log

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

### 3. 文件管理

#### 文件权限修复
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod 755 /www/wwwroot/hawaiihub.net/
chmod 777 /www/wwwroot/hawaiihub.net/include/
chmod 777 /www/wwwroot/hawaiihub.net/templates_c/
chmod 755 /www/wwwroot/hawaiihub.net/uploads/
```

#### 清理临时文件
```bash
# 清理模板编译缓存
rm -rf /www/wwwroot/hawaiihub.net/templates_c/*

# 清理上传临时文件
find /www/wwwroot/hawaiihub.net/uploads/temp/ -mtime +7 -delete

# 清理日志文件(保留30天)
find /www/wwwlogs/ -name "*.log" -mtime +30 -delete
```

### 4. 数据库操作

#### 数据库连接
```bash
# 连接数据库
mysql -u hawaiihub_net -pBkNR4w1KHrXX48by hawaiihub_net
```

#### 常用SQL操作
```sql
-- 查看表结构
SHOW TABLES;
DESCRIBE hn_member;

-- 查看会员统计
SELECT COUNT(*) FROM hn_member;

-- 查看文章统计
SELECT COUNT(*) FROM hn_article;

-- 查看今日注册用户
SELECT COUNT(*) FROM hn_member WHERE DATE(reg_time) = CURDATE();

-- 清理过期会话
DELETE FROM hn_session WHERE expire_time < UNIX_TIMESTAMP();
```

#### 数据库备份
```bash
# 备份数据库
mysqldump -u hawaiihub_net -pBkNR4w1KHrXX48by hawaiihub_net > backup_$(date +%Y%m%d).sql

# 压缩备份文件
gzip backup_$(date +%Y%m%d).sql
```

---

## 🚨 故障处理

### 常见问题快速解决

#### 1. 网站无法访问 (502/503错误)
```bash
# 诊断步骤
1. 检查Nginx状态: systemctl status nginx
2. 检查PHP-FPM状态: systemctl status php7.4-fpm
3. 重启服务: systemctl restart nginx php7.4-fpm
4. 查看错误日志: tail -f /www/wwwlogs/hawaiihub.net.error.log
```

#### 2. 数据库连接失败
```bash
# 诊断步骤
1. 检查MySQL状态: systemctl status mysql
2. 测试数据库连接: mysql -u hawaiihub_net -pBkNR4w1KHrXX48by
3. 检查配置文件: cat /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php
4. 重启MySQL: systemctl restart mysql
```

#### 3. 文件上传失败
```bash
# 诊断步骤
1. 检查目录权限: ls -la /www/wwwroot/hawaiihub.net/uploads/
2. 修复权限: chmod 755 /www/wwwroot/hawaiihub.net/uploads/
3. 检查磁盘空间: df -h
4. 检查PHP配置: grep upload_max_filesize /etc/php/7.4/fpm/php.ini
```

#### 4. 性能问题排查
```bash
# 系统资源检查
1. CPU使用率: top
2. 内存使用: free -h
3. 磁盘IO: iotop
4. 网络连接: ss -tulnp

# 应用层检查
1. PHP进程: ps aux | grep php-fpm
2. MySQL进程: SHOW PROCESSLIST;
3. 慢查询: tail -f /var/log/mysql/mysql-slow.log
```

### 紧急处理流程

#### 步骤1: 快速评估
```bash
# 检查核心服务
systemctl is-active nginx && echo "✓ Nginx正常" || echo "✗ Nginx异常"
systemctl is-active php7.4-fpm && echo "✓ PHP正常" || echo "✗ PHP异常"  
systemctl is-active mysql && echo "✓ MySQL正常" || echo "✗ MySQL异常"

# 检查网站可访问性
curl -s -o /dev/null -w "%{http_code}" https://hawaiihub.net
```

#### 步骤2: 服务重启
```bash
# 重启Web服务
systemctl restart nginx
systemctl restart php7.4-fpm

# 如果数据库有问题
systemctl restart mysql
```

#### 步骤3: 日志分析
```bash
# 查看最近的错误
tail -50 /www/wwwlogs/hawaiihub.net.error.log
tail -50 /var/log/php7.4-fpm.log
tail -50 /var/log/mysql/error.log
```

#### 步骤4: 通知相关人员
```bash
# 紧急联系方式
技术负责人: [电话/微信]
运维团队: [群聊/邮件]
业务负责人: [电话/微信]
```

---

## 📚 最佳实践

### 1. 安全规范

#### 密码管理
- 数据库密码定期更换
- SSH密钥定期轮换
- 宝塔面板密码使用强密码
- 重要配置文件权限设为600

#### 访问控制
```bash
# 限制SSH登录IP
echo "AllowUsers root@特定IP" >> /etc/ssh/sshd_config

# 禁用root远程登录(可选)
sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
```

#### 文件安全
```bash
# 重要配置文件权限
chmod 600 /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php
chmod 600 /www/wwwroot/hawaiihub.net/huoniao.php

# 定期检查可疑文件
find /www/wwwroot/hawaiihub.net/ -name "*.php" -exec grep -l "eval\|base64_decode" {} \;
```

### 2. 性能优化

#### 缓存策略
```bash
# 开启OPcache
echo "opcache.enable=1" >> /etc/php/7.4/fpm/php.ini
echo "opcache.memory_consumption=256" >> /etc/php/7.4/fpm/php.ini

# 静态文件缓存(Nginx配置已设置)
# 图片缓存30天，CSS/JS缓存12小时
```

#### 数据库优化
```sql
-- 定期清理过期数据
DELETE FROM hn_logs WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
DELETE FROM hn_session WHERE expire_time < UNIX_TIMESTAMP();

-- 优化表结构
OPTIMIZE TABLE hn_member, hn_article, hn_business;
```

### 3. 监控告警

#### 自动化监控脚本
```bash
#!/bin/bash
# /root/monitor.sh

# 检查网站可访问性
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://hawaiihub.net)
if [ $HTTP_CODE != "200" ]; then
    echo "警告: 网站无法访问 (HTTP $HTTP_CODE)" | mail -s "HawaiiHub告警" <EMAIL>
fi

# 检查磁盘使用率
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率过高 ($DISK_USAGE%)" | mail -s "HawaiiHub告警" <EMAIL>
fi

# 检查内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "警告: 内存使用率过高 ($MEM_USAGE%)" | mail -s "HawaiiHub告警" <EMAIL>
fi
```

#### 定时任务设置
```bash
# 编辑定时任务
crontab -e

# 添加监控任务
*/5 * * * * /root/monitor.sh
0 2 * * * /root/backup.sh
0 4 * * * find /www/wwwlogs/ -name "*.log" -mtime +30 -delete
```

### 4. 备份策略

#### 自动备份脚本
```bash
#!/bin/bash
# /root/backup.sh

DATE=$(date +%Y%m%d_%H%M%S)

# 备份数据库
mysqldump -u hawaiihub_net -pBkNR4w1KHrXX48by hawaiihub_net > /backup/db/hawaiihub_$DATE.sql
gzip /backup/db/hawaiihub_$DATE.sql

# 备份网站文件
tar -czf /backup/files/hawaiihub_$DATE.tar.gz /www/wwwroot/hawaiihub.net/

# 清理旧备份(保留30天)
find /backup/db/ -name "hawaiihub_*.sql.gz" -mtime +30 -delete
find /backup/files/ -name "hawaiihub_*.tar.gz" -mtime +30 -delete

echo "备份完成: $DATE"
```

### 5. 代码发布流程

#### 发布前检查清单
```
□ 代码测试通过
□ 数据库迁移脚本准备
□ 备份当前版本
□ 通知相关人员
□ 选择合适的发布时间(低峰期)
```

#### 发布步骤
```bash
# 1. 备份当前版本
cp -r /www/wwwroot/hawaiihub.net /backup/releases/hawaiihub_$(date +%Y%m%d_%H%M%S)

# 2. 上传新代码
rsync -av --exclude=uploads/ --exclude=templates_c/ new_code/ /www/wwwroot/hawaiihub.net/

# 3. 修复权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod 777 /www/wwwroot/hawaiihub.net/include/
chmod 777 /www/wwwroot/hawaiihub.net/templates_c/

# 4. 清理缓存
rm -rf /www/wwwroot/hawaiihub.net/templates_c/*

# 5. 验证发布
curl -I https://hawaiihub.net
```

### 6. 文档维护

#### 文档更新规范
- 系统配置变更时更新相关文档
- 新功能上线时补充操作说明
- 故障处理后记录解决方案
- 定期review文档内容的准确性

#### 文档存放位置
```
/documentation/
├── HawaiiHub-系统架构文档.md
├── HawaiiHub-API接口文档.md  
├── HawaiiHub-完整部署运维文档.md
├── HawaiiHub-配置详解文档.md
├── HawaiiHub-新人入职手册.md (本文档)
└── README.md
```

---

## 📞 联系方式

### 技术支持
- **技术负责人**: [姓名] - [电话] - [邮箱]
- **运维团队**: [群聊] - [邮箱]
- **紧急联系**: [24小时电话]

### 重要资源
- **服务器登录**: ssh root@*************
- **宝塔面板**: https://*************:8889
- **网站地址**: https://hawaiihub.net
- **管理后台**: https://hawaiihub.net/admin
- **API文档**: https://hawaiihub.net/api/doc

### 学习资源
- **火鸟门户官方文档**: [官网链接]
- **PHP官方文档**: https://www.php.net/docs.php
- **MySQL官方文档**: https://dev.mysql.com/doc/
- **Nginx官方文档**: https://nginx.org/en/docs/

---

## 🎯 入职任务清单

### 第一周任务
```
□ 完成服务器SSH连接配置
□ 熟悉宝塔面板操作界面
□ 掌握网站目录结构
□ 学习数据库连接和基本操作
□ 完成第一次日志分析
□ 运行系统监控脚本
□ 完成文档阅读并提出问题
```

### 第二周任务
```
□ 独立完成一次系统检查
□ 学习故障排查流程
□ 掌握文件权限管理
□ 完成数据库备份操作
□ 了解网站核心配置文件
□ 参与一次代码发布流程
□ 编写个人学习笔记
```

### 第三周目标
```
□ 能够独立处理常见故障
□ 掌握性能监控和优化
□ 熟悉安全防护措施
□ 可以进行日常维护工作
□ 具备应急响应能力
□ 完成知识转移和分享
```

---

**欢迎加入HawaiiHub团队！**  
*有任何问题随时联系技术负责人，我们一起让夏威夷华人平台更好！*

---

**文档版本**: v1.0  
**最后更新**: 2025年6月17日  
**编写人员**: 系统运维团队