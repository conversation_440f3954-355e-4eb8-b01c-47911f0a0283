# 🔌 HawaiiHub API接口文档

## 📋 目录
- [接口概述](#接口概述)
- [认证机制](#认证机制)
- [通用规范](#通用规范)
- [用户接口](#用户接口)
- [商业接口](#商业接口)
- [内容接口](#内容接口)
- [支付接口](#支付接口)
- [上传接口](#上传接口)
- [第三方接口](#第三方接口)

---

## 🌟 接口概述

### Base URL
- **生产环境**: `https://hawaiihub.net/api/`
- **测试环境**: `https://test.hawaiihub.net/api/`

### 接口架构
```
api/
├── login.php              # 用户认证
├── uc.php                 # 用户中心
├── bbs/                   # 论坛相关
├── live/                  # 直播相关
├── payment/               # 支付相关
├── upload/                # 文件上传
├── weixin/                # 微信集成
├── map/                   # 地图服务
└── handlers/              # 业务处理
```

---

## 🔐 认证机制

### Token认证
```http
Authorization: Bearer {access_token}
```

### 获取Token
```http
POST /api/login.php
Content-Type: application/json

{
    "username": "string",
    "password": "string",
    "type": "web|mobile|wechat"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires": 7200,
        "userInfo": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "level": 1
        }
    }
}
```

---

## 📝 通用规范

### 请求格式
- **Content-Type**: `application/json` 或 `multipart/form-data`
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DD HH:mm:ss)

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2025-06-17 08:30:00"
}
```

### 状态码
```
200: 成功
400: 请求参数错误
401: 未授权
403: 禁止访问
404: 资源不存在
500: 服务器内部错误
```

---

## 👤 用户接口

### 用户登录
```http
POST /api/login.php
```

**请求参数:**
```json
{
    "username": "string",      // 用户名/邮箱/手机号
    "password": "string",      // 密码
    "type": "web",            // 登录类型: web|mobile|wechat
    "captcha": "string"       // 验证码(可选)
}
```

### 用户注册
```http
POST /api/register.php
```

**请求参数:**
```json
{
    "username": "string",
    "password": "string",
    "email": "string",
    "mobile": "string",
    "sms_code": "string"
}
```

### 获取用户信息
```http
GET /api/uc.php?action=getUserInfo
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "mobile": "***********",
        "avatar": "https://hawaiihub.net/uploads/avatar/1.jpg",
        "level": 1,
        "points": 100,
        "status": 1
    }
}
```

### 更新用户信息
```http
PUT /api/uc.php?action=updateProfile
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "real_name": "string",
    "gender": 1,
    "birthday": "1990-01-01",
    "city": "Honolulu",
    "bio": "string"
}
```

---

## 🏢 商业接口

### 获取商家列表
```http
GET /api/business/list.php
```

**查询参数:**
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)
- `category`: 分类
- `city`: 城市
- `keyword`: 关键词

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "list": [
            {
                "id": 1,
                "name": "夏威夷中餐厅",
                "category": "餐饮",
                "logo": "https://hawaiihub.net/uploads/business/1.jpg",
                "address": "Honolulu, HI",
                "phone": "******-123-4567",
                "rating": 4.5
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 20
    }
}
```

### 商家详情
```http
GET /api/business/detail.php?id={business_id}
```

### 创建商家
```http
POST /api/business/create.php
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "name": "string",
    "category": "string",
    "description": "string",
    "address": "string",
    "phone": "string",
    "email": "string",
    "website": "string",
    "images": ["url1", "url2"]
}
```

---

## 📰 内容接口

### 论坛相关接口

#### 获取帖子列表
```http
GET /api/bbs/list.php
```

**查询参数:**
- `category`: 分类
- `page`: 页码
- `limit`: 每页数量
- `sort`: 排序方式 (latest|hot|replies)

#### 发布帖子
```http
POST /api/bbs/post.php
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "category": "string",
    "title": "string",
    "content": "string",
    "images": ["url1", "url2"]
}
```

#### 回复帖子
```http
POST /api/bbs/reply.php
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "post_id": 1,
    "content": "string",
    "reply_to": 0
}
```

### 直播相关接口

#### 获取直播列表
```http
GET /api/live/list.php
```

#### 创建直播间
```http
POST /api/live/create.php
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "title": "string",
    "description": "string",
    "cover_image": "string",
    "start_time": "2025-06-17 20:00:00"
}
```

---

## 💳 支付接口

### 创建支付订单
```http
POST /api/payment/create.php
Authorization: Bearer {token}
```

**请求参数:**
```json
{
    "type": "wechat|alipay|stripe|paypal",
    "amount": 99.99,
    "currency": "USD",
    "order_no": "ORDER202506170001",
    "description": "商品购买",
    "return_url": "https://hawaiihub.net/payment/return",
    "notify_url": "https://hawaiihub.net/api/payment/notify.php"
}
```

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "payment_id": "PAY202506170001",
        "payment_url": "https://api.stripe.com/v1/checkout/sessions/...",
        "qr_code": "data:image/png;base64,..."
    }
}
```

### 查询支付状态
```http
GET /api/payment/status.php?order_no={order_no}
Authorization: Bearer {token}
```

### 支付回调处理
```http
POST /api/payment/notify.php
```

**微信支付回调:**
```xml
<xml>
    <appid>wx123456789</appid>
    <mch_id>1234567890</mch_id>
    <nonce_str>abc123</nonce_str>
    <sign>ABC123DEF456</sign>
    <result_code>SUCCESS</result_code>
    <out_trade_no>ORDER202506170001</out_trade_no>
    <total_fee>9999</total_fee>
</xml>
```

---

## 📤 上传接口

### 图片上传
```http
POST /api/upload/image.php
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数:**
- `file`: 图片文件
- `type`: 上传类型 (avatar|post|business)
- `resize`: 是否压缩 (0|1)

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "url": "https://hawaiihub.net/uploads/images/2025/06/17/abc123.jpg",
        "filename": "abc123.jpg",
        "size": 102400,
        "width": 800,
        "height": 600
    }
}
```

### 视频上传
```http
POST /api/upload/video.php
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数:**
- `file`: 视频文件
- `type`: 上传类型 (live|post|business)
- `duration`: 视频时长(秒)

### 文档上传
```http
POST /api/upload/document.php
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**支持格式:**
- PDF: .pdf
- Office: .doc, .docx, .xls, .xlsx, .ppt, .pptx
- 压缩包: .zip, .rar, .7z

---

## 🔗 第三方接口

### 微信接口

#### 微信登录
```http
POST /api/weixin/login.php
```

**请求参数:**
```json
{
    "code": "string",      // 微信授权码
    "type": "miniprogram|official"
}
```

#### 微信支付
```http
POST /api/weixin/pay.php
Authorization: Bearer {token}
```

#### 微信图片上传
```http
POST /api/weixinImageUpload.php
Authorization: Bearer {token}
```

### 地图接口

#### 地址解析
```http
GET /api/map/geocode.php?address={address}
```

#### 距离计算
```http
GET /api/map/distance.php?from={lat1,lng1}&to={lat2,lng2}
```

### 短信接口

#### 发送验证码
```http
POST /api/sms/send.php
```

**请求参数:**
```json
{
    "mobile": "***********",
    "type": "register|login|forgot"
}
```

#### 验证短信码
```http
POST /api/sms/verify.php
```

**请求参数:**
```json
{
    "mobile": "***********",
    "code": "123456",
    "type": "register|login|forgot"
}
```

---

## 🔍 搜索接口

### 综合搜索
```http
GET /api/search.php
```

**查询参数:**
- `q`: 搜索关键词
- `type`: 搜索类型 (all|business|post|user)
- `page`: 页码
- `limit`: 每页数量

**响应示例:**
```json
{
    "code": 200,
    "data": {
        "businesses": [],
        "posts": [],
        "users": [],
        "total": 50,
        "took": 0.05
    }
}
```

---

## 📊 统计接口

### 用户统计
```http
GET /api/stats/user.php
Authorization: Bearer {token}
```

### 访问统计
```http
POST /api/stats/visit.php
```

**请求参数:**
```json
{
    "page": "string",
    "referrer": "string",
    "user_agent": "string"
}
```

---

## 🚨 错误处理

### 业务错误码
```
1001: 用户名或密码错误
1002: 用户不存在
1003: 用户已被禁用
1004: 验证码错误
1005: 手机号已存在
1006: 邮箱已存在
1007: 密码强度不够
1008: 验证码已过期

2001: 商品不存在
2002: 库存不足
2003: 订单不存在
2004: 订单状态错误
2005: 商家不存在
2006: 商家未审核

3001: 支付失败
3002: 支付超时
3003: 支付金额错误
3004: 支付方式不支持
3005: 余额不足

4001: 文件格式不支持
4002: 文件大小超限
4003: 上传失败
4004: 图片处理失败

5001: 第三方接口调用失败
5002: 微信接口错误
5003: 短信发送失败
5004: 邮件发送失败
```

### 错误响应示例
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": null,
    "timestamp": "2025-06-17 08:30:00"
}
```

---

## 📝 开发示例

### JavaScript调用示例
```javascript
// 用户登录
async function login(username, password) {
    try {
        const response = await fetch('/api/login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password,
                type: 'web'
            })
        });
        
        const result = await response.json();
        if (result.code === 200) {
            localStorage.setItem('token', result.data.token);
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('登录失败:', error);
    }
}

// 带Token的API调用
async function apiCall(url, options = {}) {
    const token = localStorage.getItem('token');
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return fetch(url, {
        ...options,
        headers: headers
    });
}
```

### PHP调用示例
```php
<?php
// 用户登录
function login($username, $password) {
    $data = [
        'username' => $username,
        'password' => $password,
        'type' => 'web'
    ];
    
    $options = [
        'http' => [
            'header' => "Content-Type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents('/api/login.php', false, $context);
    
    return json_decode($result, true);
}

// 带Token的API调用
function apiCall($url, $data = null, $token = null) {
    $headers = ['Content-Type: application/json'];
    
    if ($token) {
        $headers[] = "Authorization: Bearer $token";
    }
    
    $options = [
        'http' => [
            'header' => implode("\r\n", $headers) . "\r\n",
            'method' => $data ? 'POST' : 'GET'
        ]
    ];
    
    if ($data) {
        $options['http']['content'] = json_encode($data);
    }
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}
?>
```

---

**文档版本**: v1.0  
**最后更新**: 2025年6月17日  
**联系方式**: <EMAIL>