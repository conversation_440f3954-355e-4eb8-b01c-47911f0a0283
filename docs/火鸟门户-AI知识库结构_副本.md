# 🔥 火鸟门户 - AI知识库结构

> **创建时间**: 2025-01-17  
> **目标**: 为AI提供完整的火鸟门户技术知识，实现准确的问答支持  
> **使用方式**: 导入Notion，配合官方MCP实现AI查询

---

## 📊 知识库架构设计

### 🗃️ 主数据库：系统架构文档
**用途**: 存储所有技术文档和配置信息

| 字段名 | 类型 | 选项 | 说明 |
|--------|------|------|------|
| 名称 | 标题 | - | 文档标题 |
| 分类 | 选择 | 系统架构/API接口/插件系统/服务器配置/数据库/安全配置 | 文档分类 |
| 重要性 | 选择 | 高/中/低 | 重要程度 |
| 状态 | 选择 | 已完成/进行中/待更新 | 维护状态 |
| 标签 | 多选 | PHP/MySQL/API/插件/宝塔/SSH | 技术标签 |
| 描述 | 富文本 | - | 简短描述 |
| 最后更新 | 日期 | - | 更新时间 |

### 🗃️ 辅助数据库：常见问题FAQ
**用途**: 存储常见技术问题和解决方案

| 字段名 | 类型 | 选项 | 说明 |
|--------|------|------|------|
| 问题 | 标题 | - | 问题描述 |
| 类别 | 选择 | 系统问题/API问题/插件问题/服务器问题/开发问题 | 问题分类 |
| 紧急程度 | 选择 | 紧急/重要/一般 | 优先级 |
| 解决状态 | 选择 | 已解决/部分解决/未解决 | 解决状态 |
| 相关文档 | 关联 | - | 关联主文档 |
| 关键词 | 多选 | - | 搜索关键词 |

---

## 📚 知识库内容清单

### 1️⃣ 系统架构文档

#### 🏗️ 火鸟门户系统概览
**分类**: 系统架构 | **重要性**: 高 | **状态**: 已完成

**内容**:
```markdown
# 火鸟门户系统架构

## 基本信息
- **开发商**: 杭州酷曼软件技术有限公司
- **产品名**: 火鸟门户系统
- **版本**: 企业版
- **域名**: hawaiihub.net (夏威夷华人平台)

## 技术栈
- **后端**: PHP 7.4 + MySQL 8.0
- **前端**: HTML5 + CSS3 + JavaScript + Layui
- **服务器**: Nginx + 宝塔面板
- **框架**: 自研MVC架构
- **模板引擎**: 自研模板引擎(类Smarty语法)

## 核心模块
1. **用户系统**: 注册/登录/认证/会员管理
2. **商业服务**: 商家入驻/商品管理/支付系统(19种支付方式)
3. **生活服务**: 房产/汽车/招聘/教育/家政/婚恋/旅游
4. **社区功能**: 论坛/社交圈/直播/视频/贴吧
5. **工具服务**: 黄页/全景/积分/投票/海报生成

## 目录结构
- **前台**: templates/ (50个模板模块)
- **后台**: admin/ (40个管理模块)  
- **API**: api/ (11个服务模块)
- **核心**: include/ (系统核心库)
- **静态**: static/ (CSS/JS/图片资源)

## 数据库设计
- **表数量**: 490张数据表
- **表前缀**: hn_
- **核心表**: 用户表、内容表、商家表、订单表等
```

#### 🔌 插件系统架构
**分类**: 插件系统 | **重要性**: 高 | **状态**: 已完成

**内容**:
```markdown
# 插件系统架构

## 插件目录结构
```
/include/plugins/{插件ID}/
├── index.php          # 主入口文件
├── config.inc.php     # 配置文件
├── setting.php        # 设置选项
├── api.php           # API接口
├── service/          # 服务层目录
├── tpl/              # 模板目录
│   ├── *.html        # 界面模板
│   ├── css/          # 样式文件
│   └── js/           # 脚本文件
├── static/           # 静态资源
└── cache/            # 缓存目录
```

## 主要插件
### 插件 #4 - 信息资讯采集插件
- **功能**: 自动采集外部网站新闻资讯
- **核心文件**: common.php (12.2KB), getNews.php (10.5KB)
- **特性**: 多站点采集、智能提取、缓存优化

### 插件 #5 - 一键转载插件  
- **功能**: 快速转载和复制内容
- **核心服务**: HttpDownService.php, ImagesService.php
- **特性**: 图片自动下载、格式转换

### 插件 #26 - AI智能插件
- **功能**: AI内容生成和智能处理
- **配置项**: AI平台选择、模型配置、API密钥
- **应用模块**: info,job (资讯和招聘)

## 开发规范
1. **统一入口**: 所有插件必须有index.php主入口
2. **权限控制**: 集成系统权限验证机制
3. **模板分离**: 界面与逻辑代码分离
4. **错误处理**: 标准JSON格式返回
5. **数据库集成**: 与主系统数据库无缝对接
```

#### 🌐 API接口文档
**分类**: API接口 | **重要性**: 高 | **状态**: 已完成

**内容**:
```markdown
# API接口规范

## 认证机制
- **登录接口**: /api/login.php
- **认证方式**: Bearer Token
- **用户验证**: $userLogin->getUserID()

## 核心控制器
### 用户管理 (/api/handlers/member.controller.php)
- `getUserInfo` - 获取用户信息
- `register` - 用户注册  
- `updateProfile` - 更新资料
- `deleteUser` - 删除用户

### 内容管理 (/api/handlers/article.controller.php)
- `list` - 文章列表
- `detail` - 文章详情
- `create` - 创建文章
- `update` - 更新文章
- `delete` - 删除文章

### 商家管理 (/api/handlers/business.controller.php)
- `list` - 商家列表
- `detail` - 商家详情
- `create` - 创建商家
- `update` - 更新商家
- `delete` - 删除商家

## 支付系统
### 支持的支付方式 (19种)
- 支付宝、微信支付、银联支付
- PayPal、Stripe等国际支付
- 各类银行网银支付

### 支付接口
- `/api/payment/` - 支付处理
- `/api/qrPay/` - 二维码支付
- `/api/miniReturnPay/` - 小程序支付

## 文件上传
- **图片上传**: /api/upload/image.php
- **音频上传**: /api/weixinAudioUpload.php
- **文件上传**: /api/upload/file.php

## 响应格式
```json
{
  "code": 200,        // 200=成功, 其他=失败
  "message": "success",
  "data": {}          // 返回数据
}
```
```

#### 🖥️ 服务器配置
**分类**: 服务器配置 | **重要性**: 中 | **状态**: 已完成

**内容**:
```markdown
# 服务器配置详情

## 基本信息
- **IP地址**: *************
- **管理面板**: 宝塔面板
- **SSH访问**: ssh baota (免密登录已配置)
- **操作系统**: Linux
- **Web服务器**: Nginx + Apache

## 环境配置
- **PHP版本**: 7.4
- **MySQL版本**: 8.0
- **Redis**: 已安装(缓存)
- **SSL证书**: 已配置

## 目录结构
- **网站根目录**: /www/wwwroot/hawaiihub.net/
- **日志目录**: /www/wwwlogs/
- **备份目录**: /www/backup/
- **数据库**: /www/server/data/

## SSH配置
- **密钥文件**: *************_id_ed25519
- **连接命令**: ssh baota
- **用户权限**: root级别访问

## 安全配置
- SSH密钥认证(已禁用密码登录)
- 防火墙配置
- SSL证书自动续期
- 定期安全扫描
```

### 2️⃣ 常见问题FAQ

#### ❓ 如何通过API创建文章？
**类别**: API问题 | **紧急程度**: 重要

**解决方案**:
```markdown
## API创建文章步骤

1. **用户认证**
```bash
POST /api/login.php
{
  "username": "your_username",
  "password": "your_password"
}
```

2. **创建文章**
```bash
POST /api/handlers/article.controller.php?action=create
Headers: Authorization: Bearer {token}
{
  "title": "文章标题",
  "content": "文章内容",
  "category_id": 1,
  "author": "作者名称"
}
```

3. **响应示例**
```json
{
  "code": 200,
  "message": "文章创建成功",
  "data": {
    "article_id": 123,
    "url": "https://hawaiihub.net/article/123"
  }
}
```
```

#### ❓ 如何配置AI插件？
**类别**: 插件问题 | **紧急程度**: 重要

**解决方案**:
```markdown
## AI插件配置步骤

1. **访问插件管理**
- 后台地址: /admin/siteConfig/plugins.php
- 找到插件#26 (AI智能插件)

2. **基础配置**
- AI平台选择: 0=OpenAI, 1=百度, 2=阿里云
- 模型设置: gpt-3.5-turbo / gpt-4
- API密钥: 填入对应平台的API Key

3. **应用模块**
- info: 资讯模块AI生成
- job: 招聘信息AI优化

4. **配置文件位置**
- /include/plugins/26/config.inc.php
```

#### ❓ 如何SSH登录服务器？
**类别**: 服务器问题 | **紧急程度**: 一般

**解决方案**:
```markdown
## SSH登录方法

1. **直接命令**(已配置免密)
```bash
ssh baota
```

2. **完整命令**
```bash
ssh -i *************_id_ed25519 root@*************
```

3. **宝塔面板访问**
- 通过SSH登录后执行: bt
- 获取面板地址和登录信息

4. **常用目录**
- 网站文件: cd /www/wwwroot/hawaiihub.net/
- 日志查看: cd /www/wwwlogs/
- 数据库: cd /www/server/data/
```

---

## 🎯 AI查询优化建议

### 🔍 关键词索引
为提高AI查询准确性，建议在Notion中为每个文档添加以下标签：

**技术标签**:
- `PHP`, `MySQL`, `API`, `插件`, `宝塔`, `SSH`
- `用户管理`, `内容管理`, `商家管理`, `支付系统`
- `新闻采集`, `AI插件`, `一键转载`

**功能标签**:
- `增删改查`, `用户认证`, `文件上传`, `数据库操作`
- `服务器配置`, `SSL证书`, `安全设置`

**问题标签**:
- `常见问题`, `故障排除`, `性能优化`, `安全配置`

### 📝 搜索优化
1. **标题规范**: 使用"如何..."、"什么是..."、"怎样..."等疑问句式
2. **内容结构**: 采用标准的问题-解决方案格式
3. **代码示例**: 提供完整的代码片段和配置示例
4. **关联链接**: 建立文档间的关联关系

---

## 🚀 使用说明

### 导入Notion步骤
1. **重启Cursor**: 确保Notion官方MCP已加载
2. **创建workspace**: 在Notion中创建"火鸟门户知识库"工作区
3. **导入结构**: 使用MCP工具创建数据库结构
4. **填充内容**: 将本文档内容导入对应页面
5. **设置权限**: 配置AI访问权限

### AI查询测试
导入完成后，可以测试以下查询：
- "如何通过API创建文章？"
- "火鸟门户有哪些插件？"
- "服务器SSH登录方式是什么？"
- "支付系统支持哪些支付方式？"

---

**📞 技术支持**: 如有问题，请参考宝塔面板日志或联系系统管理员 