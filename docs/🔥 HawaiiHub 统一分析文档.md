# 🔥 HawaiiHub 统一分析文档

## 📋 项目概述

**项目名称**: HawaiiHub 夏威夷华人平台 (底层为 火鸟门户系统 - HuoNiaoCMS)
**开发商**: 苏州酷曼软件技术有限公司 (https://www.kumanyun.com)
**系统类型**: 基于自研MVC框架的PHP门户系统
**应用场景**: 面向夏威夷华人群体的综合性生活服务平台

### 特点
- **多端同步**：支持APP、微信、小程序、H5、电脑
- **多城市分站**：可根据不同城市建立子站点
- **核心文件加密**：使用 Swoole Loader 加密核心业务逻辑
- **模块化设计**：功能模块独立，便于维护和扩展

---

## 📊 系统规模统计

| 项目 | 数量/大小 | 说明 |
|------|-----------|------|
| 总文件数 | 21,100+ | 包含完整系统的所有文件 |
| PHP文件数 | 3,720+ | 核心业务逻辑文件 |
| 数据库表数 | 490张 | `hn_`前缀命名规范 |
| 核心安装包 | 3.3MB | 启动器和基础文件 |
| 完整系统包 | 268MB | `system.zip`解压后内容 |

---

## 🏗️ 系统架构

### 技术栈
- **后端**: PHP 7.4+, 自研MVC框架, MySQL 5.7+, Redis (可选)
- **前端**: HTML5, CSS3, JavaScript (jQuery, Layui, Vue.js)
- **核心加密**: `swoole_loader` 扩展授权
- **部署**: Apache/Nginx + PHP-FPM

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Web浏览器      │   微信小程序      │      移动APP              │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       负载均衡层                               │
│                    Nginx / Apache                            │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       应用层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    前端展示层     │     API接口层     │      管理后台层           │
│   Templates      │       API        │       Admin             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       业务逻辑层                               │
│                     Include 核心库                           │
├─────────────────┬─────────────────┬─────────────────────────┤
│   业务处理核心   │    工具类库      │      第三方集成           │
│  Common.func     │     Class       │      Vendor             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       数据层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    MySQL数据库   │    文件存储      │      缓存系统             │
│                 │    Uploads       │      Redis              │
└─────────────────┴─────────────────┴─────────────────────────┘
```

---

## 📁 目录结构

### 根目录 (`hawaiihub.net/`)
```
hawaiihub.net/
├── admin/                  # 管理后台 (40个功能模块)
├── api/                    # API接口层 (11个服务模块)
├── data/                   # 数据文件存储
├── design/                 # 设计资源文件
├── include/                # 核心功能库 (14个子目录)
├── log/                    # 系统日志文件
├── static/                 # 静态资源 (9个资源类型)
├── templates/              # 前端模板 (50个模板模块)
├── templates_c/            # 编译模板缓存
├── uploads/                # 用户上传文件
├── wmsj/                   # 系统功能目录 (外卖商家)
├── index.php               # 网站入口文件
├── huoniao.php             # 火鸟核心配置
├── huoniao.dll / huoniao.so # 核心加密库
└── .htaccess               # Apache重写规则
```

---

## 🔧 核心功能模块

### 1. 管理后台模块 (`admin/`)
- **系统管理**: 网站配置、权限管理、数据备份
- **内容管理**: 文章、论坛、评论等
- **用户管理**: 会员、管理员、等级、用户组
- **商业功能**: 商家、商城、订单、支付
- **生活服务**: 房产、招聘、二手车、婚恋
- **社区功能**: 圈子、直播、视频
- **营销推广**: 活动、优惠券、广告
- **数据统计**: 概览、用户、内容、流量、收入分析

### 2. API接口模块 (`api/`)
- **Base URL**: `https://hawaiihub.net/api/`
- **认证**: Token认证 (`Authorization: Bearer {access_token}`)
- **核心服务**:
  - `login.php`: 用户认证
  - `handlers/`: 核心业务处理器 (siteConfig, member, business)
  - `payment/`: 支付集成 (Alipay, WxPay, PayPal等)
  - `upload/`: 文件上传 (Qiniu, Aliyun, Tencent Cloud等)
  - `login/`: 第三方登录 (WeChat, QQ, Facebook等)

### 3. 核心类库 (`include/`)
- `common.inc.php`: 系统核心配置
- `dsql.class.php`: 数据库操作类 (PDO)
- `userLogin.class.php`: 用户认证体系
- `file.class.php`: 文件操作类
- `payment.class.php`: 支付处理类
- **云服务集成**: Aliyun, Huawei, Tencent, Qiniu SDKs

---

## 🔌 API接口总览

| Service | Action | 需登录 | 文件路径 |
|---|---|---|---|
| `siteConfig` | `type` | 是 | `.../api/handlers/siteConfig.controller.php` |
| `member` | `record`, `point`, `bill`, `login`, `register`, `order`, etc. | 是 | `.../api/handlers/member.controller.php` |
| `business` | `typeid`, `desk` | 是 | `.../api/handlers/business.controller.php` |

*这是一个简化的列表，完整的API列表非常庞大，涵盖了用户、内容、商业、支付等各个方面。*

---

## 📍 路径更新指南

由于项目文件已统一迁移到 `hawaiihub/` 目录，所有路径引用都需要更新。

**核心原则**: **`hawaiihub` 现在是唯一的项目入口！**

### 路径对照表示例

| 原路径 | 新路径 |
|---|---|
| `01-hawaiihub-platform` | `hawaiihub/` |
| `02-ai-automation/agents` | `hawaiihub/ai-agents/` |

### 需要更新的文件类型
- **配置文件**: `package.json`, `.env`, `config.json`
- **文档文件**: `*.md`
- **代码文件**: PHP `include`, JS `require`, Python `import`

---

## 🚀 部署与运维

- **服务器**: Linux (推荐 CentOS)
- **Web服务器**: Nginx 或 Apache
- **PHP**: 7.4+
- **数据库**: MySQL 5.7+
- **必需PHP扩展**: `swoole_loader`
- **SSH访问**: `ssh baota` (已配置免密登录到 `154.40.47.153`)
- **管理面板**: 宝塔面板