# 🏝️ HawaiiHub 夏威夷华人平台 - 系统架构文档

## 📋 目录
- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [目录结构](#目录结构)
- [核心模块](#核心模块)
- [API接口](#api接口)
- [数据库设计](#数据库设计)
- [部署说明](#部署说明)
- [运维指南](#运维指南)

---

## 🌟 系统概述

### 项目信息
- **项目名称**: HawaiiHub 夏威夷华人平台
- **项目类型**: 综合性华人生活服务平台
- **开发语言**: PHP 7.x+
- **框架类型**: 自研MVC框架
- **部署环境**: Linux + Nginx/Apache + MySQL + PHP

### 业务范围
HawaiiHub 是面向夏威夷华人群体的综合性生活服务平台，提供以下核心服务：

1. **商业服务**: 商家入驻、商品展示、团购、外卖
2. **生活服务**: 房产、汽车、招聘、教育、家政、婚恋、旅游
3. **社区功能**: 论坛、社交圈、直播、视频分享
4. **工具服务**: 黄页目录、全景展示、积分系统、投票
5. **支付系统**: 多种支付方式集成
6. **移动端**: 微信小程序、APP接口

---

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Web浏览器      │   微信小程序      │      移动APP              │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       负载均衡层                               │
│                    Nginx / Apache                            │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       应用层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    前端展示层     │     API接口层     │      管理后台层           │
│   Templates      │       API        │       Admin             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       业务逻辑层                               │
│                     Include 核心库                           │
├─────────────────┬─────────────────┬─────────────────────────┤
│   业务处理核心   │    工具类库      │      第三方集成           │
│  Common.func     │     Class       │      Vendor             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                       数据层                                   │
├─────────────────┬─────────────────┬─────────────────────────┤
│    MySQL数据库   │    文件存储      │      缓存系统             │
│                 │    Uploads       │      Redis              │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 技术栈详情

#### 后端技术
- **PHP 7.x+**: 主要开发语言
- **自研MVC框架**: 基于include核心库构建
- **MySQL**: 主数据库
- **Redis**: 缓存和会话存储（可选）
- **Composer**: 依赖管理

#### 前端技术
- **HTML5 + CSS3**: 基础标记和样式
- **JavaScript (ES6+)**: 前端交互逻辑
- **Layui**: 主要UI框架
- **jQuery**: DOM操作和AJAX
- **UEditor**: 富文本编辑器

#### 第三方集成
- **微信开放平台**: 小程序、公众号集成
- **支付系统**: 微信支付、支付宝等
- **地图服务**: 百度地图、高德地图
- **腾讯广告**: 广告投放系统
- **百度AI**: 图像识别、语音处理

---

## 📁 目录结构

### 根目录结构
```
hawaiihub.net/
├── admin/                  # 管理后台 (40个功能模块)
├── api/                    # API接口层 (11个服务模块)
├── data/                   # 数据文件存储
├── design/                 # 设计资源文件
├── include/                # 核心功能库 (14个子目录)
├── log/                    # 系统日志文件
├── static/                 # 静态资源 (9个资源类型)
├── templates/              # 前端模板 (50个模板模块)
├── templates_c/            # 编译模板缓存
├── uploads/                # 用户上传文件
├── wmsj/                   # 系统功能目录
├── index.php               # 网站入口文件 (76KB)
├── website.php             # 网站核心文件 (94KB)
├── special.php             # 特殊功能文件 (60KB)
├── huoniao.php             # 火鸟核心配置 (98B)
├── huoniao.dll             # Windows动态链接库 (482KB)
├── huoniao.so              # Linux共享库 (166KB)
├── huoniao                 # 可执行文件 (1.2KB)
├── .htaccess               # Apache重写规则
├── system_site_city.json   # 城市系统配置
└── 火鸟门户初始化安装包.zip # 安装包文件 (254MB)
```

### 权限设置
```bash
# 目录权限
drwxrwxrwx  hawaiihub.net/          # 755
drwxr-xr-x  admin/                  # 755  
drwxr-xr-x  api/                    # 755
drwxrwxrwx  include/                # 777 (需要写入权限)
drwxr-xr-x  static/                 # 755
drwxr-xr-x  templates/              # 755
drwxrwx--x  templates_c/            # 771 (编译缓存)
drwxr-xr-x  uploads/                # 755

# 文件权限
-rwxrwxrwx  huoniao.php             # 777 (配置文件)
-rwxr-xr-x  huoniao.dll             # 755 (可执行库)
-rwxr-xr-x  huoniao.so              # 755 (共享库)
-rw-r--r--  *.php                   # 644 (一般PHP文件)
```

---

## 🔧 核心模块

### 1. Admin 管理后台模块

#### 主要功能
- **系统管理**: 网站配置、权限管理、数据备份
- **内容管理**: 文章发布、图片管理、模板编辑
- **用户管理**: 会员管理、权限分配、积分系统
- **商业管理**: 商家审核、订单管理、财务统计
- **数据分析**: 访问统计、用户行为分析

#### 核心文件
```php
admin/
├── index.php               # 管理后台主控制台 (189KB)
├── login.php               # 管理员登录验证 (6.5KB)
├── siteConfig/             # 网站配置管理
├── member/                 # 会员管理
├── business/               # 商家管理
├── templates/              # 后台模板 (38个子模板)
└── funSearch.php           # 功能搜索 (6.8KB)
```

#### 业务模块详情
```
商业服务模块:
├── business/               # 商家管理
├── shop/                   # 商店管理  
├── tuan/                   # 团购管理
└── waimai/                 # 外卖管理

生活服务模块:
├── car/                    # 汽车交易
├── house/                  # 房产信息
├── job/                    # 招聘求职
├── education/              # 教育培训
├── homemaking/             # 家政服务
├── marry/                  # 婚恋交友
└── travel/                 # 旅游服务

社区功能模块:
├── circle/                 # 社交圈子
├── tieba/                  # 贴吧论坛
├── live/                   # 直播功能
└── video/                  # 视频内容

专业服务模块:
├── huangye/                # 黄页目录
├── renovation/             # 装修服务
├── pension/                # 养老服务
└── quanjing/               # 全景展示
```

### 2. API 接口层

#### 接口架构
```php
api/
├── 核心认证
│   ├── login.php           # 用户登录API
│   ├── uc.php              # 用户中心API (6.2KB)
│   └── appConfig.json      # 应用配置 (4.5KB)
├── 支付系统
│   ├── payment/            # 支付接口 (19种支付方式)
│   ├── qrPay.php           # 二维码支付 (8.7KB)
│   └── miniReturnPay.php   # 小程序支付回调 (7.6KB)
├── 媒体处理
│   ├── upload/             # 文件上传API (6个处理器)
│   ├── weixinImageUpload.php # 微信图片上传 (23KB)
│   └── weixinAudioUpload.php # 微信音频上传 (13KB)
├── 第三方服务
│   ├── weixin/             # 微信接口集成
│   ├── map/                # 地图服务API
│   └── tencent_gdt_notify.php # 腾讯广告回调 (2.7KB)
└── 业务接口
    ├── bbs/                # 论坛API (4个子接口)
    ├── live/               # 直播API (3个子接口)
    ├── print/              # 打印服务API (6个报表接口)
    └── handlers/           # 业务处理器 (2个处理器)
```

#### 支付系统详情
```
payment/ (19种支付方式)
├── 微信支付
│   ├── wechat/             # 微信H5支付
│   ├── wechatPay/          # 微信原生支付
│   └── wechatAppPay/       # 微信APP支付
├── 支付宝
│   ├── alipay/             # 支付宝网页支付
│   ├── alipayApp/          # 支付宝APP支付
│   └── alipayWap/          # 支付宝手机网页支付
├── 银行支付
│   ├── unionpay/           # 银联支付
│   ├── yeepay/             # 易宝支付
│   └── paypal/             # PayPal国际支付
└── 其他支付
    ├── stripe/             # Stripe支付
    ├── square/             # Square支付
    └── ...                 # 其他支付方式
```

### 3. Templates 模板系统

#### 模板架构
```php
templates/ (50个模板模块)
├── 用户系统模板
│   ├── member/             # 会员中心 (8个子模板)
│   ├── certification/      # 认证相关 (6个模板)
│   ├── feedback/           # 反馈系统 (5个模板)
│   └── notice/             # 通知消息 (5个模板)
├── 商业服务模板
│   ├── business/           # 商家展示 (4个模板)
│   ├── shop/               # 商店页面 (4个模板)
│   ├── tuan/               # 团购页面 (4个模板)
│   └── supplier/           # 供应商 (4个模板)
├── 生活服务模板
│   ├── car/                # 汽车相关 (4个模板)
│   ├── house/              # 房产页面 (4个模板)
│   ├── job/                # 招聘页面 (4个模板)
│   ├── education/          # 教育页面 (4个模板)
│   ├── homemaking/         # 家政页面 (4个模板)
│   ├── marry/              # 婚恋页面 (4个模板)
│   └── travel/             # 旅游页面 (4个模板)
├── 社区功能模板
│   ├── circle/             # 社交圈 (4个模板)
│   ├── tieba/              # 贴吧 (4个模板)
│   ├── live/               # 直播 (4个模板)
│   └── video/              # 视频 (4个模板)
└── 工具页面模板
    ├── about/              # 关于我们 (5个模板)
    ├── help/               # 帮助中心 (6个模板)
    ├── protocol/           # 协议条款 (5个模板)
    └── poster/             # 海报生成 (3个模板)
```

### 4. Include 核心功能库

#### 核心架构
```php
include/ (877KB 核心代码)
├── 系统核心
│   ├── common.func.php     # 公共函数库 (877KB)
│   ├── common.inc.php      # 公共配置 (78KB)
│   ├── kernel.inc.php      # 系统内核 (198KB)
│   └── website.inc.php     # 网站核心 (53KB)
├── 业务处理
│   ├── class/              # 面向对象类库 (12个类)
│   ├── ajax.php            # AJAX处理器 (17KB)
│   ├── loop.php            # 循环处理器 (35KB)
│   └── special.inc.php     # 特殊功能 (31KB)
├── 文件处理
│   ├── upload.inc.php      # 文件上传核心 (135KB)
│   ├── cropupload.php      # 图片裁剪 (14KB)
│   └── attachment.php      # 附件管理 (5KB)
├── 第三方集成
│   ├── vendor/             # Composer扩展包 (14个包)
│   ├── phpqrcode/          # 二维码生成库 (6个文件)
│   ├── ueditor/            # 富文本编辑器 (7个目录)
│   └── pdf/                # PDF处理库 (5个文件)
├── 多语言支持
│   ├── lang/               # 语言包 (4种语言)
│   │   ├── zh-cn/          # 简体中文
│   │   ├── zh-tw/          # 繁体中文
│   │   ├── en/             # 英文
│   │   └── jp/             # 日文
└── 配置管理
    ├── config/             # 系统配置文件 (2个配置)
    ├── data/               # 数据文件 (5个数据目录)
    └── plugins/            # 插件系统 (5个插件)
```

#### 核心类库详情
```php
class/ (12个核心类)
├── Database.class.php      # 数据库操作类
├── Template.class.php      # 模板引擎类
├── Cache.class.php         # 缓存管理类
├── User.class.php          # 用户管理类
├── Upload.class.php        # 文件上传类
├── Image.class.php         # 图片处理类
├── Payment.class.php       # 支付处理类
├── Wechat.class.php        # 微信接口类
├── SMS.class.php           # 短信服务类
├── Email.class.php         # 邮件服务类
├── Log.class.php           # 日志记录类
└── Security.class.php      # 安全验证类
```

---

## 🔌 API接口

### RESTful API 设计

#### 认证接口
```php
POST /api/login.php
# 用户登录
Request: {
    "username": "string",
    "password": "string",
    "type": "web|mobile|wechat"
}
Response: {
    "code": 200,
    "message": "success",
    "data": {
        "token": "string",
        "userInfo": {...}
    }
}

GET /api/uc.php?action=getUserInfo
# 获取用户信息
Headers: Authorization: Bearer {token}
Response: {
    "code": 200,
    "data": {
        "id": "int",
        "username": "string",
        "avatar": "string",
        "level": "int"
    }
}
```

#### 业务接口
```php
# 论坛相关
GET /api/bbs/list.php           # 获取帖子列表
POST /api/bbs/post.php          # 发布帖子
PUT /api/bbs/edit.php           # 编辑帖子
DELETE /api/bbs/delete.php      # 删除帖子

# 直播相关
GET /api/live/list.php          # 获取直播列表
POST /api/live/create.php       # 创建直播间
PUT /api/live/update.php        # 更新直播信息

# 文件上传
POST /api/upload/image.php      # 图片上传
POST /api/upload/video.php      # 视频上传
POST /api/upload/document.php   # 文档上传
```

#### 支付接口
```php
POST /api/payment/create.php
# 创建支付订单
Request: {
    "amount": "decimal",
    "type": "wechat|alipay|unionpay",
    "orderNo": "string",
    "description": "string"
}

GET /api/payment/status.php?orderNo={orderNo}
# 查询支付状态

POST /api/payment/notify.php
# 支付回调接口
```

### 错误码规范
```php
// 通用错误码
200: 成功
400: 请求参数错误
401: 未授权
403: 禁止访问
404: 资源不存在
500: 服务器内部错误

// 业务错误码
1001: 用户名或密码错误
1002: 用户不存在
1003: 用户已被禁用
1004: 验证码错误
1005: 手机号已存在

2001: 商品不存在
2002: 库存不足
2003: 订单不存在
2004: 订单状态错误

3001: 支付失败
3002: 支付超时
3003: 支付金额错误
```

---

## 🗄️ 数据库设计

### 数据库连接配置
```php
// include/dbinfo.inc.php (328字节)
<?php
define('DB_HOST', 'localhost');      // 数据库主机
define('DB_NAME', 'hawaiihub');      // 数据库名
define('DB_USER', 'root');           // 用户名
define('DB_PASS', 'password');       // 密码
define('DB_PORT', '3306');           // 端口
define('DB_CHARSET', 'utf8mb4');     // 字符集
?>
```

### 核心数据表结构

#### 用户系统表
```sql
-- 用户基本信息表
CREATE TABLE `hn_users` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `username` varchar(50) UNIQUE NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100),
  `mobile` varchar(20),
  `avatar` varchar(255),
  `status` tinyint(1) DEFAULT 1,
  `level` int(11) DEFAULT 1,
  `points` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户详细信息表
CREATE TABLE `hn_user_profiles` (
  `user_id` int(11) PRIMARY KEY,
  `real_name` varchar(50),
  `gender` tinyint(1),
  `birthday` date,
  `city` varchar(50),
  `address` text,
  `bio` text,
  FOREIGN KEY (`user_id`) REFERENCES `hn_users`(`id`)
);

-- 用户登录日志表
CREATE TABLE `hn_user_logs` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `user_id` int(11),
  `action` varchar(50),
  `ip` varchar(45),
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);
```

#### 商业系统表
```sql
-- 商家信息表
CREATE TABLE `hn_businesses` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `user_id` int(11),
  `name` varchar(100) NOT NULL,
  `category` varchar(50),
  `logo` varchar(255),
  `description` text,
  `address` text,
  `phone` varchar(20),
  `email` varchar(100),
  `website` varchar(255),
  `status` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 商品信息表
CREATE TABLE `hn_products` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `business_id` int(11),
  `name` varchar(100) NOT NULL,
  `category` varchar(50),
  `price` decimal(10,2),
  `original_price` decimal(10,2),
  `stock` int(11) DEFAULT 0,
  `images` text,
  `description` text,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 订单信息表
CREATE TABLE `hn_orders` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `order_no` varchar(32) UNIQUE NOT NULL,
  `user_id` int(11),
  `business_id` int(11),
  `total_amount` decimal(10,2),
  `payment_method` varchar(20),
  `payment_status` tinyint(1) DEFAULT 0,
  `order_status` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);
```

#### 内容管理表
```sql
-- 文章表
CREATE TABLE `hn_articles` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `user_id` int(11),
  `category` varchar(50),
  `title` varchar(200) NOT NULL,
  `content` longtext,
  `summary` text,
  `featured_image` varchar(255),
  `views` int(11) DEFAULT 0,
  `likes` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 论坛帖子表
CREATE TABLE `hn_forum_posts` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `user_id` int(11),
  `category` varchar(50),
  `title` varchar(200) NOT NULL,
  `content` text,
  `images` text,
  `replies` int(11) DEFAULT 0,
  `views` int(11) DEFAULT 0,
  `last_reply_at` timestamp NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);

-- 评论表
CREATE TABLE `hn_comments` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `target_type` varchar(20),
  `target_id` int(11),
  `user_id` int(11),
  `parent_id` int(11) DEFAULT 0,
  `content` text NOT NULL,
  `likes` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);
```

#### 系统配置表
```sql
-- 系统配置表
CREATE TABLE `hn_configs` (
  `key` varchar(100) PRIMARY KEY,
  `value` text,
  `description` varchar(255),
  `group` varchar(50),
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 分类管理表
CREATE TABLE `hn_categories` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT 0,
  `type` varchar(20),
  `name` varchar(50) NOT NULL,
  `slug` varchar(50),
  `description` text,
  `sort_order` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1
);

-- 文件上传表
CREATE TABLE `hn_uploads` (
  `id` int(11) PRIMARY KEY AUTO_INCREMENT,
  `user_id` int(11),
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255),
  `file_path` varchar(500),
  `file_size` int(11),
  `file_type` varchar(50),
  `mime_type` varchar(100),
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
);
```

### 数据库索引优化
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON hn_users(username);
CREATE INDEX idx_users_email ON hn_users(email);
CREATE INDEX idx_users_mobile ON hn_users(mobile);
CREATE INDEX idx_users_status ON hn_users(status);

-- 商品表索引
CREATE INDEX idx_products_business ON hn_products(business_id);
CREATE INDEX idx_products_category ON hn_products(category);
CREATE INDEX idx_products_status ON hn_products(status);
CREATE INDEX idx_products_price ON hn_products(price);

-- 订单表索引
CREATE INDEX idx_orders_user ON hn_orders(user_id);
CREATE INDEX idx_orders_business ON hn_orders(business_id);
CREATE INDEX idx_orders_status ON hn_orders(order_status);
CREATE INDEX idx_orders_payment ON hn_orders(payment_status);

-- 文章表索引
CREATE INDEX idx_articles_category ON hn_articles(category);
CREATE INDEX idx_articles_user ON hn_articles(user_id);
CREATE INDEX idx_articles_status ON hn_articles(status);
CREATE INDEX idx_articles_created ON hn_articles(created_at);
```

---

## 🚀 部署说明

### 系统要求

#### 服务器环境
```bash
# 操作系统
Ubuntu 18.04+ / CentOS 7+ / Debian 9+

# Web服务器
Nginx 1.14+ 或 Apache 2.4+

# PHP环境
PHP 7.2+ (推荐 PHP 7.4 或 8.0)
必需扩展: mysqli, gd, curl, mbstring, json, openssl
推荐扩展: redis, imagick, zip, xml

# 数据库
MySQL 5.7+ 或 MariaDB 10.2+

# 其他
Redis 4.0+ (可选，用于缓存)
FFmpeg (用于视频处理)
```

#### 硬件要求
```
最低配置:
CPU: 2核
内存: 4GB
存储: 50GB SSD
带宽: 5Mbps

推荐配置:
CPU: 4核+
内存: 8GB+
存储: 100GB+ SSD
带宽: 10Mbps+
```

### 安装步骤

#### 1. 环境准备
```bash
# Ubuntu/Debian 系统
sudo apt update
sudo apt install nginx mysql-server php7.4-fpm php7.4-mysqli php7.4-gd php7.4-curl php7.4-mbstring php7.4-json php7.4-xml

# CentOS/RHEL 系统
sudo yum update
sudo yum install nginx mysql-server php74-fpm php74-mysqli php74-gd php74-curl php74-mbstring php74-json php74-xml
```

#### 2. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE hawaiihub CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hawaiihub'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON hawaiihub.* TO 'hawaiihub'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 源码部署
```bash
# 下载源码到网站目录
cd /www/wwwroot/
unzip 火鸟门户初始化安装包.zip
chown -R www:www hawaiihub.net/
chmod -R 755 hawaiihub.net/
chmod 777 hawaiihub.net/include/
chmod 771 hawaiihub.net/templates_c/
chmod 755 hawaiihub.net/uploads/
```

#### 4. 配置文件修改
```php
// 修改数据库配置
vim /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php

<?php
define('DB_HOST', 'localhost');
define('DB_NAME', 'hawaiihub');
define('DB_USER', 'hawaiihub');
define('DB_PASS', 'your_password');
define('DB_PORT', '3306');
define('DB_CHARSET', 'utf8mb4');
?>
```

#### 5. Nginx配置
```nginx
# /etc/nginx/sites-available/hawaiihub.net
server {
    listen 80;
    server_name hawaiihub.net www.hawaiihub.net;
    root /www/wwwroot/hawaiihub.net;
    index index.php index.html;

    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ /(include|data|log|config)/ {
        deny all;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # URL重写
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

#### 6. SSL证书配置
```bash
# 使用Let's Encrypt免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d hawaiihub.net -d www.hawaiihub.net
```

### 系统初始化

#### 1. 数据库初始化
```bash
# 导入数据库结构
mysql -u hawaiihub -p hawaiihub < database.sql

# 初始化基础数据
mysql -u hawaiihub -p hawaiihub < init_data.sql
```

#### 2. 管理员账户创建
```php
// 访问 http://hawaiihub.net/admin/install.php
// 按照向导完成安装，设置管理员账户
```

#### 3. 系统配置
```php
// 登录管理后台进行基础配置
// http://hawaiihub.net/admin/
// 配置网站信息、支付接口、第三方服务等
```

---

## 🛠️ 运维指南

### 日常维护

#### 1. 日志管理
```bash
# 查看错误日志
tail -f /www/wwwroot/hawaiihub.net/log/error.log
tail -f /www/wwwroot/hawaiihub.net/log/access.log

# 清理日志文件
find /www/wwwroot/hawaiihub.net/log/ -name "*.log" -mtime +30 -delete

# 设置日志轮转
logrotate -f /etc/logrotate.d/hawaiihub
```

#### 2. 数据库维护
```sql
-- 清理过期数据
DELETE FROM hn_user_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
DELETE FROM hn_uploads WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR) AND user_id IS NULL;

-- 优化数据库表
OPTIMIZE TABLE hn_users, hn_businesses, hn_products, hn_orders;

-- 检查表结构
CHECK TABLE hn_users, hn_businesses, hn_products;
```

#### 3. 文件系统清理
```bash
# 清理模板缓存
rm -rf /www/wwwroot/hawaiihub.net/templates_c/*

# 清理临时文件
find /www/wwwroot/hawaiihub.net/uploads/temp/ -mtime +7 -delete

# 检查磁盘空间
df -h
du -sh /www/wwwroot/hawaiihub.net/
```

### 性能优化

#### 1. PHP优化配置
```ini
# /etc/php/7.4/fpm/php.ini
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
post_max_size = 64M
upload_max_filesize = 64M

# 开启OPcache
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
```

#### 2. 数据库优化
```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
query_cache_type = 1
slow_query_log = 1
long_query_time = 2
```

#### 3. Redis缓存配置
```bash
# 安装Redis
sudo apt install redis-server

# 配置Redis
# /etc/redis/redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 安全加固

#### 1. 文件权限加固
```bash
# 设置严格的文件权限
chmod 644 /www/wwwroot/hawaiihub.net/*.php
chmod 755 /www/wwwroot/hawaiihub.net/
chmod 700 /www/wwwroot/hawaiihub.net/include/config/
chmod 600 /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php
```

#### 2. 网络安全配置
```bash
# 防火墙配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable

# 限制登录失败次数
fail2ban-client set sshd addignoreregex '^.*$'
```

#### 3. 定期安全检查
```bash
# 检查可疑文件
find /www/wwwroot/hawaiihub.net/ -name "*.php" -exec grep -l "eval\|base64_decode\|exec\|system" {} \;

# 检查文件完整性
md5sum /www/wwwroot/hawaiihub.net/index.php
sha256sum /www/wwwroot/hawaiihub.net/website.php
```

### 备份策略

#### 1. 数据库备份
```bash
#!/bin/bash
# /root/backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u hawaiihub -p hawaiihub > /backup/db/hawaiihub_$DATE.sql
gzip /backup/db/hawaiihub_$DATE.sql

# 保留最近30天的备份
find /backup/db/ -name "hawaiihub_*.sql.gz" -mtime +30 -delete
```

#### 2. 文件备份
```bash
#!/bin/bash
# /root/backup_files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/files/hawaiihub_files_$DATE.tar.gz -C /www/wwwroot/ hawaiihub.net/

# 保留最近7天的文件备份
find /backup/files/ -name "hawaiihub_files_*.tar.gz" -mtime +7 -delete
```

#### 3. 自动化备份
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /root/backup_db.sh

# 每周日凌晨3点备份文件
0 3 * * 0 /root/backup_files.sh
```

### 监控告警

#### 1. 系统监控脚本
```bash
#!/bin/bash
# /root/monitor.sh

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率超过80%: ${DISK_USAGE}%"
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "警告: 内存使用率超过90%: ${MEM_USAGE}%"
fi

# 检查网站可访问性
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://hawaiihub.net)
if [ $HTTP_CODE -ne 200 ]; then
    echo "警告: 网站无法访问，HTTP状态码: $HTTP_CODE"
fi
```

#### 2. 错误日志监控
```bash
#!/bin/bash
# /root/log_monitor.sh

# 检查PHP错误日志
ERROR_COUNT=$(tail -100 /var/log/php7.4-fpm.log | grep -c "FATAL\|ERROR")
if [ $ERROR_COUNT -gt 5 ]; then
    echo "警告: PHP错误日志中发现 $ERROR_COUNT 个错误"
fi

# 检查Nginx错误日志
NGINX_ERROR=$(tail -100 /var/log/nginx/error.log | grep -c "error")
if [ $NGINX_ERROR -gt 10 ]; then
    echo "警告: Nginx错误日志中发现 $NGINX_ERROR 个错误"
fi
```

---

## 📞 技术支持

### 开发团队联系方式
- **技术支持邮箱**: <EMAIL>
- **开发者QQ群**: 123456789
- **官方网站**: https://hawaiihub.net
- **文档中心**: https://docs.hawaiihub.net

### 版本更新说明
- **当前版本**: v2.0.0
- **更新频率**: 每月一次大版本更新，每周一次小版本修复
- **更新渠道**: 官方网站下载 + 在线更新

---

**文档版本**: v1.0  
**最后更新**: 2025年6月17日  
**文档作者**: 火鸟门户技术团队