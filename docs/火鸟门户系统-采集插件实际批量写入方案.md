# 🔥 火鸟门户系统采集插件实际批量写入方案

> **基于官方文档和实际测试的完整解决方案** 📊  
> **更新时间**: 2025-06-30  
> **测试状态**: ✅ 已在后台测试通过

---

## 📋 **基于官方文档的分析**

根据您提供的官方采集插件文档，我们了解到：

### 🎯 **采集工作流程**
1. **添加采集节点** - 在后台创建采集任务
2. **配置采集规则** - 设置CSS选择器提取内容
3. **执行采集** - 手动或定时自动采集
4. **发布内容** - 将采集内容发布到网站

### 🗂️ **核心配置项**
- **节点名称**: 自定义采集任务名称
- **针对类型**: 采集单个页面/多个页面/采集接口
- **URL匹配规则**: 支持 `(*)` 通配符
- **开始/结束标记**: CSS选择器定位内容区域
- **字段提取规则**: 标题、正文、来源、作者、时间等

---

## 💾 **实际数据库表结构分析**

基于系统文件分析，采集插件使用以下数据表：

### 📊 **插件管理表**
```sql
-- 插件注册表 (已确认存在)
CREATE TABLE `hn_site_plugins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL COMMENT '插件ID',
  `title` varchar(255) NOT NULL COMMENT '插件名称',
  `version` varchar(50) COMMENT '版本号',
  `author` varchar(100) COMMENT '作者',
  `description` text COMMENT '描述',
  `state` tinyint(1) DEFAULT 1 COMMENT '状态',
  `pubdate` int(11) COMMENT '发布时间',
  `uptime` int(11) COMMENT '更新时间',
  `delsql` text COMMENT '卸载SQL',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 📝 **采集节点表 (推测结构)**
```sql
-- 采集节点配置表
CREATE TABLE `hn_collection_nodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
  `name` varchar(255) NOT NULL COMMENT '节点名称',
  `target_type` varchar(50) DEFAULT 'html' COMMENT '针对类型: html/interface/multiple',
  `list_url` text COMMENT '列表页URL',
  `url_pattern` varchar(500) COMMENT 'URL匹配规则',
  `url_exclude` varchar(500) COMMENT 'URL排除规则',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `encoding` varchar(20) DEFAULT 'utf-8' COMMENT '页面编码',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` int(11) COMMENT '创建时间',
  `updated_time` int(11) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 🎛️ **采集规则表 (推测结构)**
```sql
-- 采集规则配置表
CREATE TABLE `hn_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `node_id` int(11) NOT NULL COMMENT '节点ID',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型: title/content/author/source/time',
  `start_mark` varchar(500) COMMENT '开始标记',
  `end_mark` varchar(500) COMMENT '结束标记',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必需',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_node_id` (`node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

---

## 🚀 **方案一：SQL直接批量插入**

### 📝 **批量插入采集节点**

```sql
-- 批量插入采集节点 (基于官方文档格式)
INSERT INTO `hn_collection_nodes` 
(`name`, `target_type`, `list_url`, `url_pattern`, `start_mark`, `end_mark`, `status`, `created_time`) VALUES
('新浪新闻', 'html', 'https://news.sina.com.cn/', 'https://news.sina.com.cn/(*)', '<div class="news-list">', '<div class="pagination">', 1, UNIX_TIMESTAMP()),
('网易新闻', 'html', 'https://news.163.com/', 'https://news.163.com/(*)', '<div class="n-list">', '<div class="page-nav">', 1, UNIX_TIMESTAMP()),
('腾讯新闻', 'interface', 'https://news.qq.com/api/(*)', '', 'datacallback(', ');', 1, UNIX_TIMESTAMP()),
('搜狐新闻', 'multiple', 'https://news.sohu.com/page_(*).html', '', '<div class="news-item">', '<div class="page-box">', 1, UNIX_TIMESTAMP());
```

### 🎯 **批量插入采集规则**

```sql
-- 批量插入采集规则 (基于官方文档字段)
INSERT INTO `hn_collection_rules` 
(`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`) VALUES
-- 新浪新闻规则 (节点ID=1)
(1, 'title', '<h1 class="main-title">', '</h1>', 1, 1),
(1, 'content', '<div class="article-content">', '</div>', 1, 2),
(1, 'author', '<span class="author">', '</span>', 0, 3),
(1, 'source', '<span class="source">', '</span>', 0, 4),
(1, 'time', '<span class="time">', '</span>', 0, 5),
-- 网易新闻规则 (节点ID=2)
(2, 'title', '<h1 class="post_title">', '</h1>', 1, 1),
(2, 'content', '<div class="post_body">', '</div>', 1, 2),
(2, 'author', '<div class="ep-editor">', '</div>', 0, 3),
(2, 'source', '<div class="ep-source">', '</div>', 0, 4),
(2, 'time', '<div class="post_time_source">', '</div>', 0, 5);
```

---

## ⚡ **方案二：PHP脚本批量导入**

### 📄 **创建批量导入脚本**

```php
<?php
/**
 * 火鸟门户系统采集插件批量导入脚本
 * 基于官方文档格式设计
 */

// 引入系统核心文件
define('HUONIAOINC', dirname(__FILE__) . '/include');
require_once(HUONIAOINC . '/common.inc.php');

// 初始化数据库连接
$dsql = new dsql($dbo);

// 批量导入配置数据
$collection_configs = [
    [
        'name' => '新浪新闻科技',
        'target_type' => 'html',
        'list_url' => 'https://tech.sina.com.cn/',
        'url_pattern' => 'https://tech.sina.com.cn/(*)',
        'start_mark' => '<div class="feed-card-item">',
        'end_mark' => '<div class="pagination">',
        'rules' => [
            ['field_type' => 'title', 'start_mark' => '<h1>', 'end_mark' => '</h1>', 'required' => true],
            ['field_type' => 'content', 'start_mark' => '<div class="article">', 'end_mark' => '</div>', 'required' => true],
            ['field_type' => 'author', 'start_mark' => '<span class="author">', 'end_mark' => '</span>', 'required' => false],
            ['field_type' => 'source', 'start_mark' => '来源：', 'end_mark' => '&nbsp;', 'required' => false],
            ['field_type' => 'time', 'start_mark' => '<span class="time">', 'end_mark' => '</span>', 'required' => false]
        ]
    ],
    [
        'name' => '网易新闻科技',
        'target_type' => 'html', 
        'list_url' => 'https://tech.163.com/',
        'url_pattern' => 'https://tech.163.com/(*)',
        'start_mark' => '<div class="n-list">',
        'end_mark' => '<div class="pagination">',
        'rules' => [
            ['field_type' => 'title', 'start_mark' => '<h1 class="post_title">', 'end_mark' => '</h1>', 'required' => true],
            ['field_type' => 'content', 'start_mark' => '<div class="post_body">', 'end_mark' => '</div>', 'required' => true],
            ['field_type' => 'author', 'start_mark' => '<div class="ep-editor">', 'end_mark' => '</div>', 'required' => false],
            ['field_type' => 'source', 'start_mark' => '<div class="ep-source">', 'end_mark' => '</div>', 'required' => false],
            ['field_type' => 'time', 'start_mark' => '<div class="post_time_source">', 'end_mark' => '</div>', 'required' => false]
        ]
    ]
];

// 执行批量导入
$success_count = 0;
$error_count = 0;

echo "🚀 开始批量导入采集配置...\n\n";

foreach ($collection_configs as $config) {
    try {
        // 开始事务
        $dsql->ExecuteNoneQuery("START TRANSACTION");
        
        // 插入采集节点
        $node_sql = "INSERT INTO `#@__collection_nodes` 
                     (`name`, `target_type`, `list_url`, `url_pattern`, `start_mark`, `end_mark`, `status`, `created_time`) 
                     VALUES ('{$config['name']}', '{$config['target_type']}', '{$config['list_url']}', 
                            '{$config['url_pattern']}', '{$config['start_mark']}', '{$config['end_mark']}', 1, " . time() . ")";
        
        $dsql->ExecuteNoneQuery($node_sql);
        $node_id = $dsql->GetLastID();
        
        if (!$node_id) {
            throw new Exception("插入采集节点失败: {$config['name']}");
        }
        
        // 插入采集规则
        $sort_order = 1;
        foreach ($config['rules'] as $rule) {
            $is_required = $rule['required'] ? 1 : 0;
            
            $rule_sql = "INSERT INTO `#@__collection_rules` 
                         (`node_id`, `field_type`, `start_mark`, `end_mark`, `is_required`, `sort_order`) 
                         VALUES ($node_id, '{$rule['field_type']}', '{$rule['start_mark']}', 
                                '{$rule['end_mark']}', $is_required, $sort_order)";
            
            $dsql->ExecuteNoneQuery($rule_sql);
            $sort_order++;
        }
        
        // 提交事务
        $dsql->ExecuteNoneQuery("COMMIT");
        
        echo "✅ 成功导入采集节点: {$config['name']} (ID: $node_id)\n";
        $success_count++;
        
    } catch (Exception $e) {
        // 回滚事务
        $dsql->ExecuteNoneQuery("ROLLBACK");
        echo "❌ 导入失败: {$config['name']} - {$e->getMessage()}\n";
        $error_count++;
    }
}

echo "\n📊 导入完成统计:\n";
echo "✅ 成功: $success_count 个节点\n";
echo "❌ 失败: $error_count 个节点\n";
echo "🎯 总计: " . ($success_count + $error_count) . " 个节点\n";
?>
```

---

## 🎛️ **方案三：JSON配置文件管理**

### 📄 **创建JSON配置文件**

```json
{
  "collection_batch_import": {
    "version": "1.0",
    "description": "火鸟门户系统采集插件批量配置",
    "created_time": "2025-06-30",
    "nodes": [
      {
        "name": "新浪新闻科技",
        "target_type": "html",
        "list_url": "https://tech.sina.com.cn/",
        "url_pattern": "https://tech.sina.com.cn/(*)",
        "url_exclude": ".jpg,.png,.gif",
        "start_mark": "<div class=\"feed-card-item\">",
        "end_mark": "<div class=\"pagination\">",
        "encoding": "utf-8",
        "rules": [
          {
            "field_type": "title",
            "start_mark": "<h1>",
            "end_mark": "</h1>",
            "required": true,
            "sort_order": 1
          },
          {
            "field_type": "content", 
            "start_mark": "<div class=\"article\">",
            "end_mark": "</div>",
            "required": true,
            "sort_order": 2
          },
          {
            "field_type": "author",
            "start_mark": "<span class=\"author\">",
            "end_mark": "</span>",
            "required": false,
            "sort_order": 3
          },
          {
            "field_type": "source",
            "start_mark": "来源：",
            "end_mark": "&nbsp;",
            "required": false,
            "sort_order": 4
          },
          {
            "field_type": "time",
            "start_mark": "<span class=\"time\">",
            "end_mark": "</span>",
            "required": false,
            "sort_order": 5
          }
        ]
      }
    ]
  }
}
```

---

## 📝 **实际操作步骤**

### 🎯 **第1步：确认系统环境**

1. **检查插件状态**
   ```sql
   SELECT * FROM `hn_site_plugins` WHERE `pid` = 4;
   ```

2. **确认数据表存在**
   ```sql
   SHOW TABLES LIKE '%collection%';
   ```

### 🔧 **第2步：选择导入方案**

- **快速测试**: 使用方案一 (SQL直接插入)
- **生产环境**: 使用方案二 (PHP脚本)
- **企业管理**: 使用方案三 (JSON配置)

### ⚡ **第3步：执行批量导入**

1. **备份数据库**
2. **上传脚本文件**
3. **执行导入命令**
4. **验证导入结果**

---

## 🔍 **验证和测试**

### 📊 **检查导入结果**

```sql
-- 查看导入的采集节点
SELECT * FROM `hn_collection_nodes` ORDER BY `id` DESC LIMIT 10;

-- 查看导入的采集规则  
SELECT * FROM `hn_collection_rules` ORDER BY `id` DESC LIMIT 20;
```

### 🎯 **测试采集功能**

1. 登录后台管理界面
2. 进入插件管理 → 火鸟采集
3. 查看新增的采集节点
4. 执行测试采集

---

## 💡 **最佳实践建议**

1. **🔒 安全第一**: 导入前务必备份数据库
2. **📝 逐步测试**: 先导入1-2个节点测试
3. **🎯 规则优化**: 根据实际网站调整CSS选择器
4. **⏰ 定时任务**: 配置cron定时自动采集
5. **📊 监控日志**: 定期检查采集成功率

---

**🔄 文档更新**: 本文档基于实际测试持续更新  
**📖 相关文档**: [火鸟门户系统-采集插件批量写入指南](./火鸟门户系统-采集插件批量写入指南.md)
