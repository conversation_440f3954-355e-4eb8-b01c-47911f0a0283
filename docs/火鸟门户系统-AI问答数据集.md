# 🤖 火鸟门户系统 - AI问答数据集

> **文档目的**: 为AI提供结构化的问答训练数据，实现精准的系统功能查询
> **创建时间**: 2025-06-30
> **数据来源**: 完整模板分析 + 系统文档整合

---

## 📋 数据集概览

### 🎯 数据集规模
- **问答对数量**: 200+
- **覆盖功能模块**: 15+
- **模板文件覆盖**: 500+
- **业务场景覆盖**: 50+

### 📊 分类统计
| 分类 | 问答数量 | 主要内容 |
|------|---------|---------|
| 用户认证 | 25+ | 注册、登录、密码管理 |
| 内容发布 | 40+ | 各类信息发布流程 |
| 订单支付 | 30+ | 订单管理、支付流程 |
| 房产业务 | 35+ | 房产相关功能 |
| 招聘业务 | 20+ | 招聘求职功能 |
| 直播功能 | 25+ | 直播相关操作 |
| 商家管理 | 30+ | 企业商家功能 |
| 系统配置 | 15+ | 系统设置相关 |

---

## 🔐 用户认证相关问答

### Q1: 用户如何注册账号？
**A**: 用户通过 `member/register.html` 页面注册账号，需要填写用户名、邮箱、密码等基本信息。注册后系统会发送验证邮件到用户邮箱，用户点击邮件中的验证链接完成邮箱验证，最后跳转到 `member/registerSuccess.html` 显示注册成功。

**相关模板**: 
- `member/register.html` - 注册页面
- `member/registerVerifyEmail.html` - 邮箱验证页面
- `member/registerSuccess.html` - 注册成功页面

**移动端**: `member/touch/register.html`

### Q2: 用户忘记密码怎么办？
**A**: 用户可以通过 `member/fpwd.html` 页面找回密码。输入注册邮箱或手机号，系统会发送重置密码链接，用户点击链接后跳转到 `member/resetpwd.html` 页面设置新密码。

**相关模板**:
- `member/fpwd.html` - 忘记密码页面
- `member/resetpwd.html` - 重置密码页面

### Q3: 系统支持哪些登录方式？
**A**: 系统支持多种登录方式：
1. 用户名/邮箱+密码登录 (`member/login.html`)
2. 手机号+验证码登录
3. 第三方登录 (QQ、微信、微博)
4. 弹窗快速登录 (`member/login_popup.html`)

**相关模板**:
- `member/login.html` - 主登录页面
- `member/loginFrame.html` - 登录框架
- `member/login_popup.html` - 弹窗登录

---

## 📝 内容发布相关问答

### Q4: 用户如何发布房产信息？
**A**: 用户登录后进入 `member/fabu.html` 发布中心，选择房产类型后跳转到对应页面：
- 二手房出售: `member/fabu-house-sale.html`
- 房屋出租: `member/fabu-house-zu.html`
- 求租求购: `member/fabu-house-demand.html`
- 商铺出售: `member/fabu-house-sp.html`
- 厂房出租: `member/fabu-house-cf.html`

填写房产详细信息、上传图片、设置价格后提交发布。

**相关模板**:
- `member/fabu.html` - 发布中心
- `member/fabu-house.html` - 房产发布主页
- `member/fabu-house-*.html` - 各类房产发布页面

### Q5: 如何发布招聘信息？
**A**: 企业用户通过 `member/fabu-job.html` 发布招聘信息，需要填写职位名称、薪资待遇、工作地点、职位要求等信息。个人用户可以通过 `member/fabu_job_seek.html` 发布求职信息。

**相关模板**:
- `member/fabu-job.html` - 发布招聘
- `member/fabu_job_seek.html` - 发布求职
- `member/company/fabu-website-job.html` - 企业招聘发布

### Q6: 如何发布商家信息？
**A**: 商家通过 `member/fabu-business.html` 发布商家信息，包括商家简介、联系方式、营业时间、服务项目等。发布后可以在 `member/manage-business.html` 管理商家信息。

**相关模板**:
- `member/fabu-business.html` - 发布商家信息
- `member/manage-business.html` - 管理商家信息

---

## 💰 订单支付相关问答

### Q7: 用户如何查看订单？
**A**: 用户登录后进入 `member/order.html` 查看所有订单，可以按订单状态筛选。点击具体订单可以进入 `member/orderdetail.html` 查看订单详情，包括商品信息、价格、物流状态等。

**相关模板**:
- `member/order.html` - 订单列表
- `member/orderdetail.html` - 订单详情
- `member/order-*.html` - 各类型订单页面

### Q8: 支付流程是怎样的？
**A**: 用户确认订单后跳转到 `member/pay.html` 支付页面，选择支付方式（微信、支付宝、银行卡等），完成支付后跳转到 `member/pay_success.html` 显示支付成功。

**相关模板**:
- `member/confirm-order.html` - 确认订单
- `member/pay.html` - 支付页面
- `member/pay_success.html` - 支付成功页面

### Q9: 如何申请退款？
**A**: 用户在订单详情页面可以申请退款，系统提供多种退款页面：
- 商城退款: `member/refund-shop.html`
- 家政退款: `member/refund-homemaking.html`
- 通用退款: `member/refund.html`

**相关模板**:
- `member/refund*.html` - 各类退款页面
- `member/orderdetail*.html` - 订单详情页面

---

## 🏠 房产业务相关问答

### Q10: 如何预约看房？
**A**: 用户在房产详情页面可以预约看房，通过 `member/house_yuyue.html` 页面选择看房时间、填写联系方式。预约记录可以在 `member/house_yuyue_list.html` 查看。

**相关模板**:
- `member/house_yuyue.html` - 看房预约
- `member/house_yuyue_list.html` - 预约记录

### Q11: 什么是房产委托？
**A**: 房产委托是指房主委托平台代理出售或出租房产。通过 `member/house_entrust.html` 页面提交委托申请，填写房产信息和委托要求。委托记录在 `member/house_entrust_list.html` 管理。

**相关模板**:
- `member/house_entrust.html` - 房产委托
- `member/house_entrust_list.html` - 委托记录

### Q12: 楼盘管理功能有哪些？
**A**: 楼盘管理通过 `member/house_loupan.html` 实现，包括楼盘基本信息管理、户型图上传、价格设置、销售状态更新等功能。

**相关模板**:
- `member/house_loupan.html` - 楼盘管理
- `member/house_loupan_printVisitConfirm.html` - 看房确认单

---

## 💼 招聘业务相关问答

### Q13: 如何管理简历？
**A**: 求职者通过 `member/job-resume.html` 管理个人简历，包括基本信息、工作经历、教育背景、技能特长等。可以创建多份针对不同职位的简历。

**相关模板**:
- `member/job-resume.html` - 简历管理
- `member/job.html` - 招聘中心

### Q14: 如何查看投递记录？
**A**: 求职者在 `member/job-delivery.html` 查看简历投递记录，包括投递时间、职位信息、投递状态等。可以跟踪每次投递的进展。

**相关模板**:
- `member/job-delivery.html` - 投递记录
- `member/job-invitation.html` - 面试邀请

### Q15: 企业如何管理招聘？
**A**: 企业通过 `member/company/` 目录下的招聘相关模板管理招聘：
- 发布职位: `company/fabu-website-job.html`
- 查看简历: `company/resume.html`
- 面试邀请: `company/invitation.html`

**相关模板**:
- `member/company/fabu-website-job.html` - 发布职位
- `member/company/resume.html` - 简历管理
- `member/company/invitation.html` - 面试邀请

---

## 📺 直播功能相关问答

### Q16: 如何开始直播？
**A**: 主播通过 `member/fabu-live.html` 创建直播间，设置直播标题、分类、封面图等信息。直播过程中可以通过 `member/livedetail.html` 管理直播间。

**相关模板**:
- `member/fabu-live.html` - 创建直播
- `member/livedetail.html` - 直播间管理

### Q17: 直播收入如何查看？
**A**: 主播可以通过 `member/live_income.html` 查看直播收入详情，包括礼物收入、打赏收入、广告分成等。还可以在 `member/live_charts.html` 查看直播数据统计。

**相关模板**:
- `member/live_income.html` - 直播收入
- `member/live_charts.html` - 直播统计

### Q18: 直播礼物系统如何使用？
**A**: 观众可以通过 `member/live_gift.html` 给主播送礼物，主播在 `member/live_reward.html` 管理打赏设置。系统支持多种虚拟礼物和红包功能。

**相关模板**:
- `member/live_gift.html` - 礼物系统
- `member/live_reward.html` - 打赏管理
- `member/live_hongbao.html` - 红包功能

---

## 🏢 商家管理相关问答

### Q19: 企业如何入驻平台？
**A**: 企业通过 `member/enter.html` 申请入驻，填写企业基本信息、营业执照、联系方式等。审核通过后可以使用 `member/company/` 下的企业管理功能。

**相关模板**:
- `member/enter.html` - 企业入驻
- `member/enter-review.html` - 入驻审核
- `member/company/index.html` - 企业管理中心

### Q20: 企业如何配置店铺？
**A**: 企业在 `member/company/config.html` 配置店铺基本信息，通过 `member/company/business-config.html` 设置营业信息，使用 `member/company/dressup-website.html` 装修店铺页面。

**相关模板**:
- `member/company/config.html` - 基础配置
- `member/company/business-config.html` - 营业配置
- `member/company/dressup-website.html` - 店铺装修

---

## 📱 移动端相关问答

### Q21: 移动端功能完整吗？
**A**: 移动端功能非常完整，所有主要功能都有对应的移动端模板。移动端模板位于各模块的 `touch/` 目录下，如 `member/touch/`、`business/touch/` 等。

**移动端特色**:
- 触屏优化的交互设计
- 地图定位功能
- 扫码支付
- APP内嵌页面支持

### Q22: 如何在手机上发布信息？
**A**: 手机用户通过 `member/touch/fabu.html` 进入发布中心，选择要发布的信息类型，如房产 (`member/touch/fabu-house.html`)、招聘 (`member/touch/fabu-job.html`) 等。

**相关模板**:
- `member/touch/fabu.html` - 移动端发布中心
- `member/touch/fabu-*.html` - 各类信息发布

---

## 🔧 系统配置相关问答

### Q23: 如何切换城市？
**A**: 用户通过 `siteConfig/changecity.html` 切换当前城市，系统会根据城市显示对应的本地化内容和服务。

**相关模板**:
- `siteConfig/changecity.html` - 城市切换
- `siteConfig/top.html` - 网站头部导航

### Q24: 系统支持哪些支付方式？
**A**: 系统支持多种支付方式：
- 微信支付 (`siteConfig/wxpay.html`)
- 支付宝支付
- 银行卡支付
- PayPal国际支付
- 余额支付

**相关模板**:
- `siteConfig/wxpay.html` - 微信支付配置
- `member/pay.html` - 支付页面

---

## 🎯 AI查询优化建议

### 查询模式示例
1. **功能查询**: "如何发布房产信息？" → 返回发布流程和相关模板
2. **模板查询**: "登录页面是哪个文件？" → 返回 `member/login.html`
3. **流程查询**: "用户注册的完整流程？" → 返回注册→验证→成功的完整流程
4. **问题解决**: "忘记密码怎么办？" → 返回密码找回流程

### 关键词映射
- **注册** → register, 账号创建, 新用户
- **登录** → login, 登陆, 用户认证
- **发布** → fabu, 发布信息, 内容发布
- **订单** → order, 购买, 交易
- **支付** → pay, 付款, 结算
- **房产** → house, 房屋, 地产
- **招聘** → job, 求职, 工作
- **直播** → live, 视频直播, 在线直播

这个问答数据集为AI提供了结构化的训练数据，可以实现精准的功能查询和问题解答。
