# 🔌 火鸟门户插件文件结构详解

> **分析时间**: 2025-01-17  
> **服务器**: 154.40.47.153 (宝塔面板)  
> **网站**: hawaiihub.net  

---

## 📁 插件目录结构总览

### 主要插件目录
```
/www/wwwroot/hawaiihub.net/
├── include/plugins/                    # 核心插件代码目录
│   ├── 4/                             # 信息资讯采集插件
│   ├── 5/                             # 一键转载插件  
│   └── 26/                            # AI智能插件
├── uploads/siteConfig/plugins/         # 插件配置存储目录
├── admin/siteConfig/plugins.php        # 插件管理后台
├── admin/templates/siteConfig/plugins.html # 插件管理模板
└── static/js/admin/siteConfig/plugins.js   # 插件管理JS
```

---

## 🔍 详细插件分析

### 插件 #4 - 信息资讯采集插件
**功能**: 自动采集外部网站新闻资讯内容

```
/include/plugins/4/
├── index.php                          # 插件主入口文件
├── common.php                         # 公共函数库 (12.2KB)
├── getNews.php                        # 新闻采集核心 (10.5KB)
├── getUrl.php                         # URL处理模块 (7KB)
├── insertBodyRules.php                # 内容规则插入 (4.6KB)
├── insertNode.php                     # 节点插入处理 (3.9KB)
├── service/                           # 服务层目录
├── static/                            # 静态资源
│   ├── css/
│   │   └── ui/                       # UI样式文件
│   ├── img/                          # 图片资源
│   └── js/                           # JavaScript文件
├── tpl/                              # 模板目录
└── cache/                            # 缓存目录
```

**核心特性**:
- 支持多站点新闻采集
- 智能内容提取和格式化
- 缓存机制优化性能
- 可配置采集规则

### 插件 #5 - 一键转载插件
**功能**: 快速转载和复制内容

```
/include/plugins/5/
├── index.php                          # 主入口 (一键转载功能)
├── getInfo.php                        # 信息获取模块
├── form.php                           # 表单处理
├── service/                           # 服务层
│   ├── HttpDownService.php           # HTTP下载服务
│   └── ImagesService.php             # 图片处理服务
└── tpl/                              # 模板目录
    ├── index.html                    # 主界面模板
    ├── images/                       # 图片资源
    └── js/                           # JavaScript文件
```

**核心代码分析**:
```php
// 插件ID自动识别机制
$cur = realpath('.');
$par = realpath('..');
$folder = str_replace($par, '', $cur);
$folder = str_replace(['/', '\\'], '', $folder);

// 动态JS文件路径
$jsFile = '//' . $cfg_basehost . '/include/plugins/' . $folder . '/index.js';
```

### 插件 #26 - AI智能插件
**功能**: AI内容生成和智能处理

```
/include/plugins/26/
├── index.php                          # 主配置界面
├── config.inc.php                     # 配置文件
├── setting.php                        # 设置选项定义
├── count.php                          # 统计功能
├── api.php                           # API接口
└── tpl/                              # 模板目录
    ├── config.html                   # 配置界面模板
    └── js/                           # JavaScript支持
```

**配置文件结构** (`config.inc.php`):
```php
<?php
$aiPlatform = '0';      // AI平台选择
$model = '';            // AI模型
$outputMethod = '0';    // 输出方式
$apiKey = '';           // API密钥
$openPlugin = '0';      // 插件开关
$modules = 'info,job';  // 应用模块
$useLimit = '1';        // 使用限制
```

---

## 🏗️ 插件开发规范

### 标准插件结构
```
/include/plugins/{插件ID}/
├── index.php                          # 必需 - 插件主入口
├── config.inc.php                     # 可选 - 配置文件
├── setting.php                        # 可选 - 设置选项
├── api.php                           # 可选 - API接口
├── service/                          # 可选 - 服务层
├── tpl/                              # 必需 - 模板目录
│   ├── *.html                        # 界面模板
│   ├── css/                          # 样式文件
│   └── js/                           # 脚本文件
├── static/                           # 可选 - 静态资源
└── cache/                            # 可选 - 缓存目录
```

### 插件入口文件规范
```php
<?php
/**
 * 插件名称
 * @version    版本号
 * @package    HuoNiao.Plugins
 * @copyright  Copyright (c) 2013 - 2021, HuoNiao, Inc.
 * @link       https://www.ihuoniao.cn/
 */

// 1. 引入系统核心
require_once('../../common.inc.php');

// 2. 用户权限检查
if($userLogin->getUserID()==-1){
    header("location:" . $cfg_secureAccess.$cfg_basehost);
    exit();
}

// 3. 模板目录设置
$tpl = dirname(__FILE__) . "/tpl";
$huoniaoTag->template_dir = $tpl;
$templates = "index.html";

// 4. 插件逻辑处理
// ... 插件功能代码 ...

// 5. 模板渲染
$huoniaoTag->display($templates);
```

---

## 🎛️ 插件管理系统

### 后台管理文件
- **管理入口**: `/admin/siteConfig/plugins.php`
- **管理模板**: `/admin/templates/siteConfig/plugins.html`
- **前端脚本**: `/static/js/admin/siteConfig/plugins.js`

### 数据库表结构
```sql
-- 插件信息表
hn_site_plugins
├── id              # 插件ID
├── pid             # 父级ID
├── title           # 插件标题
├── version         # 版本号
├── description     # 描述
├── author          # 作者
├── pubdate         # 发布日期
├── uptime          # 更新时间
├── litpic          # 插件图标
└── state           # 状态(启用/禁用)
```

### 插件状态管理
- **启用**: `state = 1`
- **禁用**: `state = 0`
- **开发中**: `state = -1`

---

## 🔧 系统集成机制

### 自动加载机制
```php
// 系统定时任务中的插件调用
/include/cron/system_plugin_18.php
/include/cron/system_plugin_22.php
/include/cron/system_plugin_24.php
```

### 模板引擎集成
- 使用火鸟自研模板引擎 `$huoniaoTag`
- 支持Smarty语法
- 插件模板独立存储在各自的 `tpl/` 目录

### 权限控制
```php
// 插件访问权限检查
checkPurview("plugins");

// 用户登录状态验证
if($userLogin->getUserID()==-1){
    // 未登录处理
}
```

---

## 🚀 插件开发最佳实践

### 1. 命名规范
- 插件目录: 数字ID (如: 4, 5, 26)
- 文件命名: 小写字母+下划线
- 类命名: 驼峰命名法

### 2. 安全规范
- 所有用户输入必须过滤
- SQL查询使用预处理语句
- 文件上传严格验证

### 3. 性能优化
- 合理使用缓存机制
- 数据库查询优化
- 静态资源CDN加速

### 4. 错误处理
```php
// 标准错误返回格式
die('{"state": 200, "info": ' . json_encode("错误信息") .'}');
die('{"state": 100, "info": ' . json_encode("成功信息") .'}');
```

---

## 📋 插件API接口规范

### 标准响应格式
```json
{
    "state": 100,           // 状态码: 100=成功, 200=失败
    "info": "操作信息",      // 提示信息
    "data": {}              // 返回数据(可选)
}
```

### 配置保存接口
```php
// POST /include/plugins/{id}/index.php?action=save
// 参数通过 $_POST 传递
// 返回标准JSON格式
```

---

## 🎯 总结

火鸟门户的插件系统具有以下特点:

1. **模块化设计**: 每个插件独立目录，互不干扰
2. **标准化接口**: 统一的入口文件和配置规范  
3. **权限控制**: 完善的用户权限验证机制
4. **模板分离**: 插件界面与逻辑代码分离
5. **数据库集成**: 与主系统数据库无缝对接
6. **缓存支持**: 内置缓存机制提升性能

这个插件系统为夏威夷华人平台提供了强大的扩展能力，支持新闻采集、内容转载、AI智能处理等多种功能模块。 