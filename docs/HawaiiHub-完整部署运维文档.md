# 🏝️ HawaiiHub 夏威夷华人平台 - 完整部署运维文档

## 📋 目录
- [系统概述](#系统概述)
- [服务器环境](#服务器环境)
- [数据库配置](#数据库配置)
- [网站配置](#网站配置)
- [文件系统](#文件系统)
- [安全配置](#安全配置)
- [性能优化](#性能优化)
- [日常运维](#日常运维)
- [故障排除](#故障排除)

---

## 🌟 系统概述

### 项目基本信息
- **网站域名**: hawaiihub.net
- **网站名称**: 夏威夷资讯站 (华人新闻)
- **项目类型**: 火鸟门户系统 (综合性华人生活服务平台)
- **部署环境**: 宝塔面板 + Nginx + PHP 7.4 + MySQL
- **服务器IP**: *************
- **安装包大小**: 254MB

### 技术架构特点
- **核心框架**: 自研MVC框架 (火鸟门户系统)
- **模板引擎**: 自研模板系统
- **数据库设计**: 490张数据表，表前缀 `hn_`
- **多语言支持**: 中文、英文、日文、繁体中文
- **移动端适配**: 响应式设计 + APP + 微信小程序

---

## 🖥️ 服务器环境

### 系统信息
```bash
操作系统: Debian 12 (bookworm)
内核版本: Linux 6.1.0-27-amd64
主机名: instance-boDvfD4g
架构: x86_64
时区: UTC+8 (Etc/GMT-8)
```

### 软件环境
```bash
Web服务器: Nginx (宝塔面板管理)
PHP版本: PHP 7.4-FPM
数据库: MySQL 5.7+
面板系统: 宝塔Linux面板
Docker: 已安装 (用于容器化服务)
```

### 目录权限
```bash
网站根目录: /www/wwwroot/hawaiihub.net/
权限设置:
drwxrwxrwx  hawaiihub.net/          # 755 (root:www)
drwxrwxrwx  include/                # 777 (需要写入权限)
drwxrwx--x  templates_c/            # 771 (模板编译缓存)
drwxr-xr-x  uploads/                # 755 (文件上传)
drwxr-xr-x  static/                 # 755 (静态资源)
```

---

## 🗄️ 数据库配置

### 数据库连接信息
```php
# /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php
$DB_HOST = 'localhost';              // 数据库地址
$DB_NAME = 'hawaiihub_net';          // 数据库名
$DB_USER = 'hawaiihub_net';          // 数据库用户名
$DB_PASS = 'BkNR4w1KHrXX48by';      // 数据库密码
$DB_PREFIX = 'hn_';                  // 数据库表前缀
$DB_CHARSET = 'utf8';                // 数据库编码
```

### 数据库规模
```sql
数据库名: hawaiihub_net
表总数: 490张表
表前缀: hn_
字符编码: utf8
主要表类别:
- 用户系统: hn_member*, hn_user*
- 商业系统: hn_business*, hn_shop*
- 内容管理: hn_article*, hn_forum*
- 系统配置: hn_config*, hn_site*
- 日志统计: hn_log*, hn_stat*
```

### 重要数据表
```sql
-- 核心配置表
hn_admingroup          # 管理员组
hn_adminlogin          # 管理员登录日志
hn_app_config          # APP配置
hn_article             # 文章内容
hn_business            # 商家信息
hn_member              # 会员信息
hn_payment             # 支付记录
hn_upload              # 上传文件
```

---

## ⚙️ 网站配置

### 核心配置文件

#### 1. 主配置文件 (siteConfig.inc.php)
```php
# /www/wwwroot/hawaiihub.net/include/config/siteConfig.inc.php

基本信息:
$cfg_basehost = 'hawaiihub.net';
$cfg_webname = '夏威夷资讯站';
$cfg_shortname = '华人新闻';
$cfg_shareTitle = '夏威夷资讯站';
$cfg_shareDesc = '夏威夷华人必备助手，新闻、招聘、房产、社群一站搞定，尽享本地优质服务！';

SEO配置:
$cfg_keywords = '夏威夷生活, 华人资讯平台, 本地招聘, 房产租售, 社区互动, 一站式服务';
$cfg_description = '我们是一家专注夏威夷华人社区的综合信息网站系统，支持 APP、微信、小程序、H5、PC 五端实时同步。';

版权信息:
$cfg_beian = '© 2025 HawaiiHub 夏威夷华人生活平台 版权所有 | 苏ICP备12345678号-1';
$cfg_hotline = '0512-67581578';

模板配置:
$cfg_template = 'skin10';           # PC端模板
$cfg_touchTemplate = 'skin4';       # 移动端模板
$cfg_lang = 'zh-CN';                # 默认语言

上传配置:
$cfg_uploadDir = '/uploads';
$cfg_thumbSize = 5120;              # 缩略图大小限制(KB)
$cfg_photoSize = 51200;             # 图片大小限制(KB)
$cfg_videoSize = 1024000;           # 视频大小限制(KB)
$cfg_thumbType = 'jpg|jpeg|gif|png|swf|webp|svg';
$cfg_videoType = 'flv|swf|wmv|mp4|avi|asf|f4v|mov|webm';

安全配置:
$cfg_replacestr = '色情|赌博|诈骗|涉政|暴力|恐怖主义|谣言|极端|毒品';
$cfg_secure_domain = 'weibo.com|apple.com|baidu.com|kumanyun.com|vivo.com.cn';
```

#### 2. 火鸟核心配置 (huoniao.php)
```php
# /www/wwwroot/hawaiihub.net/huoniao.php
<?php
$kumanyun_user_keys = 'QkE0RUtRODlBVE5iTEFCdkJqMEFhRk52QUhkU1lWQjFCVDhEUGxaMFZpWlJYUT09';
?>
```

#### 3. 城市系统配置 (system_site_city.json)
```json
{
    "expire_time": 1749839225,
    "data": [{
        "id": 1,
        "cityid": 34,
        "domain": "taiwan", 
        "url": "https://hawaiihub.net",
        "link": "https://hawaiihub.net",
        "name": "台湾",
        "pinyin": "taiwan",
        "type": 2,
        "default": 1,
        "hot": 1,
        "lat": 25.040941981936,
        "lng": 121.***********,
        "count": 1
    }]
}
```

#### 4. APP配置 (appConfig.json)
```json
{
    "cfg_basehost": "https://hawaiihub.net",
    "cfg_user_index": "https://hawaiihub.net/u",
    "cfg_business_index": "https://hawaiihub.net/b",
    "cfg_webname": "夏威夷资讯站",
    "cfg_shortname": "华人新闻",
    "cfg_wechatName": "火鸟门户",
    "cfg_business_state": 1,
    "cfg_miniProgramBindPhone": "1",
    "language": "zh-CN",
    "cfg_timeZone": 8,
    "cfg_timeZoneName": "Etc/GMT-8",
    "currency": {
        "name": "人民币",
        "short": "元", 
        "symbol": "¥",
        "code": "RMB",
        "rate": "1"
    },
    "share": {
        "title": "夏威夷资讯站",
        "description": "夏威夷华人必备助手，新闻、招聘、房产、社群一站搞定，尽享本地优质服务！"
    }
}
```

### URL重写规则

#### .htaccess配置
```apache
# /www/wwwroot/hawaiihub.net/.htaccess
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  ErrorDocument 404 /404.html

  # 禁用 X-Original-URL 头部
  Header unset X-Original-URL
   
  # 禁用 X-Rewrite-URL 头部
  Header unset X-Rewrite-URL

  # 禁止OPTIONS请求
  RewriteCond %{REQUEST_METHOD} ^(OPTIONS)
  RewriteRule .* - [F]

  # 主要重写规则
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteRule ^(.*)$ index.php [NC,QSA]
</IfModule>
```

---

## 🌐 Nginx配置

### 虚拟主机配置
```nginx
# /www/server/panel/vhost/nginx/hawaiihub.net.conf
server {
    listen 80;
    listen 443 ssl http2;
    server_name hawaiihub.net;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/hawaiihub.net;

    # SSL证书配置
    ssl_certificate    /www/server/panel/vhost/cert/hawaiihub.net/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/hawaiihub.net/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    
    # 强制HTTPS
    error_page 497 https://$host$request_uri;

    # 错误页面
    error_page 404 /404.html;

    # PHP配置
    include enable-php-74.conf;

    # URL重写
    include /www/server/panel/vhost/rewrite/hawaiihub.net.conf;

    # 安全配置 - 禁止访问敏感文件
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404;
    }

    # SSL证书验证目录
    location ~ \.well-known {
        allow all;
    }

    # 静态文件缓存
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
        expires 30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$ {
        expires 12h;
        error_log /dev/null;
        access_log /dev/null;
    }

    # 日志配置
    access_log /www/wwwlogs/hawaiihub.net.log;
    error_log /www/wwwlogs/hawaiihub.net.error.log;
}
```

---

## 📁 文件系统结构

### 网站目录结构
```
hawaiihub.net/                              # 网站根目录 (775 www:www)
├── admin/                                  # 管理后台 (40个功能模块)
│   ├── index.php (189KB)                   # 后台主控制台
│   ├── login.php (6.5KB)                   # 管理员登录
│   ├── templates/ (38个模板)               # 后台模板
│   ├── business/                           # 商家管理
│   ├── member/                             # 会员管理
│   ├── siteConfig/                         # 网站配置
│   └── ...                                # 其他管理模块
├── api/                                    # API接口层 (11个服务)
│   ├── login.php                           # 登录API
│   ├── uc.php (6.2KB)                      # 用户中心API
│   ├── appConfig.json (4.5KB)              # APP配置
│   ├── payment/ (19种支付方式)             # 支付接口
│   ├── upload/ (6个处理器)                 # 文件上传API
│   ├── weixin/                             # 微信接口
│   └── ...                                # 其他API
├── include/                                # 核心功能库 (777 www:www)
│   ├── common.func.php (877KB)             # 公共函数库
│   ├── common.inc.php (78KB)               # 公共配置
│   ├── kernel.inc.php (198KB)              # 系统内核
│   ├── website.inc.php (53KB)              # 网站核心
│   ├── class/ (12个核心类)                 # 面向对象类库
│   ├── config/ (38个配置文件)              # 模块配置
│   ├── vendor/ (14个扩展包)                # Composer包
│   ├── lang/ (4种语言包)                   # 多语言
│   └── ...                                # 其他核心文件
├── templates/                              # 前端模板 (50个模块)
│   ├── member/ (8个子模板)                 # 会员中心
│   ├── business/ (4个模板)                 # 商家展示
│   ├── article/ (4个模板)                  # 文章页面
│   └── ...                                # 其他模板
├── templates_c/                            # 编译模板缓存 (771 www:www)
├── static/                                 # 静态资源
│   ├── css/ (8个样式目录)                  # 样式文件
│   ├── js/ (12个脚本目录)                  # JavaScript
│   ├── images/ (27个图片目录)              # 图片资源
│   ├── layuiadmin/ (11个管理样式)          # 后台UI
│   └── ...                                # 其他静态资源
├── uploads/                                # 用户上传文件 (755 www:www)
├── data/                                   # 数据文件存储
├── log/                                    # 系统日志文件
├── wmsj/                                   # 系统功能目录
├── design/                                 # 设计资源文件
├── index.php (76KB)                        # 网站入口文件
├── website.php (94KB)                      # 网站核心文件
├── special.php (60KB)                      # 特殊功能文件
├── huoniao.php (98B)                       # 火鸟核心配置
├── huoniao.dll (482KB)                     # Windows动态链接库
├── huoniao.so (166KB)                      # Linux共享库
├── huoniao (1.2KB)                         # 可执行文件
├── .htaccess (435B)                        # Apache重写规则
├── system_site_city.json (262B)            # 城市系统配置
└── 火鸟门户初始化安装包.zip (254MB)         # 原始安装包
```

### 关键文件说明

#### 入口文件 (index.php)
```php
<?php
//系统核心配置文件
require_once(dirname(__FILE__).'/include/common.inc.php');

//域名检测
$httpHost = $_SERVER['HTTP_HOST'];

//获取访问详情 兼容win
$reqUri = $_SERVER["HTTP_X_REWRITE_URL"];
if($reqUri == null){
    $reqUri = $_SERVER["HTTP_X_ORIGINAL_URL"];
    if($reqUri == null){
        $reqUri = $_SERVER["REQUEST_URI"];
    }
}

// URL安全处理
$reqUri = addslashes(strip_tags(trim(preg_replace('/(\'|\")/', '', $reqUri))));

$service = 'siteConfig';

// 域名绑定和路由处理
$cityDomainType = 0;
$domainIsMember = false;
$siteModuleDomainType = 0;

// 域名验证和模块分发
...
```

### 上传目录结构
```
uploads/
├── images/                                 # 图片上传
│   ├── 2025/06/                           # 按年月分类
│   └── temp/                              # 临时文件
├── videos/                                # 视频上传
├── documents/                             # 文档上传
├── audio/                                 # 音频文件
└── avatar/                                # 用户头像
```

---

## 🔒 安全配置

### 1. 文件权限安全
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod 755 /www/wwwroot/hawaiihub.net/
chmod 777 /www/wwwroot/hawaiihub.net/include/
chmod 771 /www/wwwroot/hawaiihub.net/templates_c/
chmod 755 /www/wwwroot/hawaiihub.net/uploads/
chmod 644 /www/wwwroot/hawaiihub.net/*.php
chmod 600 /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php
```

### 2. Nginx安全配置
```nginx
# 禁止访问敏感文件
location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
    return 404;
}

# 禁止执行PHP的目录
location ~ ^/(uploads|static)/.*\.php$ {
    deny all;
}

# 限制请求方法
if ($request_method !~ ^(GET|HEAD|POST)$ ) {
    return 405;
}
```

### 3. PHP安全配置
```ini
# /etc/php/7.4/fpm/php.ini
expose_php = Off
display_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300
memory_limit = 512M
```

### 4. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER 'hawaiihub_net'@'localhost' IDENTIFIED BY 'BkNR4w1KHrXX48by';
GRANT ALL PRIVILEGES ON hawaiihub_net.* TO 'hawaiihub_net'@'localhost';
FLUSH PRIVILEGES;

-- 删除默认用户
DROP USER ''@'localhost';
DROP USER 'root'@'%';
```

### 5. 应用层安全
```php
// 内容过滤
$cfg_replacestr = '色情|赌博|诈骗|涉政|暴力|恐怖主义|谣言|极端|毒品';

// 域名白名单
$cfg_secure_domain = 'weibo.com|apple.com|baidu.com|kumanyun.com|vivo.com.cn';

// IP访问限制
$cfg_iplimit = '174.258.124.156
186.15.
203.208.60.';

// 登录安全
$cfg_errLoginCount = 10;        # 错误登录次数限制
$cfg_loginLock = 15;            # 锁定时间(分钟)
```

---

## 🚀 性能优化

### 1. PHP-FPM优化
```ini
# /etc/php/7.4/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

# 开启OPcache
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
```

### 2. MySQL优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
query_cache_type = 1
slow_query_log = 1
long_query_time = 2
max_connections = 200
```

### 3. Nginx优化
```nginx
# 开启gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1k;
gzip_comp_level 6;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 静态文件缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
}

# 连接优化
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
```

### 4. 应用层优化
```php
// 启用缓存
$cfg_cache_lifetime = '3600';   # 缓存生存时间

// 图片压缩
$cfg_imageCompress = '1';       # 开启图片压缩
$cfg_quality = 85;              # 压缩质量

// 静态资源CDN
$cfg_remoteStatic = '';         # CDN地址

// 数据库查询优化
$cfg_max_subtable_count = 500000;   # 子表最大记录数
```

---

## 🛠️ 日常运维

### 1. 日志管理

#### 网站访问日志
```bash
# 查看访问日志
tail -f /www/wwwlogs/hawaiihub.net.log

# 分析访问统计
awk '{print $1}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -10

# 清理日志文件
find /www/wwwlogs/ -name "*.log" -mtime +30 -delete
```

#### 错误日志
```bash
# 查看错误日志
tail -f /www/wwwlogs/hawaiihub.net.error.log

# PHP错误日志
tail -f /var/log/php7.4-fpm.log

# 系统日志
tail -f /var/log/syslog
```

#### 应用日志
```bash
# 系统日志目录
ls -la /www/wwwroot/hawaiihub.net/log/

# 登录日志
tail -f /www/wwwroot/hawaiihub.net/log/login.log

# 支付日志
tail -f /www/wwwroot/hawaiihub.net/log/payment.log
```

### 2. 数据库维护

#### 定期备份
```bash
#!/bin/bash
# 数据库备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u hawaiihub_net -pBkNR4w1KHrXX48by hawaiihub_net > /backup/db/hawaiihub_$DATE.sql
gzip /backup/db/hawaiihub_$DATE.sql

# 保留最近30天的备份
find /backup/db/ -name "hawaiihub_*.sql.gz" -mtime +30 -delete
```

#### 数据库优化
```sql
-- 清理过期数据
DELETE FROM hn_logs WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
DELETE FROM hn_session WHERE expire_time < UNIX_TIMESTAMP();

-- 优化表结构
OPTIMIZE TABLE hn_member, hn_article, hn_business;

-- 检查表状态
CHECK TABLE hn_member, hn_article, hn_business;

-- 重建索引
REPAIR TABLE hn_member, hn_article, hn_business;
```

### 3. 文件系统维护

#### 清理临时文件
```bash
# 清理模板缓存
rm -rf /www/wwwroot/hawaiihub.net/templates_c/*

# 清理上传临时文件
find /www/wwwroot/hawaiihub.net/uploads/temp/ -mtime +7 -delete

# 清理系统临时文件
find /tmp/ -name "php*" -mtime +1 -delete
```

#### 磁盘空间监控
```bash
# 检查磁盘使用情况
df -h

# 检查目录大小
du -sh /www/wwwroot/hawaiihub.net/
du -sh /www/wwwroot/hawaiihub.net/uploads/
du -sh /www/wwwroot/hawaiihub.net/static/

# 查找大文件
find /www/wwwroot/hawaiihub.net/ -type f -size +100M -exec ls -lh {} \;
```

### 4. 安全检查

#### 文件完整性检查
```bash
# 检查核心文件是否被篡改
md5sum /www/wwwroot/hawaiihub.net/index.php
sha256sum /www/wwwroot/hawaiihub.net/include/common.inc.php

# 查找可疑文件
find /www/wwwroot/hawaiihub.net/ -name "*.php" -exec grep -l "eval\|base64_decode\|exec\|system" {} \;

# 检查文件权限
find /www/wwwroot/hawaiihub.net/ -type f -perm 777 -exec ls -la {} \;
```

#### 访问监控
```bash
# 检查异常访问
awk '$9 == 404 {print $1}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -10

# 检查可疑IP
awk '{print $1}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -20

# 检查异常User-Agent
awk '{print $12}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr | head -10
```

### 5. 自动化监控

#### 系统监控脚本
```bash
#!/bin/bash
# /root/monitor_hawaiihub.sh

echo "=== HawaiiHub 系统监控报告 $(date) ==="

# 检查服务状态
echo "1. 服务状态检查:"
systemctl is-active nginx && echo "✓ Nginx运行正常" || echo "✗ Nginx异常"
systemctl is-active php7.4-fpm && echo "✓ PHP-FPM运行正常" || echo "✗ PHP-FPM异常"
systemctl is-active mysql && echo "✓ MySQL运行正常" || echo "✗ MySQL异常"

# 检查网站可访问性
echo -e "\n2. 网站可访问性:"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://hawaiihub.net)
if [ $HTTP_CODE -eq 200 ]; then
    echo "✓ 网站访问正常 (HTTP $HTTP_CODE)"
else
    echo "✗ 网站访问异常 (HTTP $HTTP_CODE)"
fi

# 检查磁盘空间
echo -e "\n3. 磁盘使用情况:"
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 80 ]; then
    echo "✓ 磁盘使用正常 ($DISK_USAGE%)"
else
    echo "✗ 磁盘使用率过高 ($DISK_USAGE%)"
fi

# 检查内存使用
echo -e "\n4. 内存使用情况:"
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -lt 85 ]; then
    echo "✓ 内存使用正常 ($MEM_USAGE%)"
else
    echo "✗ 内存使用率过高 ($MEM_USAGE%)"
fi

# 检查数据库连接
echo -e "\n5. 数据库状态:"
mysql -u hawaiihub_net -pBkNR4w1KHrXX48by -e "SELECT 1;" >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ 数据库连接正常"
else
    echo "✗ 数据库连接异常"
fi

# 检查错误日志
echo -e "\n6. 错误日志检查:"
ERROR_COUNT=$(tail -100 /www/wwwlogs/hawaiihub.net.error.log | grep -c "error")
if [ $ERROR_COUNT -lt 5 ]; then
    echo "✓ 错误日志正常 ($ERROR_COUNT个错误)"
else
    echo "✗ 错误日志异常 ($ERROR_COUNT个错误)"
fi

echo -e "\n=== 监控完成 ==="
```

#### 定时任务设置
```bash
# 编辑定时任务
crontab -e

# 添加以下任务:
# 每小时执行系统监控
0 * * * * /root/monitor_hawaiihub.sh >> /var/log/hawaiihub_monitor.log 2>&1

# 每天凌晨2点备份数据库
0 2 * * * /root/backup_db.sh

# 每周日凌晨3点备份文件
0 3 * * 0 /root/backup_files.sh

# 每天凌晨4点清理日志
0 4 * * * find /www/wwwlogs/ -name "*.log" -mtime +30 -delete

# 每小时清理临时文件
0 * * * * find /www/wwwroot/hawaiihub.net/uploads/temp/ -mtime +1 -delete
```

---

## 🔧 故障排除

### 1. 网站无法访问

#### 检查步骤
```bash
# 1. 检查Nginx状态
systemctl status nginx
systemctl restart nginx

# 2. 检查端口监听
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# 3. 检查域名解析
nslookup hawaiihub.net
ping hawaiihub.net

# 4. 检查SSL证书
openssl x509 -in /www/server/panel/vhost/cert/hawaiihub.net/fullchain.pem -text -noout
```

#### 常见问题
```bash
问题1: 502 Bad Gateway
解决: systemctl restart php7.4-fpm

问题2: 403 Forbidden
解决: chmod 755 /www/wwwroot/hawaiihub.net/

问题3: SSL证书过期
解决: 登录宝塔面板更新SSL证书
```

### 2. 数据库连接问题

#### 检查步骤
```bash
# 1. 检查MySQL状态
systemctl status mysql
systemctl restart mysql

# 2. 测试数据库连接
mysql -u hawaiihub_net -pBkNR4w1KHrXX48by hawaiihub_net

# 3. 检查数据库配置
cat /www/wwwroot/hawaiihub.net/include/dbinfo.inc.php

# 4. 检查数据库日志
tail -f /var/log/mysql/error.log
```

### 3. 文件上传问题

#### 检查步骤
```bash
# 1. 检查目录权限
ls -la /www/wwwroot/hawaiihub.net/uploads/

# 2. 修复权限
chown -R www:www /www/wwwroot/hawaiihub.net/uploads/
chmod 755 /www/wwwroot/hawaiihub.net/uploads/

# 3. 检查PHP上传配置
grep -E "upload_max_filesize|post_max_size|max_file_uploads" /etc/php/7.4/fpm/php.ini

# 4. 检查磁盘空间
df -h /www/
```

### 4. 性能问题排查

#### 系统资源检查
```bash
# CPU使用率
top -n 1 | head -3

# 内存使用情况
free -h

# IO使用情况
iotop -n 1

# 网络连接
ss -tulnp | grep :80
```

#### 应用性能检查
```bash
# PHP进程状态
ps aux | grep php-fpm

# MySQL进程列表
mysql -u hawaiihub_net -pBkNR4w1KHrXX48by -e "SHOW PROCESSLIST;"

# 慢查询日志
tail -f /var/log/mysql/mysql-slow.log
```

### 5. 安全事件处理

#### 发现可疑文件
```bash
# 1. 隔离可疑文件
mv suspicious_file.php /tmp/quarantine/

# 2. 分析文件内容
cat /tmp/quarantine/suspicious_file.php

# 3. 检查访问日志
grep "suspicious_file.php" /www/wwwlogs/hawaiihub.net.log

# 4. 恢复文件权限
chmod 644 /www/wwwroot/hawaiihub.net/*.php
```

#### 发现异常访问
```bash
# 1. 封禁恶意IP
iptables -A INPUT -s 恶意IP -j DROP

# 2. 分析访问模式
awk '{print $1}' /www/wwwlogs/hawaiihub.net.log | sort | uniq -c | sort -nr

# 3. 检查用户行为
grep "恶意IP" /www/wwwlogs/hawaiihub.net.log | awk '{print $7}' | sort | uniq -c
```

---

## 📞 应急联系

### 技术支持
- **运维负责人**: 系统管理员
- **技术支持**: <EMAIL>
- **紧急电话**: 0512-67581578
- **监控告警**: <EMAIL>

### 重要文档位置
- **配置文件**: `/www/wwwroot/hawaiihub.net/include/config/`
- **日志文件**: `/www/wwwlogs/hawaiihub.net.*`
- **备份文件**: `/backup/`
- **监控脚本**: `/root/monitor_hawaiihub.sh`

---

**文档版本**: v1.0  
**最后更新**: 2025年6月17日  
**维护人员**: 系统运维团队