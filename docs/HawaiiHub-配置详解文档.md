# 🏝️ HawaiiHub 配置详解文档

## 📋 目录
- [数据库配置](#数据库配置)
- [网站核心配置](#网站核心配置)
- [服务器配置](#服务器配置)
- [安全配置](#安全配置)
- [第三方集成](#第三方集成)
- [移动端配置](#移动端配置)

---

## 🗄️ 数据库配置

### 数据库连接配置
**文件位置**: `/www/wwwroot/hawaiihub.net/include/dbinfo.inc.php`

```php
<?php
// 数据库连接信息
$DB_HOST = 'localhost';              // 数据库服务器地址
$DB_NAME = 'hawaiihub_net';          // 数据库名称
$DB_USER = 'hawaiihub_net';          // 数据库用户名
$DB_PASS = 'BkNR4w1KHrXX48by';      // 数据库密码 (生产环境)
$DB_PREFIX = 'hn_';                  // 数据表前缀
$DB_CHARSET = 'utf8';                // 数据库字符编码
?>
```

### 数据库结构
- **数据库名**: hawaiihub_net
- **表总数**: 490张数据表
- **表前缀**: hn_
- **编码格式**: utf8
- **核心表分类**:
  - 用户系统: hn_member*, hn_user*, hn_admin*
  - 内容管理: hn_article*, hn_forum*, hn_news*
  - 商业功能: hn_business*, hn_shop*, hn_payment*
  - 系统配置: hn_config*, hn_site*, hn_setting*

---

## ⚙️ 网站核心配置

### 主配置文件
**文件位置**: `/www/wwwroot/hawaiihub.net/include/config/siteConfig.inc.php`

#### 基本信息配置
```php
// 网站基本信息
$cfg_basehost = 'hawaiihub.net';                    // 主域名
$cfg_webname = '夏威夷资讯站';                        // 网站名称
$cfg_shortname = '华人新闻';                         // 网站简称
$cfg_shareTitle = '夏威夷资讯站';                     // 分享标题
$cfg_shareDesc = '夏威夷华人必备助手，新闻、招聘、房产、社群一站搞定，尽享本地优质服务！';
```

#### SEO配置
```php
// SEO优化配置
$cfg_keywords = '夏威夷生活, 华人资讯平台, 本地招聘, 房产租售, 社区互动, 一站式服务';
$cfg_description = '我们是一家专注夏威夷华人社区的综合信息网站系统，支持 APP、微信、小程序、H5、PC 五端实时同步。整合新闻资讯、招聘求职、房产租赁、社区互动、优惠团购等功能模块，专业的本地化服务，助您轻松融入夏威夷生活。';
```

#### 版权和备案信息
```php
// 版权信息
$cfg_beian = '© 2025 HawaiiHub 夏威夷华人生活平台 版权所有 | 苏ICP备12345678号-1';
$cfg_hotline = '0512-67581578';                      // 客服电话
$cfg_powerby = '<p style="text-align: center;">© 2025 HawaiiHub 夏威夷华人生活平台 版权所有 | 京CP备12345678号-1</p>';
```

#### 模板配置
```php
// 模板设置
$cfg_template = 'skin10';                           // PC端模板
$cfg_touchTemplate = 'skin4';                       // 移动端模板
$cfg_lang = 'zh-CN';                                // 默认语言
$cfg_timeZone = 8;                                  // 时区 (UTC+8)
$cfg_mapCity = '苏州';                              // 地图默认城市
```

#### 系统功能配置
```php
// 系统开关
$cfg_visitState = 0;                                // 访问状态 (0:正常 1:维护)
$cfg_visitMessage = '网站正在维护中，请稍后访问！！';  // 维护提示
$cfg_pcState = 0;                                   // PC端开关
$cfg_urlRewrite = '1';                              // URL重写
$cfg_bindMobile = '1';                              // 强制绑定手机
$cfg_member_card = '0';                             // 会员卡功能
$cfg_httpSecureAccess = '1';                        // HTTPS访问
```

### 上传配置
```php
// 文件上传设置
$cfg_uploadDir = '/uploads';                        // 上传目录
$cfg_thumbSize = 5120;                              // 缩略图大小限制(KB)
$cfg_photoSize = 51200;                             // 图片大小限制(KB)
$cfg_videoSize = 1024000;                           // 视频大小限制(KB)
$cfg_thumbType = 'jpg|jpeg|gif|png|swf|webp|svg';   // 允许的图片格式
$cfg_videoType = 'flv|swf|wmv|mp4|avi|asf|f4v|mov|webm'; // 允许的视频格式

// 图片尺寸设置
$cfg_thumbSmallWidth = 250;                         // 小图宽度
$cfg_thumbSmallHeight = 200;                        // 小图高度
$cfg_thumbMiddleWidth = 350;                        // 中图宽度
$cfg_thumbMiddleHeight = 300;                       // 中图高度
$cfg_thumbLargeWidth = 450;                         // 大图宽度
$cfg_thumbLargeHeight = 400;                        // 大图高度

// 图片质量和压缩
$cfg_imageCompress = '1';                           // 开启图片压缩
$cfg_quality = 100;                                 // 压缩质量
$cfg_photoCutType = 'scale';                        // 裁剪方式
$cfg_photoCutPostion = 'center';                    // 裁剪位置
```

### 安全配置
```php
// 内容安全
$cfg_replacestr = '色情|赌博|诈骗|涉政|暴力|恐怖主义|谣言|极端|毒品|赌博|未成年人|侵权|外挂|木马|病毒|敏感信息|泄露|涉黄|涉恐';

// 域名安全
$cfg_secure_domain = 'weibo.com|apple.com|baidu.com|kumanyun.com|vivo.com.cn';

// IP访问限制
$cfg_iplimit = '174.258.124.156
186.15.
203.208.60.';

// 登录安全
$cfg_errLoginCount = 10;                            // 错误登录次数限制
$cfg_loginLock = 15;                                // 锁定时间(分钟)
$cfg_smsLoginState = 1;                             // 短信登录开关
$cfg_smsAutoRegister = 0;                           // 短信自动注册
```

### 会员系统配置
```php
// 注册设置
$cfg_regstatus = 1;                                 // 注册开关
$cfg_regverify = 1;                                 // 注册审核
$cfg_regtime = 1;                                   // 注册时间限制
$cfg_regtype = '2,3';                               // 注册方式
$cfg_holduser = 'admin|123456|user';                // 保留用户名

// 会员验证
$cfg_memberVerified = 1;                            // 实名认证
$cfg_memberVerifiedInfo = '请先进行实名认证！';
$cfg_memberBindPhone = 1;                           // 手机绑定
$cfg_memberBindPhoneInfo = '请先进行手机绑定！';
$cfg_memberFollowWechat = 0;                        // 关注公众号
$cfg_memberFollowWechatInfo = '请先关注公众号！';

// 用户行为
$cfg_nicknameEditState = '0';                       // 昵称修改开关
$cfg_avatarEditState = '0';                         // 头像修改开关
$cfg_avatarEditAudit = '1';                         // 头像审核
$cfg_disableCommentState = '0';                     // 禁用评论
$cfg_disableLikeState = '0';                        // 禁用点赞
```

---

## 🌐 服务器配置

### Nginx虚拟主机配置
**文件位置**: `/www/server/panel/vhost/nginx/hawaiihub.net.conf`

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name hawaiihub.net;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/hawaiihub.net;

    # SSL证书配置
    ssl_certificate    /www/server/panel/vhost/cert/hawaiihub.net/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/hawaiihub.net/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    
    # 强制HTTPS重定向
    error_page 497 https://$host$request_uri;

    # PHP-FPM配置
    include enable-php-74.conf;

    # URL重写规则
    include /www/server/panel/vhost/rewrite/hawaiihub.net.conf;

    # 安全配置
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404;
    }

    # 静态文件缓存
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
        expires 30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$ {
        expires 12h;
        error_log /dev/null;
        access_log /dev/null;
    }

    # 日志配置
    access_log /www/wwwlogs/hawaiihub.net.log;
    error_log /www/wwwlogs/hawaiihub.net.error.log;
}
```

### URL重写规则
**文件位置**: `/www/wwwroot/hawaiihub.net/.htaccess`

```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  ErrorDocument 404 /404.html

  # 安全头部设置
  Header unset X-Original-URL
  Header unset X-Rewrite-URL

  # 禁用OPTIONS方法
  RewriteCond %{REQUEST_METHOD} ^(OPTIONS)
  RewriteRule .* - [F]

  # 主重写规则
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteRule ^(.*)$ index.php [NC,QSA]
</IfModule>
```

---

## 📱 移动端配置

### APP配置文件
**文件位置**: `/www/wwwroot/hawaiihub.net/api/appConfig.json`

```json
{
    "cfg_basehost": "https://hawaiihub.net",
    "cfg_user_index": "https://hawaiihub.net/u",
    "cfg_business_index": "https://hawaiihub.net/b",
    "cfg_webname": "夏威夷资讯站",
    "cfg_shortname": "华人新闻",
    "cfg_wechatName": "火鸟门户",
    "cfg_business_state": 1,
    "cfg_miniProgramBindPhone": "1",
    "cfg_useWxMiniProgramLogin": 0,
    "cfg_miniProgramLocationAuth": "chooseLocation",
    "cfg_iosVirtualPaymentState": 0,
    "cfg_iosVirtualPaymentTip": "iOS端小程序不支持该功能",
    "language": "zh-CN",
    "cfg_timeZone": 8,
    "cfg_timeZoneName": "Etc/GMT-8",
    
    // 应用信息配置
    "cfg_app_info": {
        "android": {
            "version": "",
            "update": "",
            "size": "",
            "note": "",
            "url": "",
            "force": "",
            "timestamp": ""
        },
        "ios": {
            "version": "",
            "update": "",
            "note": "",
            "url": "",
            "force": "",
            "timestamp": ""
        },
        "basic": {
            "name": "",
            "subtitle": "",
            "logo": "",
            "wx_appid": "",
            "URLScheme_Android": "",
            "URLScheme_iOS": ""
        }
    },
    
    // 货币配置
    "currency": {
        "name": "人民币",
        "short": "元",
        "symbol": "¥",
        "code": "RMB",
        "rate": "1"
    },
    
    // 分享配置
    "share": {
        "title": "夏威夷资讯站",
        "description": "夏威夷华人必备助手，新闻、招聘、房产、社群一站搞定，尽享本地优质服务！"
    },
    
    // 协议链接
    "protocol": {
        "用户注册协议": "https://hawaiihub.net/protocol.html",
        "隐私政策": "https://hawaiihub.net/protocol.html",
        "商家注册协议": "https://hawaiihub.net/protocol.html?from=wmsj",
        "商家隐私政策": "https://hawaiihub.net/protocol.html?from=wmsj",
        "骑手注册协议": "https://hawaiihub.net/protocol.html?from=wmsj",
        "骑手隐私政策": "https://hawaiihub.net/protocol.html?from=wmsj",
        "打赏须知": "https://hawaiihub.net/protocol.html"
    },
    
    // 地图配置
    "cfg_map": {
        "baidu": {"android": "", "ios": ""},
        "google": {"android": "", "ios": ""},
        "amap": {"android": "", "ios": ""}
    },
    
    // 登录配置
    "cfg_loginconnect": {
        "qq": {"appid": "", "appkey": ""},
        "wechat": {"appid": "", "appsecret": ""},
        "sina": {"akey": "", "skey": ""}
    },
    
    // 安全域名
    "cfg_secureDomain": [
        "hawaiihub.net",
        "ihuoniao.cn", 
        "kumanyun.com",
        "beian.miit.gov.cn",
        "qq.com",
        "baidu.com",
        "amap.com",
        "google.com",
        "weibo.com",
        "apple.com",
        "vivo.com.cn"
    ],
    
    // 城市信息
    "site_city_info": {
        "id": 1,
        "cityid": 34,
        "domain": "taiwan",
        "url": "https://hawaiihub.net",
        "link": "https://hawaiihub.net",
        "name": "台湾",
        "pinyin": "taiwan",
        "type": 2,
        "default": 1,
        "hot": 1,
        "lat": 25.040941981936,
        "lng": 121.57463095526,
        "count": 1
    }
}
```

### 城市系统配置
**文件位置**: `/www/wwwroot/hawaiihub.net/system_site_city.json`

```json
{
    "expire_time": 1749839225,
    "data": [{
        "id": 1,
        "cityid": 34,
        "domain": "taiwan",
        "url": "https://hawaiihub.net",
        "link": "https://hawaiihub.net", 
        "name": "台湾",
        "pinyin": "taiwan",
        "type": 2,              // 城市类型: 2=主站
        "default": 1,           // 默认城市
        "hot": 1,               // 热门城市
        "lat": 25.040941981936, // 纬度
        "lng": 121.57463095526, // 经度
        "count": 1              // 城市数量
    }]
}
```

---

## 🔐 第三方集成

### 支付配置
系统支持19种支付方式，配置文件位于各模块配置中：

```php
// 微信支付
$cfg_wechatPay_appid = '';          // 微信AppID
$cfg_wechatPay_mchid = '';          // 商户号
$cfg_wechatPay_key = '';            // 支付密钥

// 支付宝
$cfg_alipay_appid = '';             // 支付宝AppID
$cfg_alipay_private_key = '';       // 应用私钥
$cfg_alipay_public_key = '';        // 支付宝公钥

// 其他支付方式
$cfg_paypal_client_id = '';         // PayPal客户端ID
$cfg_stripe_public_key = '';        // Stripe公钥
$cfg_stripe_secret_key = '';        // Stripe私钥
```

### 地图服务配置
```php
// Google地图
$cfg_map_google = 'AIzaSyCGkzu4xH35eOFC1qPw10y_tKjvEJ-vDLw';

// 百度地图
$cfg_map_baidu = '';
$cfg_map_baidu_server = '';
$cfg_map_baidu_wxmini = '';

// 高德地图  
$cfg_map_amap = '';
$cfg_map_amap_server = '';
$cfg_map_amap_jscode = '';

// 腾讯地图
$cfg_map_tmap = '';
$cfg_map_tmap_server = '';
```

### 云存储配置
```php
// 阿里云OSS
$cfg_OSSUrl = '';
$cfg_OSSBucket = '';
$cfg_EndPoint = '';
$cfg_OSSKeyID = '';
$cfg_OSSKeySecret = '';

// 腾讯云COS
$cfg_COSUrl = '';
$cfg_COSBucket = '';
$cfg_COSRegion = '';
$cfg_COSSecretid = '';
$cfg_COSSecretkey = '';

// 七牛云
$cfg_QINIUAccessKey = '';
$cfg_QINIUSecretKey = '';
$cfg_QINIUbucket = '';
$cfg_QINIUdomain = '';

// 华为云OBS
$cfg_OBSUrl = '';
$cfg_OBSBucket = '';
$cfg_OBSEndpoint = '';
$cfg_OBSKeyID = '';
$cfg_OBSKeySecret = '';
```

### 短信服务配置
```php
// 阿里大鱼
$cfg_smsAlidayu = 0;
$cfg_sms_aliyun_accesskey = '';
$cfg_sms_aliyun_secretkey = '';
$cfg_sms_aliyun_signname = '';
$cfg_sms_aliyun_template = '';

// 腾讯云短信
$cfg_sms_tencent_appid = '';
$cfg_sms_tencent_appkey = '';
$cfg_sms_tencent_signname = '';
$cfg_sms_tencent_template = '';
```

### 邮件服务配置
```php
// SMTP邮件
$cfg_mail = 0;                      // 邮件开关
$cfg_mailServer = '';               // SMTP服务器
$cfg_mailPort = '465';              // SMTP端口
$cfg_mailFrom = '';                 // 发件人邮箱
$cfg_mailUser = '';                 // SMTP用户名
$cfg_mailPass = '';                 // SMTP密码
```

---

## 🔧 模块配置详解

### 文章系统配置
**文件位置**: `/www/wwwroot/hawaiihub.net/include/config/article.inc.php`

```php
// 文章发布配置
$article_post_verify = 1;           // 发布审核
$article_post_verify_vip = 0;       // VIP免审核
$article_edit_verify = 1;           // 编辑审核
$article_comment_verify = 0;        // 评论审核
$article_praise_state = 1;          // 点赞功能
$article_comment_state = 1;         // 评论功能
$article_share_state = 1;           // 分享功能
```

### 商家系统配置
**文件位置**: `/www/wwwroot/hawaiihub.net/include/config/business.inc.php`

```php
// 商家认证配置
$business_verify_state = 1;         // 认证开关
$business_verify_price = 100;       // 认证费用
$business_verify_expire = 365;      // 认证有效期(天)
$business_comment_state = 1;        // 评论功能
$business_praise_state = 1;         // 点赞功能
```

### 会员系统配置
**文件位置**: `/www/wwwroot/hawaiihub.net/include/config/member.inc.php`

```php
// 会员等级配置
$member_level_state = 1;            // 等级系统
$member_level_upgrade = 'auto';     // 升级方式
$member_vip_state = 1;              // VIP功能
$member_point_state = 1;            // 积分系统
$member_sign_state = 1;             // 签到功能
```

---

**文档版本**: v1.0  
**最后更新**: 2025年6月17日  
**维护人员**: 系统运维团队