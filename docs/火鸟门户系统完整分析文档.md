# 🔥 火鸟门户系统完整分析文档

## 📋 项目概述

**项目名称**：火鸟门户系统初始化安装包  
**项目路径**：`/Users/<USER>/Desktop/火鸟门户初始化安装包 2/hawaiihub/`  
**原始包大小**：224MB（包含268MB的system.zip）  
**系统类型**：基于自研MVC框架的PHP门户系统  
**开发商**：杭州酷曼公司（https://www.kumanyun.com/）  
**应用场景**：夏威夷华人平台（hawaiihub.net）  
**文档创建时间**：2024年12月30日

---

## 🏗️ 系统架构概览

### 双层结构设计
火鸟门户采用了独特的双层安装结构：

1. **核心安装包**（3.3MB）- 精简版启动器
2. **完整系统包**（268MB）- 完整功能模块

### 技术特点
- 🔒 **核心文件加密**：使用 Swoole Loader 加密核心业务逻辑
- 🏢 **企业级架构**：自研MVC框架，支持大型门户网站
- 🔧 **模块化设计**：功能模块独立，便于维护和扩展
- 🌐 **多平台支持**：Linux、Windows、Mac平台兼容

---

## 📊 系统规模统计

| 项目 | 数量/大小 | 说明 |
|------|-----------|------|
| 总文件数 | 21,100+ | 包含完整系统的所有文件 |
| PHP文件数 | 3,320+ | 核心业务逻辑文件 |
| 核心安装包 | 3.3MB | 启动器和基础文件 |
| 完整系统包 | 268MB | system.zip解压后内容 |
| GitHub仓库 | 私人仓库 | firebird-portal-installer |
| 数据库表数 | 490张 | hn_前缀命名规范 |

---

## 📁 完整目录结构分析

### 一、核心安装包结构（核心启动器）

```
火鸟门户初始化安装包/
├── .git/                        # Git版本控制目录
├── .gitignore                   # Git忽略文件配置
├── .gitattributes              # Git LFS配置文件
├── .DS_Store                   # Mac系统文件
│
├── huoniao                     # 🚀 主程序入口（1.2KB，Linux/Mac）
├── huoniao.dll                 # 🚀 Windows平台动态库（1.0MB）
├── huoniao.so                  # 🚀 Linux/Mac平台动态库（1.4MB）
├── index.php                   # 🌐 Web入口文件（766B）
│
├── include/                    # 🏗️ 核心类库目录
│   └── class/
│       ├── file.class.php      # 文件操作类
│       └── httpdown.class.php  # HTTP下载类
│
└── install/                    # 🛠️ 安装程序目录
    ├── check.txt               # 安装检查文件
    ├── db_default.txt          # 默认数据库配置
    ├── db_structure.txt        # 数据库结构文件
    ├── dbinfo.inc.php          # 数据库信息配置
    ├── index.php               # 安装程序主文件
    │
    ├── module/                 # 📦 核心模块包
    │   └── system/
    │       └── system.zip      # 🔥 完整系统包（268MB）
    │
    └── templates/              # 🎨 安装界面模板
        ├── css/                # 样式文件
        ├── images/             # 图片资源（15个文件）
        ├── js/                 # JavaScript文件
        └── index.html          # 安装界面模板
```

### 二、完整系统结构（system.zip解压后）

```
temp_analysis/
├── admin/                      # 🛡️ 后台管理系统
├── api/                        # 🔌 API接口层
├── data/                       # 💾 数据目录
├── design/                     # 🎨 设计工具
├── include/                    # 📚 核心类库
├── static/                     # 📦 静态资源
├── templates/                  # 🎭 前端模板
├── wmsj/                       # 🏪 商城系统
├── index.php                   # 🌐 主入口文件（75KB）
├── special.php                 # 🌟 特殊页面处理（61KB）
└── website.php                 # 🏠 网站配置（95KB）
```

---

## 🎯 核心功能模块分析

### 1. 后台管理系统（admin/）

#### 应用管理（app/）
- **appConfig.php** - 应用配置管理
- **audioVideoProcess.php** - 音视频处理
- **pushConfig.php** - 推送配置

#### 商务管理（business/）
- **businessAbout.php** - 商户介绍管理
- **businessAdd.php** - 商户添加
- **businessAddr.php** - 商户地址管理
- **businessArticle.php** - 商户文章管理
- **businessCertification.php** - 商户认证
- **businessConfig.php** - 商户配置
- **businessEdit.php** - 商户编辑
- **businessFund.php** - 商户资金管理
- **businessGroup.php** - 商户分组
- **businessList.php** - 商户列表

#### 会员管理（member/）
- **adminEdit.php** - 管理员编辑
- **adminGroup.php** - 管理员分组
- **adminList.php** - 管理员列表
- **memberAdd.php** - 会员添加
- **memberEdit.php** - 会员编辑
- **memberList.php** - 会员列表
- **memberConfig.php** - 会员配置

#### 网站配置（siteConfig/）
- **advAdd.php** - 广告添加
- **advCityAdd.php** - 城市广告添加
- **advCityList.php** - 城市广告列表
- **siteConfig.php** - 网站基础配置
- **cityConfig.php** - 城市配置

### 2. API接口系统（api/）

#### 论坛集成（bbs/）
- **discuz/** - Discuz论坛集成
- **phpwind/** - PHPWind论坛集成

#### 登录集成（login/）
- **alipay/** - 支付宝登录
- **baidu/** - 百度登录
- **facebook/** - Facebook登录
- **qq/** - QQ登录
- **sina/** - 新浪微博登录
- **wechat/** - 微信登录

#### 支付系统（payment/）
- **alipay/** - 支付宝支付
- **paypal/** - PayPal支付
- **wxpay/** - 微信支付
- **unionpay/** - 银联支付
- **globalalipay/** - 国际支付宝
- **fomopay_paynow/** - FomoPay PayNow
- **fomopay_wxpay/** - FomoPay微信支付

#### 直播系统（live/）
- **alilive/** - 阿里云直播

#### 上传系统（upload/）
- **aliyun/** - 阿里云OSS
- **huawei/** - 华为云OBS
- **Qiniu/** - 七牛云存储
- **tencent/** - 腾讯云COS

#### 核心处理器（handlers/）
- **business.class.php** - 商务类
- **business.controller.php** - 商务控制器
- **handlers.class.php** - 处理器基类

### 3. 设计工具系统（design/）

#### 可视化编辑工具
- **control/** - 控件库（36个文件）
  - background.css/js - 背景控件
  - border.css/js - 边框控件
  - button.css/js - 按钮控件
  - text.css/js - 文本控件
  - image.css/js - 图片控件

#### DIY自定义（diy/）
- **additive.css/js** - 附加功能
- **clipboard.css/js** - 剪贴板
- **dock/** - 停靠栏
- **page/** - 页面管理
- **panel/** - 面板管理
- **widget/** - 小部件

#### 小部件库（widgets/）
- **360qjlist/** - 360全景列表
- **area/** - 区域选择
- **articlelist/** - 文章列表
- **articletype/** - 文章分类
- **audio/** - 音频播放器
- **autolist/** - 自动列表
- **body/** - 页面主体
- **button/** - 按钮组件

### 4. 核心类库（include/）

#### 系统类库（class/）
- **59个PHP类文件** + **10个子目录**
- 文件操作、数据库操作、缓存、邮件、短信等核心功能

#### 配置文件（config/）
- **9个配置文件** - 系统配置、数据库配置等

#### 定时任务（cron/）
- **73个计划任务文件** - 自动化处理脚本

#### 第三方集成（vendor/）
- **12个第三方库目录** - 包含各种开源库集成

### 5. 前端模板系统（templates/）

#### 功能模板
- **about/** - 关于我们
- **business/** - 商务模板
- **certification/** - 认证模板
- **courier/** - 快递模板
- **feedback/** - 反馈模板
- **help/** - 帮助中心
- **member/** - 会员中心（279个文件）
- **notice/** - 通知公告
- **store/** - 商店模板

#### 系统模板
- **diy/** - DIY模板
- **siteConfig/** - 网站配置模板
- **mytag/** - 标签模板

### 6. 商城系统（wmsj/）

#### 核心功能
- **shop/** - 商店管理（17个文件）
- **order/** - 订单管理（5个文件）
- **message/** - 消息管理（2个文件）
- **statistics/** - 统计分析（7个文件）

---

## 🔧 技术栈分析

### 后端技术
- **PHP 7.4+** - 主要编程语言
- **自研MVC框架** - 核心架构
- **Swoole Loader** - 代码加密保护
- **MySQL** - 数据库系统（490张表）
- **Redis** - 缓存系统

### 前端技术
- **jQuery** - JavaScript框架
- **Bootstrap** - UI框架
- **CSS3/HTML5** - 现代Web标准
- **Less** - CSS预处理器
- **响应式设计** - 移动端适配

### 第三方集成
- **支付系统**：支付宝、微信、PayPal、银联等
- **登录系统**：QQ、微信、支付宝、Facebook等
- **云存储**：阿里云、腾讯云、七牛云、华为云
- **直播系统**：阿里云直播
- **论坛系统**：Discuz、PHPWind

---

## 🛠️ 部署环境要求

### 服务器要求
- **操作系统**：Linux（推荐）、Windows、Mac
- **Web服务器**：Apache 2.4+ 或 Nginx 1.8+
- **PHP版本**：PHP 7.4+
- **数据库**：MySQL 5.7+ 或 MariaDB 10.2+
- **扩展要求**：
  - Swoole Loader（必须）
  - GD库（图片处理）
  - cURL（API调用）
  - Redis（缓存）

### 宝塔面板配置
- **当前服务器**：*************
- **SSH连接**：ssh baota（已配置免密登录）
- **PHP版本**：7.4
- **数据库**：MySQL 5.7
- **网站配置**：夏威夷资讯站

---

## 📊 数据库结构分析

### 表命名规范
- **前缀**：huoniao_
- **总表数**：490张
- **主要表类型**：
  - 用户管理表
  - 内容管理表
  - 商户管理表
  - 订单管理表
  - 配置管理表
  - 日志管理表

### 连接信息 (生产环境)
- **主机**: `127.0.0.1`
- **数据库名**: `hawaiihub_net`
- **用户名**: `hawaiihub_net`
- **密码**: `J5yWd4bE3gH2sR6k` (注：敏感信息，仅供内部开发使用)
- **核心配置文件**: `/include/common.inc.php`

---

## 🌐 API接口分析

### 认证系统
- **登录接口**：/api/login.php
- **权限验证**：Token/Session双重验证
- **第三方登录**：支持多平台OAuth2.0

### 核心接口
- **处理器路径**：/api/handlers/
- **业务接口**：business.controller.php
- **支付回调**：各支付平台通知接口
- **上传接口**：多云存储上传接口

---

## 🔌 插件系统分析

本次通过对生产环境服务器的直接分析，我们成功揭示了火鸟系统插件化架构的完整工作机制，并定位到了核心的"信息资讯采集"插件。

### 插件核心架构

火鸟的插件系统是一个管理与运行分离的经典架构：

- **中央管理器**: `admin/siteConfig/plugins.php` 是所有插件的"总控制台"，负责插件的列表展示、状态启停和卸载。
- **插件源码目录**: 所有插件的源代码均存放在 `/include/plugins/` 目录中。
- **寻址方式**: 每个插件在安装时会被分配一个唯一的数字ID（`pid`），其所有代码都存放在以该ID命名的子目录中，例如 `/include/plugins/4/`。
- **后台入口**: 插件的后台管理界面的统一访问入口为 `/include/plugins/{pid}/index.php`。
- **数据存储**: 插件的元信息（如名称、版本、作者、状态等）都集中存储在数据库的 `huoniao_site_plugins` 表中。

### 案例分析：信息资讯采集插件

通过对服务器文件的逐层排查，我们最终确认"信息资讯采集"插件的具体信息如下：

- **插件ID (`pid`)**: `4`
- **源码路径**: `/www/wwwroot/hawaiihub.net/include/plugins/4/` (生产环境)
- **功能**: 从任何外部网站抓取新闻、文章等信息，并自动录入到本系统。
- **核心文件分析**:
  - `index.php`: 插件的后台管理主界面，用于配置"采集节点"。
  - `getNews.php`: 核心采集逻辑脚本，负责执行HTTP请求并抓取网页内容。
  - `insertBodyRules.php`: 内容提取规则的配置和处理逻辑。
  - `HttpDownService.php`: 底层的HTTP下载服务类。
  - `cron.php`: 定时任务脚本，用于被系统调度，实现自动化、周期性的采集。

### 采集规则工作原理

与我们最初推测的不同，采集规则并非手写的PHP脚本，而是一个完全通过后台UI配置、将规则存储在数据库中的结构化系统。

- **UI驱动**: 所有规则都在插件后台通过表单进行配置，无需编写任何代码。
- **结构化存储**: 规则被分解成结构化的数据，存入以 `huoniao_site_plugins_spider_` 为前缀的多张数据库表中（如节点表、规则表等）。
- **完整采集流程**:
  1.  **定义采集节点**: 为每一个目标网站创建一个"采集节点"，相当于一个独立的采集任务。
  2.  **指定列表页URL**: 提供目标网站的文章列表页URL，并可以使用 `(*)` 作为通配符来代表页码，程序会自动遍历。
  3.  **定义文章链接规则**: 在列表页HTML源码中，通过设定一个CSS选择器来精确定位哪些`<a>`标签是文章的链接。
  4.  **定义内容提取规则**: 进入文章页后，为标题、作者、发布时间、正文等每一个字段，分别配置独立的CSS选择器，从而实现对文章内容的精准提取。
  5.  **自动执行**: 系统内置的定时任务机制会调用插件的 `cron.php` 脚本，根据用户设定的更新频率，在后台自动执行以上所有采集和入库流程。

### 如何批量写入规则

既然规则就是数据库记录，批量写入的思路就是**直接批量操作数据库**。

1. **准备数据源**: 将采集规则整理成标准格式文件（如 CSV），列出网站名称、列表页URL、标题选择器、内容选择器等。
2. **编写入库脚本**: 使用PHP或Python编写脚本，连接线上数据库。
3. **循环插入**: 脚本读取数据源，遍历每一行，将数据拼装成SQL `INSERT` 语句，写入 `huoniao_site_plugins_spider_nodes` 和 `huoniao_site_plugins_spider_node_rules` 等相关表中。

这种方法可以高效实现大量采集规则的自动化创建与管理。

---

## 📈 功能特性总结

### 内容管理系统
- ✅ 文章发布与管理
- ✅ 分类管理
- ✅ 标签系统
- ✅ SEO优化
- ✅ 多媒体支持

### 用户系统
- ✅ 用户注册登录
- ✅ 第三方登录集成
- ✅ 用户等级管理
- ✅ 积分系统
- ✅ 会员权限管理

### 商务功能
- ✅ 商户入驻
- ✅ 产品展示
- ✅ 在线支付
- ✅ 订单管理
- ✅ 财务结算

### 营销工具
- ✅ 广告管理
- ✅ 促销活动
- ✅ 优惠券系统
- ✅ 推荐系统
- ✅ 数据统计

### 扩展功能
- ✅ 论坛集成
- ✅ 直播功能
- ✅ 打印服务
- ✅ 地图标注
- ✅ 多语言支持

---

## 🔐 安全特性

### 代码保护
- **Swoole Loader加密** - 核心业务逻辑加密
- **文件权限控制** - 严格的文件访问权限
- **SQL注入防护** - 参数化查询
- **XSS防护** - 输入输出过滤

### 数据安全
- **敏感数据加密** - 用户密码等敏感信息加密存储
- **备份机制** - 定时数据备份
- **日志审计** - 操作日志记录

---

## 📋 GitHub仓库信息

**仓库地址**：https://github.com/Poghappy/firebird-portal-installer  
**仓库类型**：私人仓库  
**上传状态**：✅ 核心文件已上传，❌ system.zip（268MB）因文件过大暂未上传  

### 仓库结构
```
firebird-portal-installer/
├── README.md                   # 项目说明
├── .gitignore                  # 忽略配置
├── .gitattributes             # LFS配置
├── huoniao*                   # 主程序文件
├── index.php                  # Web入口
├── include/                   # 核心类库
└── install/                   # 安装程序
```

---

## 🎯 应用场景：夏威夷华人平台

### 平台定位
- **目标用户**：夏威夷华人社区
- **主要功能**：新闻资讯、分类信息、商户展示、社区交流
- **网站地址**：hawaiihub.net

### 运营模块
- **新闻资讯** - 华人社区新闻发布
- **分类信息** - 房屋租售、求职招聘、二手交易
- **商户黄页** - 华人商户展示推广
- **社区论坛** - 用户交流互动
- **活动发布** - 社区活动组织

---

## 🚀 下一步工作建议

### 1. 系统部署
- [ ] 上传system.zip到服务器
- [ ] 配置数据库连接
- [ ] 设置文件权限
- [ ] 安装Swoole Loader
- [ ] 执行系统安装

### 2. 功能定制
- [ ] 配置夏威夷华人平台主题
- [ ] 设置中英文双语支持
- [ ] 配置第三方登录接口
- [ ] 设置支付系统
- [ ] 配置邮件短信服务

### 3. 内容运营
- [ ] 导入初始内容数据
- [ ] 设置SEO配置
- [ ] 配置广告位
- [ ] 设置用户等级
- [ ] 制定运营策略

### 4. 安全加固
- [ ] 配置SSL证书
- [ ] 设置防火墙规则
- [ ] 配置备份策略
- [ ] 设置监控告警
- [ ] 定期安全检查

---

## 📞 技术支持

**开发商**：杭州酷曼公司  
**官方网站**：https://www.kumanyun.com/  
**技术文档**：系统内置帮助文档  
**社区支持**：火鸟门户用户社区

---

*文档最后更新：2024年12月30日* 

## ⚔️ API 逆向工程实战指南

本指南旨在提供一套完整的、可实操的火鸟门户系统 API 调用方法，帮助开发者从任何外部系统（如 Python 脚本、Java 服务、自动化工具）与平台进行数据交互，实现对核心功能的程序化控制（增、删、改、查）。

所有结论均基于对线上服务器 `/www/wwwroot/hawaiihub.net/` 源码的直接分析。

### 1. API 核心要素

- **统一入口点 (Endpoint)**: 所有的API请求都发往同一个URL，通过参数来区分具体要执行的功能。
  - **线上地址**: `https://hawaiihub.net/api/handlers/`
  - **本地开发/测试地址**: `http://<your_domain>/api/handlers/`

- **认证机制 (Authentication)**: API 的安全认证依赖于 `Cookie` 中存储的用户登录信息。这意味着，任何外部调用**必须首先模拟用户登录**，获取并携带合法的 `Cookie`。
  - **关键 Cookie**: `gs_userinfo`
  - **认证流程**:
    1.  **POST 请求登录接口**: 向 `https://hawaiihub.net/login.php` 发送包含用户名和密码的POST请求。
    2.  **获取并保存 Cookie**: 从登录成功的响应头 (`Set-Cookie`) 中提取 `gs_userinfo` 的值。
    3.  **携带 Cookie 调用 API**: 在后续所有对 API 入口点的请求中，都必须在请求头中附带这个 `Cookie`。

- **请求规约 (Calling Convention)**:
  - **请求方法**: 主要是 `POST`，部分查询功能也支持 `GET`。为保证兼容性，**建议全部使用 POST**。
  - **核心参数**: 每个 API 请求都必须包含以下几个核心参数，以路由到正确的处理逻辑。
    - `service`: **服务名**。对应 `api/handlers/` 目录下的控制器文件名（不含 `.controller.php`）。例如，调用 `business.controller.php` 时，`service` 参数应为 `business`。
    - `action`: **动作名**。对应控制器文件中 `if($action == "...")` 语句块所定义的具体操作。
    - `param`: **业务参数**。一个 JSON 格式的字符串，包含了执行该 `action` 所需的所有业务数据。
  - **数据格式**: 请求体 (Request Body) 通常使用 `application/x-www-form-urlencoded` 格式。

### 2. 实战演练：以Python为例

以下我们将用 Python 演示如何调用 `business.controller.php` 中的 `detail` 动作，来获取指定商家的详细信息。

#### 第 1 步: 模拟登录并获取 Cookie

```python
import requests
import json

# 生产环境服务器地址
BASE_URL = "https://hawaiihub.net"

# 登录凭据 (请替换为真实的测试账号)
LOGIN_PAYLOAD = {
    'username': 'your_test_username',
    'password': 'your_test_password',
    'type': 'json' # 很多登录接口需要这个参数来返回JSON格式的结果
}

# 创建一个会话(Session)，它会自动处理Cookie
session = requests.Session()

try:
    # 1. 发起登录请求
    login_response = session.post(f"{BASE_URL}/login.php", data=LOGIN_PAYLOAD)
    login_response.raise_for_status() # 如果请求失败则抛出异常

    # 2. 检查是否登录成功
    login_data = login_response.json()
    if login_data.get("state") == 100:
        print("✅ 登录成功！")
        # Session 对象已自动保存 Cookie (`gs_userinfo`)
    else:
        print(f"❌ 登录失败: {login_data.get('info')}")
        exit()

except requests.exceptions.RequestException as e:
    print(f"请求异常: {e}")
    exit()

```

#### 第 2 步: 调用 API 获取商家详情

现在，我们使用上一步中建立的 `session`（已包含登录 `Cookie`）来调用 API。

```python
# API 入口
API_ENDPOINT = f"{BASE_URL}/api/handlers/"

# 业务参数: 获取ID为 1 的商家信息
# 注意：param 的值是一个 JSON 字符串
business_params = {
    "id": 1
}

# 构建完整的请求体
api_payload = {
    'service': 'business',
    'action': 'detail',
    'param': json.dumps(business_params) # 将业务参数转为JSON字符串
}

try:
    # 3. 发起API调用请求
    api_response = session.post(API_ENDPOINT, data=api_payload)
    api_response.raise_for_status()

    # 4. 解析并打印结果
    api_data = api_response.json()
    if api_data.get("state") == 100:
        print("✅ API 调用成功！")
        # 打印商家详情
        print(json.dumps(api_data.get("info"), indent=2, ensure_ascii=False))
    else:
        print(f"❌ API 调用失败: {api_data.get('info')}")

except requests.exceptions.RequestException as e:
    print(f"请求异常: {e}")
except json.JSONDecodeError:
    print("返回内容非JSON格式:", api_response.text)

```

### 3. 如何查找 `service` 和 `action`？

- **`service`**:
  - `service` 的名称就是 `api/handlers/` 目录下对应的 `*.controller.php` 文件名（去掉后缀）。
  - 例如 `article.controller.php` 的 `service` 就是 `article`。
  - **关键目录**: `/www/wwwroot/hawaiihub.net/api/handlers/`

- **`action`**:
  - `action` 的名称定义在具体的 `*.controller.php` 文件内部。
  - 你需要打开文件，查找 `if ($action == "...")` 或 `if ($action == '...')` 这样的代码块。双引号或单引号里的字符串就是 `action` 的值。
  - **示例**: 在 `business.controller.php` 中，你可以找到 `if($action == "detail")`，因此获取详情的 `action` 就是 `detail`。

### 4. 增删改查 (CRUD) 映射

| 操作 | HTTP 方法 | 火鸟系统 `action` 命名惯例 | `param` 内容 |
| :--- | :--- | :--- | :--- |
| **创建 (Create)** | `POST` | `add`, `create`, `pub` (发布) | 包含所有必填字段的 JSON 对象 |
| **读取 (Read)** | `POST`/`GET` | `detail`, `list`, `get` | 通常包含 `id` 或分页参数 `page`, `pageSize` |
| **更新 (Update)** | `POST` | `update`, `edit`, `modify` | 必须包含要更新记录的 `id`，以及要修改的字段 |
| **删除 (Delete)** | `POST` | `del`, `delete`, `remove` | 必须包含要删除记录的 `id` |

**要实现对任意模块的增删改查，只需重复以下步骤：**

1.  在 `/www/wwwroot/hawaiihub.net/api/handlers/` 中找到目标模块的控制器文件（如 `job.controller.php`）。
2.  打开文件，找到实现增删改查对应的 `action` 名称（如 `fabu`, `list`, `del` 等）。
3.  分析该 `action` 代码块内从 `$param` 中取了哪些值，确定必需的业务参数。
4.  使用上述 Python 模板，替换 `service`, `action`, 和 `param`，即可完成调用。 