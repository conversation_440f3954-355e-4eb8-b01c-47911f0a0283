# 🎨 火鸟门户系统 - 模板功能映射手册

> **文档目的**: 为AI提供准确的模板文件功能映射，实现精准的功能查询和问题解答
> **创建时间**: 2025-06-30
> **数据来源**: 官方templates目录完整分析

---

## 📋 快速功能导航

### 🔍 按功能查找模板
- [用户中心功能](#用户中心) - member/
- [商家管理功能](#商家管理) - business/
- [系统配置功能](#系统配置) - siteConfig/
- [房产相关功能](#房产模块) - house_*
- [招聘相关功能](#招聘模块) - job_*
- [直播相关功能](#直播模块) - live_*
- [交友相关功能](#交友模块) - dating_*

### 🎯 按场景查找模板
- [用户注册登录](#用户认证) 
- [内容发布管理](#内容管理)
- [订单支付流程](#订单支付)
- [移动端适配](#移动端)

---

## 🏠 用户中心功能

### 👤 用户认证相关
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/login.html` | 用户登录页面 | 用户登录系统 |
| `member/register.html` | 用户注册页面 | 新用户注册 |
| `member/fpwd.html` | 忘记密码页面 | 密码找回 |
| `member/resetpwd.html` | 重置密码页面 | 密码重置 |
| `member/logout.html` | 退出登录页面 | 用户退出 |
| `member/loginFrame.html` | 登录框架页面 | 弹窗登录 |
| `member/login_popup.html` | 登录弹窗 | 快速登录 |

### 📝 内容发布管理
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/fabu.html` | 发布内容主页 | 选择发布类型 |
| `member/fabu-article.html` | 发布文章 | 资讯文章发布 |
| `member/fabu-info.html` | 发布信息 | 分类信息发布 |
| `member/fabu-business.html` | 发布商家信息 | 商家入驻发布 |
| `member/fabu-house.html` | 发布房产信息 | 房产信息发布 |
| `member/fabu-job.html` | 发布招聘信息 | 招聘信息发布 |
| `member/fabu-car.html` | 发布车辆信息 | 二手车发布 |
| `member/fabu-live.html` | 发布直播 | 直播间创建 |

### 📊 内容管理
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/manage.html` | 管理中心主页 | 内容管理入口 |
| `member/manage-article.html` | 文章管理 | 管理已发布文章 |
| `member/manage-info.html` | 信息管理 | 管理分类信息 |
| `member/manage-house.html` | 房产管理 | 管理房产信息 |
| `member/manage-car.html` | 车辆管理 | 管理车辆信息 |
| `member/manage-live.html` | 直播管理 | 管理直播内容 |

### 💰 订单支付系统
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/order.html` | 订单列表 | 查看所有订单 |
| `member/orderdetail.html` | 订单详情 | 查看订单详细信息 |
| `member/pay.html` | 支付页面 | 订单支付 |
| `member/pay_success.html` | 支付成功页面 | 支付完成提示 |
| `member/confirm-order.html` | 确认订单 | 订单确认 |

---

## 🏢 商家管理功能

### 🏪 商家系统
| 目录/文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `business/skin4/` | 商家模板皮肤4 | 商家页面样式 |
| `business/touch/` | 商家移动端模板 | 移动端商家页面 |

---

## ⚙️ 系统配置功能

### 🔧 核心配置模板
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `siteConfig/top.html` | 网站头部 | 全站头部导航 |
| `siteConfig/footer1.html` | 网站底部 | 全站底部信息 |
| `siteConfig/404.html` | 404错误页面 | 页面不存在提示 |
| `siteConfig/error.html` | 错误页面 | 系统错误提示 |
| `siteConfig/changecity.html` | 切换城市 | 多城市切换 |
| `siteConfig/mobile.html` | 移动端检测 | 移动端跳转 |

### 📱 移动端适配
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `siteConfig/touch_top.html` | 移动端头部 | 手机版头部 |
| `siteConfig/touch_bottom.html` | 移动端底部 | 手机版底部 |
| `siteConfig/404_touch.html` | 移动端404页面 | 手机版错误页 |
| `siteConfig/error_touch.html` | 移动端错误页面 | 手机版错误提示 |

### 🔧 系统维护配置
| 功能 | 文件/位置 | 说明 |
|------|----------|------|
| 计划任务执行 | `include/cron.php` | 系统自动化任务核心文件 |
| 计划任务管理 | 后台→系统→基本设置→计划任务管理 | 任务配置界面 |
| 官方配置教程 | https://help.kumanyun.com/help-70-753.html | 详细配置步骤 |

**计划任务主要功能**:
- 📊 数据统计自动更新
- 🗑️ 过期信息自动清理
- 🔄 系统缓存定时刷新
- 📦 订单状态自动检查
- 📧 短信邮件队列处理
- 💾 文件清理和备份

**配置要点**:
- **Linux服务器**: 宝塔面板计划任务，每1分钟执行Shell脚本
- **Windows服务器**: 任务计划程序定时执行PHP脚本
- **执行文件**: `include/cron.php`

---

## 🏠 房产模块

### 🏘️ 房产相关功能
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/fabu-house-sale.html` | 发布房屋出售 | 二手房出售 |
| `member/fabu-house-zu.html` | 发布房屋出租 | 房屋租赁 |
| `member/fabu-house-demand.html` | 发布房屋需求 | 求租求购 |
| `member/house_yuyue.html` | 房屋预约 | 看房预约 |
| `member/house_loupan.html` | 楼盘管理 | 楼盘信息管理 |

---

## 💼 招聘模块

### 👔 招聘相关功能
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/job.html` | 招聘中心 | 招聘功能入口 |
| `member/job-resume.html` | 简历管理 | 个人简历 |
| `member/job-delivery.html` | 投递记录 | 简历投递记录 |
| `member/job-invitation.html` | 面试邀请 | 面试邀请管理 |
| `member/fabu_job_seek.html` | 发布求职信息 | 求职者发布 |

---

## 📺 直播模块

### 🎥 直播相关功能
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/livedetail.html` | 直播详情页 | 观看直播 |
| `member/live_charts.html` | 直播统计 | 直播数据分析 |
| `member/live_gift.html` | 直播礼物 | 礼物赠送 |
| `member/live_income.html` | 直播收入 | 收入统计 |
| `member/live_comment.html` | 直播评论 | 互动评论 |

---

## 💕 交友模块

### 👫 交友相关功能
| 模板文件 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `member/dating-profile.html` | 交友资料 | 个人交友信息 |
| `member/dating-my_user.html` | 我的用户 | 交友用户管理 |
| `member/dating-visit.html` | 访问记录 | 查看访问记录 |
| `member/dating-store_income.html` | 交友收入 | 交友平台收入 |

---

## 🔍 常见问题快速查找

### Q: 用户如何注册登录？
**相关模板**: `member/register.html`, `member/login.html`
**功能流程**: 注册 → 邮箱验证 → 登录 → 完善资料

### Q: 如何发布房产信息？
**相关模板**: `member/fabu-house.html`, `member/fabu-house-sale.html`
**功能流程**: 选择房产类型 → 填写详细信息 → 上传图片 → 发布

### Q: 移动端页面在哪里？
**相关目录**: 各模块下的 `touch/` 目录
**主要文件**: `siteConfig/touch_*` 系列文件

### Q: 支付功能如何实现？
**相关模板**: `member/pay.html`, `member/pay_success.html`, `siteConfig/wxpay.html`
**支付方式**: 微信支付、支付宝、其他第三方支付

---

## 📱 移动端模板映射

每个主要模块都有对应的移动端模板：
- `member/touch/` - 用户中心移动端
- `business/touch/` - 商家系统移动端  
- `siteConfig/touch/` - 系统配置移动端
- 各功能模块的 `touch/` 子目录

---

## 🎯 AI查询优化建议

### 查询模式示例
1. **功能查询**: "用户注册功能在哪个模板？" → `member/register.html`
2. **场景查询**: "房屋出租发布页面" → `member/fabu-house-zu.html`
3. **模块查询**: "直播相关的所有模板" → `member/live_*` 系列
4. **移动端查询**: "手机版登录页面" → `member/touch/login.html`

### 关键词映射
- **注册登录** → register.html, login.html, fpwd.html
- **发布内容** → fabu-*.html 系列
- **管理内容** → manage-*.html 系列
- **订单支付** → order*.html, pay*.html 系列
- **移动端** → touch/ 目录下的文件

---

## 📊 完整模板统计分析

### 🏗️ 模板规模统计
| 模块 | PC端模板数 | 移动端模板数 | 总计 | 主要功能 |
|------|-----------|-------------|------|---------|
| **member/** | 200+ | 300+ | 500+ | 用户中心核心功能 |
| **member/company/** | 150+ | 50+ | 200+ | 企业/商家管理 |
| **siteConfig/** | 30+ | 20+ | 50+ | 系统配置模板 |
| **business/** | 10+ | 10+ | 20+ | 商家展示模板 |
| **其他模块** | 50+ | 30+ | 80+ | 辅助功能模板 |
| **总计** | 440+ | 410+ | 850+ | 完整系统模板 |

### 🎯 核心业务模块详细分析

#### 💼 商家企业系统 (member/company/)
这是一个完整的B2B企业管理系统，包含：

**企业配置管理**:
- `config-*.html` 系列 (20+个) - 各行业企业配置
- `business-config*.html` - 商家基础配置
- `dressup-website.html` - 企业网站装修

**内容发布管理**:
- `fabu-*.html` 系列 (30+个) - 企业内容发布
- `manage-*.html` 系列 (15+个) - 企业内容管理

**订单业务管理**:
- `order-*.html` 系列 (15+个) - 企业订单管理
- `orderdetail-*.html` 系列 (15+个) - 订单详情

**行业专业功能**:
- 装修行业: `renovation-*.html` (10+个)
- 婚庆行业: `marry-*.html` (8+个)
- 旅游行业: `travel-*.html` (10+个)
- 教育行业: `education-*.html` (5+个)
- 家政行业: `homemaking-*.html` (8+个)

#### 🏠 房产系统详细分析
**房产发布**:
- `fabu-house-sale.html` - 二手房出售
- `fabu-house-zu.html` - 房屋出租
- `fabu-house-demand.html` - 求租求购
- `fabu-house-cf.html` - 厂房发布
- `fabu-house-cw.html` - 车位发布
- `fabu-house-sp.html` - 商铺发布
- `fabu-house-xzl.html` - 写字楼发布

**房产服务**:
- `house_yuyue.html` - 看房预约
- `house_loupan.html` - 楼盘管理
- `house_entrust.html` - 房源委托
- `house_baobei.html` - 房产宝贝

#### 💼 招聘系统详细分析
**求职者功能**:
- `job-resume.html` - 简历管理
- `job-delivery.html` - 投递记录
- `job-collections.html` - 收藏职位
- `job-invitation.html` - 面试邀请
- `fabu_job_seek.html` - 发布求职

**企业招聘功能**:
- `company/fabu-website-job.html` - 企业发布职位
- `company/resume.html` - 简历查看
- `company/invitation.html` - 面试邀请管理

#### 📺 直播系统详细分析
**主播功能**:
- `livedetail.html` - 直播间主页
- `live_charts.html` - 直播数据统计
- `live_income.html` - 直播收入
- `live_gift.html` - 礼物管理
- `live_reward.html` - 打赏管理

**观众功能**:
- `live_comment.html` - 直播评论
- `live_userlist.html` - 用户列表
- `live_hongbao.html` - 红包功能

#### 💕 交友系统详细分析
**个人资料**:
- `dating-profile.html` - 交友资料
- `dating-album.html` - 相册管理
- `dating-portrait.html` - 头像设置

**交友互动**:
- `dating-visit.html` - 访问记录
- `dating-my_sendgreet.html` - 发送问候
- `dating-my_receiveapply.html` - 收到申请
- `dating-review.html` - 交友审核

**红娘系统**:
- `dating-store_*.html` 系列 - 红娘店铺管理
- `dating-store_income.html` - 红娘收入

### 📱 移动端模板完整性分析

#### 移动端覆盖率
- **用户中心**: 100%覆盖，300+个移动端模板
- **商家系统**: 80%覆盖，主要功能已适配
- **系统配置**: 90%覆盖，核心功能已适配

#### 移动端特色功能
- **地图定位**: `mapPosi*.html` 系列
- **扫码功能**: 二维码登录、支付
- **触屏优化**: 专门的触屏交互设计
- **APP集成**: 支持APP内嵌页面

### 🎨 样式和资源分析

#### CSS样式文件
- **member/css/**: 200+个CSS文件
- **响应式设计**: 支持多种屏幕尺寸
- **主题皮肤**: 支持多套UI主题

#### JavaScript功能
- **member/js/**: 300+个JS文件
- **交互功能**: 丰富的前端交互
- **AJAX支持**: 异步数据加载
- **地图集成**: 百度地图、高德地图

#### 图片资源
- **member/images/**: 500+个图片文件
- **图标系统**: 完整的图标库
- **UI素材**: 丰富的界面素材

---

## 🚀 AI学习建议

### 📚 推荐的学习方案

#### 方案一：按功能模块学习 (推荐)
1. **先学核心模板** - 用户注册登录、发布管理
2. **再学业务模板** - 房产、招聘、直播等具体业务
3. **最后学企业模板** - 商家管理、企业配置

#### 方案二：按技术层次学习
1. **HTML结构分析** - 理解页面布局和组件
2. **CSS样式分析** - 学习UI设计和响应式
3. **JavaScript功能** - 理解交互逻辑和数据处理

#### 方案三：按使用场景学习
1. **用户端功能** - 普通用户使用的模板
2. **商家端功能** - 企业商家使用的模板
3. **管理端功能** - 系统管理相关模板

### 🔧 创建AI训练数据集

我建议为AI创建以下数据集：

#### 1. 模板功能索引数据集
```json
{
  "template_index": {
    "member/login.html": {
      "function": "用户登录",
      "category": "用户认证",
      "related_files": ["member/register.html", "member/fpwd.html"],
      "api_endpoints": ["/api/login", "/api/user/info"],
      "keywords": ["登录", "认证", "用户"]
    }
  }
}
```

#### 2. 业务流程映射数据集
```json
{
  "business_flows": {
    "user_registration": {
      "steps": [
        "member/register.html",
        "member/registerVerifyEmail.html",
        "member/registerSuccess.html"
      ],
      "description": "用户注册完整流程"
    }
  }
}
```

#### 3. 问答训练数据集
```json
{
  "qa_dataset": [
    {
      "question": "用户如何注册账号？",
      "answer": "用户通过 member/register.html 页面注册，需要填写邮箱、密码等信息，然后通过 member/registerVerifyEmail.html 验证邮箱，最后在 member/registerSuccess.html 完成注册。",
      "related_templates": ["member/register.html", "member/registerVerifyEmail.html"]
    }
  ]
}
```

### 📖 下一步建议

您希望我：

1. **创建详细的模板分析文档** - 分析每个重要模板的具体功能和代码结构
2. **建立模板关系图** - 创建模板之间的调用关系和业务流程图
3. **生成AI训练数据** - 基于模板创建结构化的AI训练数据集
4. **创建快速查询工具** - 开发一个可以快速查找模板功能的工具

请告诉我您的偏好，我会立即开始实施！
