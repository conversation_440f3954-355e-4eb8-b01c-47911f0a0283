# 📚 火鸟门户系统 - 文档总索引

> **整理时间**: 2025-06-30  
> **文档用途**: 统一管理所有文档，提供清晰的导航和查找功能  
> **维护说明**: 新增文档时请及时更新此索引

---

## 🎯 文档分类说明

### 📋 分类原则
- **01-核心学习文档**: AI学习和问答的主要文档
- **02-官方参考文档**: 官方帮助中心和技术文档
- **03-历史项目文档**: HawaiiHub等历史项目文档
- **04-工具配置文档**: 技术配置和工具说明
- **99-待整理文档**: 需要进一步处理的文档

---

## 📖 01-核心学习文档 (AI优先使用)

### 🎓 主要学习文档
| 序号 | 文档名称 | 文件路径 | 用途说明 | 重要程度 |
|------|---------|---------|---------|---------|
| 1.1 | **AI学习完整指南** | `火鸟门户系统-AI学习完整指南.md` | AI学习路径和方法论 | ⭐⭐⭐⭐⭐ |
| 1.2 | **AI问答数据集** | `火鸟门户系统-AI问答数据集.md` | 结构化Q&A训练数据 | ⭐⭐⭐⭐⭐ |
| 1.3 | **模板功能映射手册** | `火鸟门户系统-模板功能映射手册.md` | 模板与功能对应关系 | ⭐⭐⭐⭐⭐ |
| 1.4 | **统一完整分析文档** | `火鸟门户系统-统一完整分析文档.md` | 系统全面分析 | ⭐⭐⭐⭐ |
| 1.5 | **采集插件批量写入指南** | `火鸟门户系统-采集插件批量写入指南.md` | 采集规则批量导入方案 | ⭐⭐⭐⭐⭐ |
| 1.6 | **采集插件实际批量写入方案** | `火鸟门户系统-采集插件实际批量写入方案.md` | 基于官方文档的实用方案 | ⭐⭐⭐⭐⭐ |

### 🔧 使用建议
- **AI问答**: 优先使用1.1-1.3文档
- **系统分析**: 参考1.4文档
- **功能查询**: 使用1.2和1.3文档组合查询
- **采集功能开发**: 使用1.5和1.6文档进行批量采集规则配置
- **实际部署**: 优先使用1.6文档的实用方案

---

## 📚 02-官方参考文档

### 🌐 官方文档索引
| 序号 | 文档名称 | 文件路径 | 用途说明 | 重要程度 |
|------|---------|---------|---------|---------|
| 2.1 | **官方文档索引** | `火鸟门户系统-官方文档索引.md` | 官方帮助中心导航 | ⭐⭐⭐⭐⭐ |
| 2.2 | **官方帮助中心结构** | `help.kumanyun.comhelp-1.html.md` | 完整官方文档结构 | ⭐⭐⭐⭐ |
| 2.3 | **模板制作文档** | `help.kumanyun.comhelp-219-0.html.md` | 官方模板制作指南 | ⭐⭐⭐ |

### 🔗 官方资源链接
- **官方帮助中心**: https://help.kumanyun.com/
- **官方网站**: https://www.kumanyun.com/
- **技术支持**: 通过官方文档索引查找

---

## 🏛️ 03-历史项目文档 (参考用)

### 📋 HawaiiHub项目文档
| 序号 | 文档名称 | 文件路径 | 用途说明 | 状态 |
|------|---------|---------|---------|------|
| 3.1 | 夏威夷华人平台统一文档 | `HawaiiHub-夏威夷华人平台-统一系统文档.md` | 项目总体文档 | 📚 参考 |
| 3.2 | 系统架构文档 | `HawaiiHub-系统架构文档.md` | 技术架构说明 | 📚 参考 |
| 3.3 | 部署运维文档 | `HawaiiHub-完整部署运维文档.md` | 部署和运维指南 | 📚 参考 |
| 3.4 | 配置详解文档 | `HawaiiHub-配置详解文档.md` | 详细配置说明 | 📚 参考 |
| 3.5 | 功能模块清单 | `HawaiiHub-功能模块清单.md` | 功能模块列表 | 📚 参考 |
| 3.6 | 新人入职手册 | `HawaiiHub-新人入职手册.md` | 入职培训文档 | 📚 参考 |
| 3.7 | API接口文档 | `HawaiiHub-API接口文档.md` | API接口说明 | 📚 参考 |

### ⚠️ 注意事项
- 这些文档是历史项目，仅供参考
- 与当前火鸟门户系统可能存在差异
- 建议优先使用火鸟门户系统的官方文档

---

## 🛠️ 04-工具配置文档

### 📋 工具和配置文件
| 序号 | 文档名称 | 文件路径 | 用途说明 | 类型 |
|------|---------|---------|---------|------|
| 4.1 | API接口总览 | `API接口总览.md` | API接口汇总 | 🔧 工具 |
| 4.2 | API目录配置 | `api_catalog.json` | API配置文件 | ⚙️ 配置 |
| 4.3 | 路径更新指南 | `📍 路径更新指南.md` | 路径配置说明 | 📋 指南 |

### 📁 资源文件
- **图片资源**: `image/` 目录
- **截图文件**: `截屏2025-06-30 01.50.05.png`

---

## 🎯 AI使用建议

### 📋 按使用场景分类

#### 🤖 AI问答场景
**推荐文档顺序**:
1. `火鸟门户系统-AI问答数据集.md` - 直接查找已有Q&A
2. `火鸟门户系统-模板功能映射手册.md` - 查找功能对应关系
3. `火鸟门户系统-官方文档索引.md` - 查找官方权威答案

#### 🔍 系统分析场景
**推荐文档顺序**:
1. `火鸟门户系统-AI学习完整指南.md` - 了解学习方法
2. `火鸟门户系统-统一完整分析文档.md` - 获取系统全貌
3. `help.kumanyun.comhelp-1.html.md` - 查看官方完整结构

#### 🛠️ 技术开发场景
**推荐文档顺序**:
1. `火鸟门户系统-官方文档索引.md` - 查找开发相关文档
2. `help.kumanyun.comhelp-219-0.html.md` - 模板制作指南
3. `API接口总览.md` - API接口参考

### ⭐ 重要程度说明
- ⭐⭐⭐⭐⭐ **必读文档**: AI学习和问答的核心文档
- ⭐⭐⭐⭐ **重要文档**: 系统理解和功能查询的重要参考
- ⭐⭐⭐ **参考文档**: 特定场景下的补充资料
- 📚 **参考**: 历史项目文档，仅供参考
- 🔧 **工具**: 工具类文档
- ⚙️ **配置**: 配置文件
- 📋 **指南**: 操作指南

---

## 🔄 文档维护

### 📝 更新原则
1. **新增文档**: 及时更新此索引
2. **文档修改**: 更新相应的用途说明
3. **文档删除**: 从索引中移除对应条目
4. **重要变更**: 更新重要程度标记

### 🎯 优化建议
1. **定期整理**: 每月检查文档结构
2. **内容去重**: 避免重复内容
3. **分类优化**: 根据使用频率调整分类
4. **索引更新**: 保持索引的准确性和时效性

---

## 📞 使用帮助

如果您在使用文档时遇到问题：
1. **查找文档**: 使用此索引快速定位
2. **功能咨询**: 优先查看AI问答数据集
3. **技术问题**: 参考官方文档索引
4. **历史参考**: 可查看HawaiiHub相关文档

**最后更新**: 2025-06-30  
**维护人员**: AI助手  
**文档版本**: v1.0
