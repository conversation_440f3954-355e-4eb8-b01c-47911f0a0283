# 📍 HawaiiHub 路径更新指南

## 🔄 路径迁移说明

由于项目文件已统一迁移到 `hawaiihub/` 目录，所有原有路径引用需要更新。

## 📋 路径对照表

### 原路径 → 新路径

| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `hawaiihub/core-system/` | `hawaiihub/core-system/` | 核心系统文件 |
| `hawaiihub/api-integration/` | `hawaiihub/api-integration/` | API集成脚本 |
| `hawaiihub/platform-docs/` | `hawaiihub/platform-docs/` | 平台技术文档 |
| `hawaiihub/server-access/` | `hawaiihub/server-access/` | 服务器访问密钥 |
| `02-ai-automation/agents/bailian*` | `hawaiihub/ai-agents/bailian/` | 百炼AI代理 |
| `02-ai-automation/agents/aliyun*` | `hawaiihub/ai-agents/aliyun/` | 阿里云代理 |
| `.cursor/rules/hawaii*` | `hawaiihub/docs/cursor-rules/` | Cursor规则配置 |

## 🔧 需要更新的文件类型

### 1. 配置文件
- `package.json` - 脚本路径
- `.env` 文件 - 文件路径配置
- `config.json` - 配置文件路径

### 2. 文档文件
- `README.md` - 文档链接
- `*.md` 文档 - 相对路径引用
- API文档 - 示例路径

### 3. 代码文件
- Python脚本 - import路径
- PHP文件 - include路径
- JavaScript - require路径

## 🛠️ 批量更新命令

### 查找需要更新的文件
```bash
# 查找包含旧路径的文件
grep -r "01-hawaiihub-platform" hawaiihub/
grep -r "02-ai-automation/agents" hawaiihub/
grep -r ".cursor/rules" hawaiihub/
```

### 批量替换路径
```bash
# 替换文档中的路径引用
find hawaiihub/ -name "*.md" -exec sed -i '' 's|hawaiihub/|hawaiihub/|g' {} \;
find hawaiihub/ -name "*.md" -exec sed -i '' 's|02-ai-automation/agents/|hawaiihub/ai-agents/|g' {} \;
find hawaiihub/ -name "*.md" -exec sed -i '' 's|.cursor/rules/|hawaiihub/docs/cursor-rules/|g' {} \;
```

## 📍 IDE工作区配置

### VS Code / Cursor 配置
```json
{
  "folders": [
    {
      "name": "HawaiiHub",
      "path": "./hawaiihub"
    }
  ],
  "settings": {
    "files.exclude": {
      "backup-before-cleanup": true,
      "02-ai-automation": true,
      "03-development-tools": true,
      "04-documentation": true,
      "05-archive": true
    }
  }
}
```

### 项目根目录设置
```bash
# 设置hawaiihub为项目根目录
cd hawaiihub
code . # 或 cursor .
```

## ✅ 验证清单

- [ ] 所有文档链接正常
- [ ] 代码导入路径正确
- [ ] 配置文件路径更新
- [ ] IDE工作区配置完成
- [ ] 测试关键功能正常

## 🚨 注意事项

1. **备份重要**：原始文件已备份到 `backup-before-cleanup/`
2. **逐步更新**：建议分批更新，测试后再继续
3. **路径检查**：更新后务必检查所有路径是否正确
4. **团队同步**：如有团队成员，需同步更新

---

**📌 记住：hawaiihub 现在是唯一的项目入口！** 