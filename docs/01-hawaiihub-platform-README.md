# 🏝️ 夏威夷华人平台（火鸟门户系统）

> **重要说明**：火鸟门户就是夏威夷华人平台的底层系统！

## 📁 目录说明

- `core-system/` - 火鸟门户核心系统文件
- `platform-docs/` - 平台架构和功能文档  
- `api-integration/` - API集成脚本
- `server-access/` - 服务器访问凭证

## 🔍 常见查询场景

| 查询类型 | 推荐路径 | 说明 |
|---------|---------|------|
| **系统架构问题** | `platform-docs/🏝️ HawaiiHub 夏威夷华人平台 - 网站文件结构详解` | 完整的平台架构说明 |
| **核心功能查询** | `core-system/include/` | 核心库，包含主要业务逻辑 |
| **数据库结构** | `core-system/install/db_structure.txt` | 490张数据表的完整结构 |
| **API操作** | `api-integration/hawaiihub-api-agent.py` | API代理脚本示例 |
| **服务器运维** | `server-access/154.40.47.153_id_ed25519` | SSH密钥，可用ssh baota连接 |

## 🔗 关键文件

### 核心系统文件
- `core-system/huoniao.dll/so` - 火鸟门户系统核心（加密）
- `core-system/include/class/` - 核心类库文件
- `core-system/install/` - 安装程序和数据库结构
- `core-system/index.php` - 系统入口文件

### 平台文档
- `platform-docs/🏝️ HawaiiHub 夏威夷华人平台 - 网站文件结构详解` - **最重要的架构文档**
- `platform-docs/火鸟门户插件文件结构详解.md` - 插件架构说明
- `platform-docs/documentation/` - 详细技术文档

### API集成
- `api-integration/hawaiihub-api-agent.py` - 夏威夷华人平台API代理脚本

### 服务器访问
- `server-access/154.40.47.153_id_ed25519` - SSH密钥文件
- 服务器地址：154.40.47.153
- 连接方式：`ssh baota`（已配置免密登录）
- 管理面板：宝塔面板
- 运行环境：PHP 7.4 + MySQL

## 🎯 技术架构

- **框架**：自研MVC框架
- **数据库**：MySQL，490张数据表（hn_前缀）
- **核心文件**：swoole_loader加密
- **API路径**：/api/handlers/
- **认证方式**：login.php
- **控制器**：handlers目录

## 🚀 快速开始

1. **了解平台架构** → 查看 `platform-docs/🏝️ HawaiiHub 夏威夷华人平台 - 网站文件结构详解`
2. **查看核心功能** → 进入 `core-system/include/`
3. **理解数据结构** → 查看 `core-system/install/db_structure.txt`
4. **API集成开发** → 参考 `api-integration/hawaiihub-api-agent.py`
5. **服务器运维** → 使用 `ssh baota` 连接服务器

---

> 💡 **提示**：当遇到平台相关问题时，优先查看本目录下的文档和核心文件，必要时可以远程登录服务器查看线上环境。 