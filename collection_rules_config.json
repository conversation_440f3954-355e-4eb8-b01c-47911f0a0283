{"collection_batch_import": {"version": "1.0", "description": "火鸟门户系统采集插件批量配置 - 基于官方文档", "created_time": "2025-06-30", "author": "AI Assistant", "total_nodes": 6, "nodes": [{"name": "新浪新闻科技", "description": "新浪科技频道新闻采集", "target_type": "html", "list_url": "https://tech.sina.com.cn/", "url_pattern": "https://tech.sina.com.cn/(*)", "url_include": "tech.sina.com.cn", "url_exclude": ".jpg,.png,.gif,.pdf", "start_mark": "<div class=\"feed-card-item\">", "end_mark": "<div class=\"pagination\">", "encoding": "utf-8", "interval": 60, "max_pages": 10, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "<h1>", "end_mark": "</h1>", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章正文", "start_mark": "<div class=\"article\">", "end_mark": "</div>", "required": true, "sort_order": 2}, {"field_type": "author", "field_label": "文章作者", "start_mark": "<span class=\"author\">", "end_mark": "</span>", "required": false, "sort_order": 3}, {"field_type": "source", "field_label": "文章来源", "start_mark": "来源：", "end_mark": "&nbsp;", "required": false, "sort_order": 4}, {"field_type": "time", "field_label": "发布时间", "start_mark": "<span class=\"time\">", "end_mark": "</span>", "required": false, "sort_order": 5}]}, {"name": "网易新闻科技", "description": "网易科技频道新闻采集", "target_type": "html", "list_url": "https://tech.163.com/", "url_pattern": "https://tech.163.com/(*)", "url_include": "tech.163.com", "url_exclude": "", "start_mark": "<div class=\"n-list\">", "end_mark": "<div class=\"pagination\">", "encoding": "utf-8", "interval": 60, "max_pages": 10, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "<h1 class=\"post_title\">", "end_mark": "</h1>", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章正文", "start_mark": "<div class=\"post_body\">", "end_mark": "</div>", "required": true, "sort_order": 2}, {"field_type": "author", "field_label": "文章作者", "start_mark": "<div class=\"ep-editor\">", "end_mark": "</div>", "required": false, "sort_order": 3}, {"field_type": "source", "field_label": "文章来源", "start_mark": "<div class=\"ep-source\">", "end_mark": "</div>", "required": false, "sort_order": 4}, {"field_type": "time", "field_label": "发布时间", "start_mark": "<div class=\"post_time_source\">", "end_mark": "</div>", "required": false, "sort_order": 5}]}, {"name": "腾讯新闻科技", "description": "腾讯科技频道API接口采集", "target_type": "interface", "list_url": "https://news.qq.com/api/tech.js", "url_pattern": "https://news.qq.com/api/tech_(*).js", "url_include": "news.qq.com", "url_exclude": "", "start_mark": "datacallback(", "end_mark": ");", "encoding": "utf-8", "interval": 30, "max_pages": 5, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "\"title\":\"", "end_mark": "\",", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章摘要", "start_mark": "\"digest\":\"", "end_mark": "\",", "required": true, "sort_order": 2}, {"field_type": "source", "field_label": "文章来源", "start_mark": "\"source\":\"", "end_mark": "\",", "required": false, "sort_order": 3}, {"field_type": "time", "field_label": "发布时间", "start_mark": "\"time\":\"", "end_mark": "\",", "required": false, "sort_order": 4}]}, {"name": "搜狐新闻科技", "description": "搜狐IT频道多页面采集", "target_type": "multiple", "list_url": "https://it.sohu.com/", "url_pattern": "https://it.sohu.com/page_(*).html", "url_include": "it.sohu.com", "url_exclude": "", "start_mark": "<div class=\"news-item\">", "end_mark": "<div class=\"page-box\">", "encoding": "utf-8", "interval": 90, "max_pages": 15, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "<h4><a", "end_mark": "</a></h4>", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章正文", "start_mark": "<div class=\"content\">", "end_mark": "</div>", "required": true, "sort_order": 2}, {"field_type": "source", "field_label": "文章来源", "start_mark": "<span class=\"source\">", "end_mark": "</span>", "required": false, "sort_order": 3}, {"field_type": "time", "field_label": "发布时间", "start_mark": "<span class=\"time\">", "end_mark": "</span>", "required": false, "sort_order": 4}]}, {"name": "凤凰新闻科技", "description": "凤凰科技频道新闻采集", "target_type": "html", "list_url": "https://tech.ifeng.com/", "url_pattern": "https://tech.ifeng.com/(*)", "url_include": "tech.ifeng.com", "url_exclude": ".jpg,.png,.gif", "start_mark": "<div class=\"news_list\">", "end_mark": "<div class=\"page_nav\">", "encoding": "utf-8", "interval": 120, "max_pages": 8, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "<h3><a", "end_mark": "</a></h3>", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章正文", "start_mark": "<div class=\"main_content\">", "end_mark": "</div>", "required": true, "sort_order": 2}, {"field_type": "source", "field_label": "文章来源", "start_mark": "<span class=\"ss01\">", "end_mark": "</span>", "required": false, "sort_order": 3}, {"field_type": "time", "field_label": "发布时间", "start_mark": "<span class=\"ss03\">", "end_mark": "</span>", "required": false, "sort_order": 4}]}, {"name": "火鸟门户测试", "description": "基于官方文档的火鸟门户测试采集", "target_type": "html", "list_url": "https://ihuoniao.cn/sz/article/yule/", "url_pattern": "https://ihuoniao.cn/sz/article/yule/?page=(*)", "url_include": "article", "url_exclude": ".jpg", "start_mark": "<div class=\"n-list\">", "end_mark": "<div class=\"pagination\">", "encoding": "utf-8", "interval": 60, "max_pages": 5, "rules": [{"field_type": "title", "field_label": "文章标题", "start_mark": "<div class=\"wmain\">", "end_mark": "&nbsp;&nbsp;作者：", "required": true, "sort_order": 1}, {"field_type": "content", "field_label": "文章正文", "start_mark": "<div class=\"wmain\">", "end_mark": "<div class=\"rewardS\">", "required": true, "sort_order": 2}, {"field_type": "source", "field_label": "文章来源", "start_mark": "来源：", "end_mark": "&nbsp;&nbsp;作者：", "required": false, "sort_order": 3}, {"field_type": "author", "field_label": "文章作者", "start_mark": "&nbsp;&nbsp;作者：", "end_mark": "&nbsp;&nbsp;浏览", "required": false, "sort_order": 4}, {"field_type": "time", "field_label": "发布时间", "start_mark": "<div class=\"tit-bar fn-clear\">", "end_mark": "&nbsp;&nbsp;浏览", "required": false, "sort_order": 5}]}]}}