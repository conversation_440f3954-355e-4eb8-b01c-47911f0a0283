<?php
/**
 * 火鸟门户系统采集插件独立定时任务脚本 - 测试版本
 * 用于宝塔面板计划任务或独立cron调用
 * 
 * @version 1.0 (测试版)
 * @usage php test_cron_runner.php [--dry-run] [node_id]
 */

// 设置字符集和错误报告
header('Content-Type: text/html; charset=utf-8');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置执行时间限制
set_time_limit(300); // 5分钟
ignore_user_abort(true);

echo "🔥 火鸟门户系统采集插件定时任务执行器 (测试版)\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n";
echo "==========================================\n\n";

// 解析命令行参数
$dry_run = false;
$target_node_id = null;

if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--dry-run') {
            $dry_run = true;
        } elseif (is_numeric($arg)) {
            $target_node_id = (int)$arg;
        }
    }
}

if ($dry_run) {
    echo "🧪 DRY-RUN 模式: 仅模拟执行，不进行实际采集\n\n";
}

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'hawaiihub_test',
    'charset' => 'utf8mb4'
];

try {
    // 创建数据库连接
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
        ]
    );
    
    echo "✅ 数据库连接成功\n";
    
    // 动态检测表前缀
    $table_prefix = '';
    $test_tables = ['hn_site_plugins', 'huoniao_site_plugins'];
    
    foreach ($test_tables as $test_table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$test_table'");
        if ($stmt->rowCount() > 0) {
            $table_prefix = str_replace('site_plugins', '', $test_table);
            break;
        }
    }
    
    if (empty($table_prefix)) {
        throw new Exception("无法检测数据库表前缀");
    }
    
    echo "📋 检测到表前缀: {$table_prefix}\n";
    
    // 动态获取插件ID
    $plugin_sql = "SELECT pid FROM `{$table_prefix}site_plugins` WHERE `title` LIKE '%采集%' OR `title` LIKE '%资讯%' LIMIT 1";
    $stmt = $pdo->query($plugin_sql);
    $plugin_data = $stmt->fetch();
    
    if (!$plugin_data) {
        throw new Exception("未找到采集插件，请先安装采集插件");
    }
    
    $plugin_id = $plugin_data['pid'];
    echo "🔌 检测到插件ID: {$plugin_id}\n";
    
    // 检查插件目录
    $plugin_dir = dirname(__FILE__) . "/include/plugins/{$plugin_id}";
    $cron_file = $plugin_dir . "/cron.php";
    
    if (!is_dir($plugin_dir)) {
        echo "⚠️  插件目录不存在: {$plugin_dir}\n";
        echo "📁 模拟插件目录结构...\n";
    } else {
        echo "📁 插件目录: {$plugin_dir}\n";
    }
    
    if (!file_exists($cron_file)) {
        echo "⚠️  cron.php文件不存在: {$cron_file}\n";
        echo "📄 模拟cron.php执行...\n";
    } else {
        echo "📄 cron.php文件: {$cron_file}\n";
    }
    
    // 获取需要执行的采集节点
    $where_clause = "WHERE status = 1";
    $params = [];
    
    if ($target_node_id) {
        $where_clause .= " AND id = :node_id";
        $params['node_id'] = $target_node_id;
        echo "🎯 指定执行节点ID: {$target_node_id}\n";
    }
    
    $nodes_sql = "SELECT id, name, target_type, list_url, last_run_time, interval_time 
                  FROM `{$table_prefix}collection_nodes` {$where_clause} 
                  ORDER BY last_run_time ASC";
    
    $stmt = $pdo->prepare($nodes_sql);
    $stmt->execute($params);
    $nodes = $stmt->fetchAll();
    
    if (empty($nodes)) {
        echo "📭 没有找到可执行的采集节点\n";
        exit(0);
    }
    
    echo "\n🚀 开始轮巡采集节点...\n";
    echo "==========================================\n";
    
    $executed_count = 0;
    $skipped_count = 0;
    $current_time = time();
    
    foreach ($nodes as $node) {
        echo "\n📝 检查节点: {$node['name']} (ID: {$node['id']})\n";
        echo "   类型: {$node['target_type']}\n";
        echo "   URL: {$node['list_url']}\n";
        
        // 检查是否到了执行时间
        $interval = $node['interval_time'] ?: 3600; // 默认1小时
        $next_run_time = ($node['last_run_time'] ?: 0) + $interval;
        
        if ($current_time < $next_run_time && !$target_node_id) {
            $wait_minutes = ceil(($next_run_time - $current_time) / 60);
            echo "   ⏰ 还需等待 {$wait_minutes} 分钟才能执行\n";
            $skipped_count++;
            continue;
        }
        
        echo "   ✅ 满足执行条件\n";
        
        if ($dry_run) {
            echo "   🧪 DRY-RUN: 模拟执行采集任务\n";
            echo "   📊 模拟采集结果: 成功采集 3 篇文章\n";
        } else {
            echo "   🔄 执行采集任务...\n";
            
            // 模拟执行cron.php
            $cmd = "php {$cron_file} nid={$node['id']} limit=3";
            echo "   💻 执行命令: {$cmd}\n";
            
            // 这里在实际环境中会执行真正的采集
            echo "   📊 模拟采集结果: 成功采集 3 篇文章\n";
            
            // 更新最后执行时间
            $update_sql = "UPDATE `{$table_prefix}collection_nodes` SET last_run_time = :time WHERE id = :id";
            $stmt = $pdo->prepare($update_sql);
            $stmt->execute(['time' => $current_time, 'id' => $node['id']]);
        }
        
        $executed_count++;
        
        // 避免过于频繁的执行
        if (!$target_node_id) {
            sleep(2);
        }
    }
    
    echo "\n🎉 定时任务执行完成！\n";
    echo "==========================================\n";
    echo "✅ 执行节点: {$executed_count} 个\n";
    echo "⏰ 跳过节点: {$skipped_count} 个\n";
    echo "🕐 总耗时: " . (time() - $current_time) . " 秒\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ 定时任务脚本执行完成！\n";
?>
